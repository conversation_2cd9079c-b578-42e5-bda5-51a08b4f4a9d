# -*- coding: utf-8 -*-
# @Time    : 2023/8/25 14:38
# <AUTHOR> hzx1994
# @File    : proxy.py
# @Software: PyCharm
# @description:


from controllers.user import MyAP<PERSON><PERSON>eyHead<PERSON>, add_user
from exections import ComstomException
from models.db.proxy import T<PERSON>proyalOrder, TIps, TProxyIp, TProxyMachines,TAppVersion,TAppVersionControl, TUser
from models.user import UserIn, UserIn_Body, UserOut
from utils import Tools, get_partner_share_user_info, ip2int,ip2hex,cache, notify_partner_share_user_bind
import hashlib
from tortoise import Tortoise
import time
from datetime import datetime, timedelta
import json

async def update_iproyal_ip_func(old_ip:str,iproyal_url:str,order_id:int):
    ip_obj = await TIps.filter(ip=old_ip).first()
    new_ip,_,username,password = iproyal_url.split(":")
    if ip_obj:
        if exist_ip_obj := await TIps.filter(ip=new_ip).first():
            ip_obj = exist_ip_obj
        else:
            ip_obj.ip = new_ip
            ip_obj.ip_addr = ip2int(new_ip)
            ip_obj.ip_address = ip2hex(new_ip)
            await ip_obj.save()
    else:
        raise ComstomException(detail="ip not found")
    
    proxys = await TProxyIp.filter(ip_id=ip_obj.id)
    for proxy in proxys:
        await TProxyMachines.filter(id=proxy.pm_id).update(ip_id=ip_obj.id)

        proxy.username = username
        proxy.password = password
        proxy.status = 1
        proxy.health = 1
        proxy.online = 1
        await proxy.save()
        await TIproyalOrder.filter(pi_id=proxy.id).update(expired=0,order_id=order_id)
    return True



async def upload_app(upload_file,version:str,platform='Android',depict:str=None):
    from controllers.gpt import upload_blob
   
    # 把版本号转换成内部版本code
    def version2code(version):
        version = version.split(".")
        num = 0
        for index,value in enumerate(version[::-1]):
            value = int(value)
            value*=10**(index)
            num+=value
        return num

    prefix = f"app"
    data = await upload_file.read()
    url = await upload_blob(data, file_name=upload_file.filename, pre=prefix)
    file_size = len(data)
    file_md5 = hashlib.md5(data).hexdigest()

    await TAppVersion.create(package=upload_file.filename,version_name=version
    ,version_code=version2code(version),platform=platform,file_size=file_size,depict=depict
    ,file_md5=file_md5,download_url=url)
    return True

@cache(ttl=60*5)
async def get_app_version_func(platform='Android',lang='zh'):
    record = await TAppVersionControl.filter(platform=platform).first()
    min_version_id = record.min_version_id
    min_version = await TAppVersion.filter(id=min_version_id).first().values("version_name")

    recommend_version_id = record.recommend_version_id
    recommend_version = await TAppVersion.filter(id=recommend_version_id).first().values("version_name")

    latest = await TAppVersion.filter(platform=platform).order_by("-version_code").first().values("download_url")
    return {
        "min_version":min_version.get("version_name"),
        "recommend_version":recommend_version.get("version_name"),
        "latest_download_url":latest.get("download_url")
    }
    

    
async def login_partner_share(code:str,register_ip:str,ref:str=''):
    user_info = await get_partner_share_user_info(code,Tools.config.adswave.get("product_id"),Tools.config.adswave.get("host",""),Tools.config.adswave.get("secret_key",""))
    if not user_info:
        return {}
    if user_info and user_info.get("user_key"):
        user_id = user_info.get("user_key")
        user = await TUser.filter(user_id=user_id).first().values("uid", "email","name", "password", "salt", "created_on", "is_use","deleted_on")
        if user:
            await notify_partner_share_user_bind(user_id,user.get("uid"),Tools.config.adswave.get("product_id"),Tools.config.adswave.get("host",""),Tools.config.adswave.get("secret_key",""))
            user = UserOut(**user)
            token = await MyAPIKeyHeader.cache_key(dict(user))
            return {"token":token}
    # 注册用户 绑定授权码
    name = user_info.get("user_name")
    user_key = user_info.get("user_key")
    user = UserIn_Body(email="",user_id=user_key,name=name,password=user_key,from_invite_code="",ref=ref,captcha='123456',code='123456')
    created = await add_user(user, user.from_invite_code or user.ref, register_ip,is_gpt=0,check=False)
    
    print(created)
    if created:
        user = UserOut(**{"uid":created.uid,"email":created.email,"name":created.name,"password":created.password,"salt":created.salt,"created_on":created.created_on,"is_use":created.is_use,"deleted_on":created.deleted_on})
        await notify_partner_share_user_bind(user_key,created.uid,Tools.config.adswave.get("product_id"),Tools.config.adswave.get("host",""),Tools.config.adswave.get("secret_key",""))
        token = await MyAPIKeyHeader.cache_key(dict(user))
        return {"token":token}
    else:
        return {}


async def get_amount_record():
    """
    获取代理商的金额统计记录
    从1606988127开始，每10天统计一次数据，存储在redis中
    如果redis中没有数据，则从mysql中获取并更新到redis
    """
    REDIS_KEY = "amount_record_stats"
    TEN_DAYS = 10 * 24 * 60 * 60  # 10天的秒数
    start_timestamp = 1605498791
    current_timestamp = int(time.time())
    
    # 检查Redis中是否有数据
    conn = Tortoise.get_connection("default")
    result_data = {}
    diff = int(time.time()) - current_timestamp
    # 按10天间隔统计数据
    current_start = start_timestamp
    while current_start < current_timestamp:
        s = await Tools.redis.hgetall(REDIS_KEY)

        if s:
            current_start = int(s.get(b"timestamp"))
        else:
            current_start = start_timestamp
        diff = int(time.time()) - current_start
        if diff < TEN_DAYS:
            TEN_DAYS = diff
        current_end = current_start + TEN_DAYS
        
        # 查询该时间段的数据
        query = f"""
            SELECT 
                SUM(receive_currency) as total_currency,
                sum(case when text = 'from gpt302' then receive_currency else 0 end) as ai_total_currency,
                sum(case when text != 'from gpt302' then receive_currency else 0 end) as proxy_total_currency,
                SUM(CASE WHEN is_inner = 0 AND currency_type not in  ('','IP') THEN value ELSE 0 END) as total_value,
                SUM(CASE WHEN  text = 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN value ELSE 0 END) as ai_total_value,
                SUM(CASE WHEN  text != 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN value ELSE 0 END) as proxy_total_value,
                SUM(CASE WHEN is_inner = 0 AND currency_type not in  ('','IP') THEN extra_value ELSE 0 END) as total_extra_value,
                SUM(CASE WHEN  text = 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN extra_value ELSE 0 END) as ai_total_extra_value,
                SUM(CASE WHEN  text != 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN extra_value ELSE 0 END) as proxy_total_extra_value,

                SUM(CASE WHEN is_inner = 0 AND currency_type not in  ('','IP') THEN amount ELSE 0 END) as not_inner_amount,
                SUM(CASE WHEN  text = 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN amount ELSE 0 END) as ai_not_inner_amount,
                SUM(CASE WHEN  text != 'from gpt302' and is_inner = 0 AND currency_type not in  ('','IP') THEN amount ELSE 0 END) as proxy_not_inner_amount,
                SUM(CASE WHEN is_inner = 1 AND currency_type not in  ('') THEN amount ELSE 0 END) as inner_amount,
                SUM(CASE WHEN  text = 'from gpt302' and is_inner = 1 AND currency_type not in  ('') THEN amount ELSE 0 END) as ai_inner_amount,
                SUM(CASE WHEN  text != 'from gpt302' and is_inner = 1 AND currency_type not in  ('') THEN amount ELSE 0 END) as proxy_inner_amount
            FROM t_ip_orders 
            WHERE deleted_on = 0 
                AND status = 1 
                AND created_on >= {current_start} AND  created_on < {current_end}
        """


        
        stats = await conn.execute_query_dict(query)
        if stats and stats[0]:
            

            current_data = {
                'timestamp': current_end,
                "total_currency": float(stats[0]['total_currency'] or 0),
                "ai_total_currency": float(stats[0]['ai_total_currency'] or 0),
                "proxy_total_currency": float(stats[0]['proxy_total_currency'] or 0),
                'total_value': float(stats[0]['total_value'] or 0),
                'ai_total_value': float(stats[0]['ai_total_value'] or 0),
                'proxy_total_value': float(stats[0]['proxy_total_value'] or 0),
                'total_extra_value': float(stats[0]['total_extra_value'] or 0),
                'ai_total_extra_value': float(stats[0]['ai_total_extra_value'] or 0),
                'proxy_total_extra_value': float(stats[0]['proxy_total_extra_value'] or 0),
                'not_inner_amount': float(stats[0]['not_inner_amount'] or 0),
                'ai_not_inner_amount': float(stats[0]['ai_not_inner_amount'] or 0),
                'proxy_not_inner_amount': float(stats[0]['proxy_not_inner_amount'] or 0),
                'inner_amount': float(stats[0]['inner_amount'] or 0),
                'ai_inner_amount': float(stats[0]['ai_inner_amount'] or 0),
                'proxy_inner_amount': float(stats[0]['proxy_inner_amount'] or 0),
            }
            if s:
                current_data['total_value'] = float(s.get(b'total_value',0)) + float(current_data['total_value'])
                current_data['ai_total_value'] = float(s.get(b'ai_total_value',0)) + float(current_data['ai_total_value'])
                current_data['proxy_total_value'] = float(s.get(b'proxy_total_value',0)) + float(current_data['proxy_total_value'])
                current_data['total_currency'] = float(s.get(b'total_currency',0)) + float(current_data['total_currency'])
                current_data['ai_total_currency'] = float(s.get(b'ai_total_currency',0)) + float(current_data['ai_total_currency'])
                current_data['proxy_total_currency'] = float(s.get(b'proxy_total_currency',0)) + float(current_data['proxy_total_currency'])
                current_data['total_extra_value'] = float(s.get(b'total_extra_value',0)) + float(current_data['total_extra_value'])
                current_data['ai_total_extra_value'] = float(s.get(b'ai_total_extra_value',0)) + float(current_data['ai_total_extra_value'])
                current_data['proxy_total_extra_value'] = float(s.get(b'proxy_total_extra_value',0)) + float(current_data['proxy_total_extra_value'])
                current_data['not_inner_amount'] = float(s.get(b'not_inner_amount',0)) + float(current_data['not_inner_amount'])
                current_data['ai_not_inner_amount'] = float(s.get(b'ai_not_inner_amount',0)) + float(current_data['ai_not_inner_amount'])
                current_data['proxy_not_inner_amount'] = float(s.get(b'proxy_not_inner_amount',0)) + float(current_data['proxy_not_inner_amount'])
                current_data['inner_amount'] = float(s.get(b'inner_amount',0)) + float(current_data['inner_amount'])
                current_data['ai_inner_amount'] = float(s.get(b'ai_inner_amount',0)) + float(current_data['ai_inner_amount'])
                current_data['proxy_inner_amount'] = float(s.get(b'proxy_inner_amount',0)) + float(current_data['proxy_inner_amount'])


            await Tools.redis.hset(REDIS_KEY, mapping=current_data)
        
    
    
    return result_data

