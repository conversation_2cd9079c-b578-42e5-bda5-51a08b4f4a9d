import hashlib
import json
import pickle
import time

import redis
from fastapi import APIRouter, Depends, Query, Body, Path, Form
from urllib import parse
import urllib

from fastapi.params import Header
from pydantic import EmailStr
from starlette.background import BackgroundTask, BackgroundTasks
from starlette.requests import Request
from starlette.responses import RedirectResponse
from tortoise.functions import Count
from controllers import user as user_controller
from controllers.pubilc import get_question_status_by_id, check_user_is_submit, test_user_action, check_user_not_gift
from controllers.register import Google, Phone, GithubAuth, GooglePwd
from controllers.user import band_email_func, get_user, get_current_active_user, get_user_balance, get_user_info, MyAPIKeyHeader, change_pw, add_user, \
    get_client_ip, check_user, change_user_name, mark_user, get_user_by_old_token, verify_captcha, is_in_db, \
    code_to_email, get_lang_from_req, alarm_balance_create_or_update, get_user_alarm_conf
from exections import raise_execption
from libs.Github import Github
from models.db.proxy import UserInfo
from models.proxy import Region
from models.response import BaseData, suc_data, fail_data, StateCode
from models.user import UserIn, UserLoginIn, UserOut, ResetPW, Nmae, UserRegion
from utils import encryption_password_or_decode, generate_invite_code, Tools, to_ptc, random_string, is_from_gpt, sms, \
    get_region
from models.user import UserIn, UserLoginIn, UserOut, ResetPW, Nmae, UserBody
from utils import encryption_password_or_decode, generate_invite_code, Tools, to_ptc, random_string, is_from_gpt

app = APIRouter()

@app.post("/ref/bind",summary="绑定ref来源")
async def bind_ref(request: Request,ref:str=Form(...,title="ref来源"),user: UserOut = Depends(get_current_active_user),code:str=Form(...,title="随机code"),captcha:str=Form(...,title="验证码")):
    if res := await verify_captcha(code,captcha,await get_client_ip(request),request.headers.get("user-agent")):
        return res
    is_gpt = is_from_gpt(request)
    if await user_controller.bind_ref(user.uid,ref,is_gpt=is_gpt):
        return suc_data()
    return fail_data(code=4201)


@app.get("/info", response_model=BaseData, summary="获取用户信息")
async def info(request: Request, user: UserOut = Depends(get_current_active_user)):
    """
    获取用户信息
    """
    st = time.time() * 1000
    ip = await get_client_ip(request)
    is_gpt = is_from_gpt(request)
    lang = request.get("lang")
    region = get_region(request.headers.get("referer", ""))
    Tools.log.debug(ip)
    user_info_dict = await get_user_info(user.uid,region,is_gpt=is_gpt)
    user_info_dict["balance"] = to_ptc(user_info_dict.get("balance"))
    user_info_dict["gpt_cost"] = to_ptc(user_info_dict.get("gpt_cost"))
    user_info_dict["user_name"] = user.name
    user_info_dict["email"] = user.email
    user_info_dict["uid"] = user.uid

    user_info_dict["is_new_user"] = user_info_dict.get("is_new_user")  # 新用户引导


    user_info_dict["question_switch"] = await get_question_status_by_id(user.uid,is_gpt)  # 问卷入口开关



    user_info_dict["show_questionnaire_windows"] = user.email!= '<EMAIL>' and await check_user_is_submit(user.uid,is_gpt=is_gpt,lang=lang)  # 弹窗开关
    user_info_dict["to_band_phone"] = not user_info_dict['phone'] and (not user_info_dict["question_switch"] and await check_user_not_gift(user.uid))  # 弹窗开关
    user_info_dict["question_switch"] = await check_user_not_gift(user.uid)

    ed = time.time() * 1000
    Tools.log.debug(f"cost_time:{ed - st}")
    return suc_data(data=user_info_dict)

import asyncio


# 删除用户
@app.delete("/delete", response_model=BaseData, summary="删除用户")
async def delete_user(request: Request,user: UserOut = Depends(get_current_active_user)):
    await user_controller.delete_user(user.uid)
    await MyAPIKeyHeader.del_key(request=request)
    return suc_data()

@app.post("/login", response_model=BaseData, summary="用户登陆接口")
async def login(request: Request,back:BackgroundTasks, user_params: UserLoginIn):
    "用户登陆接口，返回token"
    # 恶意攻击
    if user_params.email != "<EMAIL>":
        ip = await get_client_ip(request)

        if request.headers.get("proxy-authorization") or request.headers.get("https"):
            request.scope["to_mysql"] = False
            await asyncio.sleep(60)
            return fail_data(code=StateCode.FAIL)

        # 一个ip5秒登录一次
        ip_timeout_key = f"login_ip_timeout_{ip}"
        if await Tools.redis.exists(ip_timeout_key):
            return fail_data(code=StateCode.FAIL)
        await Tools.redis.setex(ip_timeout_key,1,5)
        # 验证码缓存key
        if res := await verify_captcha(user_params.code,user_params.captcha,ip,request.headers.get("user-agent")):
            return res
        key = None
    else:
        key = "Basic WMRNqanEmVTGcAMktDjqS=="


    back.add_task(mark_user,None, email=user_params.email, job=user_params.event, ref=user_params.ref)
    user = await get_user(email=user_params.email,phone=user_params.phone.replace(' ',''), password=user_params.password)
    Tools.log.info(f"user:{user_params.email} login headers :{request.headers}")
    if not user:
        if await Tools.redis.get(f"REGISTER_EMAIL_{user_params.email}"):
            return fail_data(code=StateCode.USER_NEED_ACTIVE)
        return fail_data(code=StateCode.PW_NOT_MATCH)
    token = await MyAPIKeyHeader.cache_key(dict(user),key=key)
    return suc_data(data={"token": token})

@app.get("/user/token/{uid}",summary="获取用户token")
async def get_user_token(uid:int=Path(...,title="用户ID")):
    user = await get_user(uid=uid,need_pwd=False)
    if not user:
        return fail_data(code=StateCode.PW_NOT_MATCH)
    token = await MyAPIKeyHeader.cache_key(dict(user),key=None)     
    return suc_data(data={"token": token})

@app.get("/to_new/{token}")
async def to_new(token:str=Path(...)):
    user = await get_user_by_old_token(token)
    if not user:
        return fail_data(code=StateCode.UserExistError)
    token = await MyAPIKeyHeader.cache_key(dict(user))
    url = f"https://{Tools.config.dashboard_host}/anonymous?token={urllib.parse.quote(token)}"
    Tools.log.debug(url)
    return RedirectResponse(url=url,status_code=302)



@app.post("/reset_pw")
async def reset_pw(request: Request, email: EmailStr = Form(..., description="要重置的邮箱"),code=Form(...,description="验证码code"),
                   captcha=Form(...,description="验证码"),
                   ):
    """
    重置密码，发送邮件
    """
    region = get_region(request.headers.get("referer"))
    lang = await get_lang_from_req(request)
    is_gpt= is_from_gpt(request)
    if res := await verify_captcha(code,captcha,await get_client_ip(request),request.headers.get("user-agent")):
        return res
    host = request.base_url
    host = str(host)
    if host.endswith("/"):
        host = host[:-1]
    credential = await user_controller.reset_pw(email)
    if not credential:
        return fail_data(code=StateCode.USER_NOT_EXIST)
    host = Tools.config.gpt_dashboard_host if is_gpt else Tools.config.dashboard_host
    if region == 0:
        host = Tools.config.gpt_dashboard_cn_host
    url = f"https://{host}/reset-password/{credential}"
    # url = Tools.config.reset_url.format(credential=credential)
    if is_gpt:
        email_obj = Tools.email_for_gpt
    else:
        email_obj = Tools.email
    email_obj.sendResetPasswordEmail(to_user_name=email, to=email, token_url=url,
                                       lang=lang,is_gpt=is_gpt)
    return suc_data()


@app.put("/name", summary="修改用户名称")
async def change_name(request: Request, name: Nmae = Body(..., description="新的名字"),
                      user: UserOut = Depends(get_current_active_user)):
    if res := await test_user_action(user):
        return res
    auth_token = request.headers.get("Authorization")
    await change_user_name(user.uid, name.name, auth_token)
    return suc_data()


@app.get("/reset_pw/{credential}", summary="重置密码的链接，需要改重定向到前端的页面")
async def check_credential(credential: str = Path(..., description="邮箱的base64编码")):
    """
    验证邮箱对应的链接，创建用户
    """
    if await user_controller.reset_pw(credential):
        #  重定向到成功的页面去重置密码
        return suc_data()
    #  重定向到一个错误页面，提示用户链接失效
    return fail_data(code=StateCode.LINK_EXPIRED.value)


@app.put("/reset_pw/{credential}", summary="修改密码")
async def reset_pw(credential: str = Path(..., description="邮箱的base64编码"),
                   password: ResetPW = Body(..., title="新密码")):
    await user_controller.reset_pw(credential=credential, password=password.password)
    return suc_data()


@app.put("/region")
async def change_region(request: Request,region:UserRegion,user: UserOut = Depends(get_current_active_user)):
    domain = await user_controller.save_region(region.region,user.uid)
    return suc_data(data={"domain":domain})

@app.put("/resource_area",summary="资源归属地")
async def change_resource_area(request: Request,resource_area:int=Body(0),extra=Body(""),user: UserOut = Depends(get_current_active_user)):
    await user_controller.save_resource_area(resource_area,user.uid)
    return suc_data()


@app.put("/pw", summary="修改密码", response_model=BaseData)
async def set_password(request: Request, origin_password=Body(..., title="原始密码"),
                       change_password=Body(..., title="修改后的密码"),
                       user: UserOut = Depends(get_current_active_user)):
    """
    修改密码

    """
    if res := await test_user_action(user):
        return res
    if await change_pw(user.uid, origin_password, change_password):
        return suc_data()
    return fail_data(code=StateCode.PW_NOT_MATCH.value)


@app.get("/verify/register/{key}", summary="真实的注册接口", response_model=BaseData)
async def verify(request: Request, key=Path(...)):
    """
    验证邮件对应的链接，创建用户
    """
    is_gpt = is_from_gpt(request)

    register_ip = await get_client_ip(request)

    register_ip_log = f"proxy302_register_ip_{register_ip}"
    await Tools.redis.incr(register_ip_log, 1)
    await Tools.redis.expire(register_ip_log, 60 * 10)

    host = Tools.config.dashboard_host
    Tools.log.debug(request.headers)
    if is_gpt:
        if 'cn' in request.url.netloc:
            host = Tools.config.gpt_dashboard_cn_host
        else:
            host = Tools.config.gpt_dashboard_host


    value = await Tools.redis.get("REGISTER_"+key)
    if await is_in_db(register_ip):
        host = f"https://{host}/register"
        return RedirectResponse(host, status_code=302)

    if not value:
        host = f"https://{host}/register"
        return RedirectResponse(host, status_code=302)
    await Tools.redis.delete("REGISTER_"+key)
    value = json.loads(value.decode())
    user = UserIn(**value)
    if value := await Tools.redis.get(register_ip_log):
        if int(value) > 10:
            Tools.feishu1.sendText("====注册异常提示====\n"
                                   f"注册IP：{register_ip}，用户邮箱：{user.email}，请核实是否恶意注册<at user_id=\"all\">所有人</at>")

        await Tools.redis.delete(register_ip_log)
    created = await add_user(user, user.from_invite_code or user.ref, register_ip,is_gpt=is_gpt)
    if created:
        _ = await UserInfo.get(uid=created.uid).values("region")
        region = _.get("region")
        if is_gpt and region == 0:
            host = Tools.config.gpt_dashboard_cn_host
            if 'cn' not in request.url.netloc:
                host = Tools.config.gpt_dashboard_host
    if created:
        ts = int(time.time()*1000)
        host = f"https://{host}/login?activate=true&uid{created.uid}&timestamp={ts}"
    else:
        host = f"https://{host}/register"

    return RedirectResponse(host, status_code=302)

@app.post("/register/phone",summary="手机号注册", response_model=BaseData)
async def register_from_phone(request: Request,id_token:str=Form(""),name=Form("",title="用户名"),password=Form(...,title="密码"),from_invite_code=Form(""),
                              confirmPassword=Form(...,title="密码校验"),ref=Form(""),sms_code:str= Form("",title="短信验证码"),phone_number:str=Form("",title="手机号")):
    if confirmPassword != password:
        return fail_data()
    register_ip = await get_client_ip(request)
    lang = await get_lang_from_req(request)
    is_gpt = is_from_gpt(request)
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''

    region = get_region(refer_host)

    auth = Phone(register_ip=register_ip,ref=ref or from_invite_code,is_gpt=is_gpt,name=name,password=password,sms_code=sms_code,phone_number=phone_number,region=region,lang=lang)
    token = await auth.start(id_token=id_token,login=False)
    if token:
        return suc_data({"token":token})
    return fail_data()


@app.post("/bind_phone", summary="手机号绑定", response_model=BaseData)
async def bind_phone(request: Request,id_token:str=Body(""),phone_number:str=Body(...,title="手机号"),sms_code:str=Body(...,title="短信验证码"),user: UserOut = Depends(get_current_active_user)):
    register_ip = await get_client_ip(request)
    zh = request.headers.get("accept-language") and request.headers.get("accept-language").startswith("zh")
    is_gpt = is_from_gpt(request)
    auth = Phone(register_ip=register_ip,is_gpt=is_gpt,zh=zh,phone_number=phone_number,sms_code=sms_code,user_id=user.uid)
    await auth.phone_exists()
    await auth.get_user_info(id_token=id_token)
    await auth.bind_or_change_phone(phone_number.replace(" ",""))
    return suc_data()


@app.post("/sms/rny",summary='获取容联云手机验证码')
async def get_sms_code(request: Request,mobile:str=Body(...,title="手机号"),captcha:str=Body(...,title="验证码")
                       ,code:str=Body(...,title="验证码对应的code")):
    is_gpt = is_from_gpt(request)
    ip = await get_client_ip(request)
    if res := await verify_captcha(code, captcha, ip, request.headers.get("user-agent")):
        return res
    Tools.log.info(f"send sms : mobile:{mobile}, ip:{ip},captcha:{captcha},code:{code} ")
    if not await sms("set",mobile,is_gpt=is_gpt,ip=ip):
        return fail_data(msg="验证码获取太频繁",code=StateCode.CallFrequently.value)
    return suc_data()


@app.put("/reset_pw",summary="手机号重置密码", response_model=BaseData)
async def reset_from_phone(request: Request,id_token=Form("",title="token"),phone_number=Form("",title="手机号"),sms_code=Form(""),password=Form(...,title="密码")):
    """
    重置手机账号对应的密码
    """
    register_ip = await get_client_ip(request)
    zh = request.headers.get("accept-language") and request.headers.get("accept-language").startswith("zh")

    is_gpt = is_from_gpt(request)

    auth = Phone(register_ip=register_ip,password=password,is_gpt=is_gpt,zh=zh,phone_number=phone_number,sms_code=sms_code)
    token = await auth.get_user_info(id_token=id_token)
    if token and await auth.reset_password():
        return suc_data()
    return fail_data(code=StateCode.USER_NOT_EXIST)


@app.post("/login/phone",summary="手机号登录", response_model=BaseData)
async def login_from_phone(request: Request,user_params: UserLoginIn):
    ip = await get_client_ip(request)
    # 验证码缓存key
    if res := await verify_captcha(user_params.code, user_params.captcha, ip, request.headers.get("user-agent")):
        return res
    user = await get_user(phone=user_params.phone.replace(' ',''), password=user_params.password)
    if not user:
        return fail_data(code=StateCode.PW_NOT_MATCH)
    token = await MyAPIKeyHeader.cache_key(dict(user))
    return suc_data(data={"token": token})


@app.get("/register/github",summary="github 注册|登陆", response_model=BaseData)
async def register_from_github(request: Request,ref=Form(""),from_invite_code:str=Form("")):
    """
    github 登录
    """
    register_ip = await get_client_ip(request)
    lang = await get_lang_from_req(request)
    is_gpt = is_from_gpt(request)
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''

    region = get_region(refer_host)
    conf = Tools.config.ai302_github_conf if is_gpt else Tools.config.proxy_github_conf
    github = Github(conf.get("client_id"), conf.get("client_secret"))
    authorization_url,state = github.login()
    # info_dict['oauth_state'] = state
    # info_dict['ref'] = ref
    # info_dict['register_ip'] = register_ip
    # info_dict['lang'] = lang
    # info_dict['is_gpt'] = is_gpt
    
    # info_dict['region'] = region
    data = {"register_ip": register_ip, "ref": ref or from_invite_code, "is_gpt": is_gpt, "region": region}
    await Tools.redis.setex(f"oauth_state:{state}",600,json.dumps(data).encode())
    return suc_data(data={"url":authorization_url})

    return RedirectResponse(url=authorization_url)

@app.get("/oauth/github/callback",summary="github 回调")
async def github_callback(request: Request,state:str=Query(...,title="state")):
    info_dict = await Tools.redis.get(f"oauth_state:{state}")
    await Tools.redis.delete(state)
    info_dict = json.loads(info_dict.decode("utf-8"))
    ref = info_dict.get('ref')
    register_ip = info_dict.get('register_ip')
    lang = info_dict.get('lang')
    is_gpt = info_dict.get('is_gpt')
    region = info_dict.get('region')
    Tools.log.debug(f"info_dict:{info_dict}")
    url = request.url
    # url='http://localhost:8000/callback'

    auth = GithubAuth(register_ip=register_ip, ref=ref, is_gpt=is_gpt, region=region, lang=lang)
    token = await auth.start(request_url=url,state=state)
    if token:
        _,token = token.split(' ')
        url = (Tools.config.gpt_dashboard_host if is_gpt else Tools.config.dashboard_host)+f"/authentication/{token}"
        return RedirectResponse(url=("https://" if not url.startswith('http') else '') +url )
    return fail_data()

@app.post("/register/google",summary="谷歌验证码注册/登录", response_model=BaseData)
async def register_from_google(request: Request, id_token:str =Form(...),ref=Form(""),from_invite_code:str=Form("")):
    """

    """
    # 判断用户是否注册过，注册过直接充当登录接口
    # 帮用户注册账号（share也一起做了 create_user
    # 异步任务 随机密码邮件通知用户-告知重置密码
    register_ip = await get_client_ip(request)
    lang = await get_lang_from_req(request)
    is_gpt = is_from_gpt(request)
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''

    region= get_region(refer_host)
    auth = Google(register_ip=register_ip,ref=ref or from_invite_code,is_gpt=is_gpt,region=region,lang=lang)
    token = await auth.start(id_token=id_token)
    if token:
        return suc_data(data={"token": token})
    return fail_data()
@app.post("/login/google",summary="谷歌登录（指定对应的邮箱和对应的user_id强制登录）")
async def get_google_code(request: Request,email:str=Form(...,title="邮箱"),user_id:str=Form(...,title="user_id"),ref=Form("")):
    register_ip = await get_client_ip(request)
    lang = await get_lang_from_req(request)
    is_gpt = is_from_gpt(request)
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''

    region = get_region(refer_host)
    auth = GooglePwd(register_ip=register_ip, ref=ref, is_gpt=is_gpt, region=region, lang=lang,email=email,user_id=user_id)
    token = await auth.start()
    if token:
        return suc_data(data={"token": token})
    return fail_data()






@app.post("/register/email/code",summary='发送验证码到邮箱')
async def send_email_code(request: Request,email:EmailStr=Form(...,title="邮箱"),captcha:str=Form(...,title="验证码"),code=Form(...,title="获取验证码的code"),):
    is_gpt = is_from_gpt(request)
    if res := await verify_captcha(code,captcha,await get_client_ip(request),request.headers.get("user-agent")):
        return res
    lang = await get_lang_from_req(request)
    if lang.startswith('zh') or lang.startswith('cn'):
        is_zh = True
    else:
        is_zh = False
    await code_to_email(email=email,action="send",is_gpt=is_gpt,zh=is_zh)
    return suc_data()


@app.post("/register/email/verify_code",summary='验证邮箱验证码')
async def send_email_code(request: Request,email:EmailStr=Form(...,title="邮箱"),code:str=Form(...,title="验证码")):
    if await code_to_email(email=email,action="verify",code=code):
        return suc_data()
    return fail_data()

@app.post('/v1/register',summary='注册接口,给app用', response_model=BaseData)
@app.post('/v1/register/',summary='注册接口,给app用', response_model=BaseData)
async def register_from_app(request: Request,user:UserBody):
    register_ip = await get_client_ip(request)
    if not await code_to_email(email=user.email,action="verify",code=user.email_code):
        return fail_data(code=StateCode.CaptchaError.value)
    created = await add_user(user, user.ref, register_ip,is_gpt=user.is_gpt)
    if not created:
        return fail_data(code=StateCode.UserExistError.value)
    user = await get_user(uid=created.uid,need_pwd=False)
    token = await MyAPIKeyHeader.cache_key(dict(user))
    return suc_data({"token":token})


@app.post("/band/email/{uid}",summary="绑定邮箱")
async def band_email(request: Request,uid:int=Path(...,title="用户ID"),email:EmailStr=Form(...,title="邮箱"),code:str=Form(...,title="验证码")):
    if await code_to_email(email=email,action="verify",code=code):
        if await band_email_func(uid=uid,email=email):
            return suc_data()
        return fail_data(code=StateCode.UserExistError.value)
    return fail_data()


@app.post("/register", summary="注册接口，生成发送邮箱的激活链接", response_model=BaseData)
async def register(request: Request, user: UserIn = Depends()):
    """
    验证邮件对应的链接，创建用户，构建另一个接口的完整链接发送邮箱给用户
    """
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''
    lang = await get_lang_from_req(request)
    # zh = request.headers.get("accept-language") and request.headers.get("accept-language").startswith("zh")
    Tools.log.debug(request.headers)
    is_gpt = is_from_gpt(request) or bool(request.headers.get("Isgpt"))


    # is_gpt = True
    # zh=False
    if res := await verify_captcha(user.code,user.captcha,await get_client_ip(request),request.headers.get("user-agent")):
        return res
    if await check_user(user.email):
        return fail_data(msg="email is exist", code=StateCode.UserExistError.value)
    register_ip = await get_client_ip(request)
    if await is_in_db(register_ip):
        return fail_data(msg="register from vpn")
    host = request.base_url
    host = str(host)
    region = get_region(refer_host)
    if is_gpt and region == 0:
        host = "https://dash-cn-api.302.ai/"
    if host.endswith("/"):
        host = host[:-1]
    query = user.to_dict()
    query['is_gpt'] = is_gpt
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''

    query['region'] = get_region(refer_host)
    query['lang'] = lang
    key = "REGISTER_"+random_string(15)
    Tools.log.debug(key)
    await Tools.redis.setex(f"REGISTER_EMAIL_{user.email}",60*60*24,"1")
    await Tools.redis.setex(name=key, value=json.dumps(query), time=60 * 60 * 24)
    url = str(host) + f"/user/verify/register/{key.split('_')[1]}"
    Tools.log.debug(url)
    if is_gpt:
        email = Tools.email_for_gpt
    else:
        email = Tools.email
    email.sendVerificationEmail(to_user_name=user.email, to=user.email, verification_url=url,lang=lang,is_gpt=is_gpt)
    # 发邮箱
    return suc_data(msg="查看邮箱获取激活链接", data={"url": url})


@app.get("/redirect/home/<USER>", summary="重定向回官网")
async def redirect_home(salt: str = Path(..., description="salt"),event=Query("AnniversaryCelebration", title="事件"),ref=Query("Email", title="来源")):
    """
    重定向回官网
    """

    background = BackgroundTask(mark_user, salt,None,job=event,ref=ref)

    return RedirectResponse("https://www.proxy302.com/", status_code=302, background=background)


@app.post("/balance/alarm", summary="设置余额告警邮件")
@app.put("/balance/alarm", summary="修改余额告警邮件")
async def set_balance_alarm(request: Request, user: UserOut = Depends(get_current_active_user),
                            Lang: str = Header("zh-CN", title="语言"),
                             alarm_value: str = Body(..., title="告警邮箱"),
                             alarm_type: str = Body(..., title="告警类型"),
                             enable_alarm: int = Body(0, title="是否启用"),
                             alarm_balance: int = Body(1, title="告警余额")):
    if alarm_balance<1:
        return fail_data(msg="余额告警余额不能小于1")
    is_gpt = is_from_gpt(request)
    await alarm_balance_create_or_update(user.uid,alarm_balance,alarm_type,alarm_value,is_gpt,lang=Lang,enable_alarm=enable_alarm)
    return suc_data()

@app.get("/balance/alarm", summary="获取余额告警邮件")
async def get_balance_alarm(request: Request,user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    return suc_data(data=await get_user_alarm_conf(user.uid,is_gpt=is_gpt))

# 获取用户余额
@app.get("/v1/balance",summary="获取用户余额")
async def get_balance(request: Request,user: UserOut = Depends(get_current_active_user)):
    return suc_data(data=await get_user_balance(user.uid))