import requests
from typing import Dict, Any

ref = "TFtmDY"

# 处理用户邮箱或手机信息
_email = "<EMAIL>"
# 根据类型获取产品配置
product_id = 13329
conversion_token = "MC1KXVBQ7_QQWslxPlfkhC17BRnEED3M"

# 构建请求参数
params = {
    'conversion_token': conversion_token,
    'product_id': product_id,
    'ref': ref,
    'source_user_id': 246673,
    'transaction_id':  '1234567890',
    'total_money': 5,
    'start_time_plan': 1709222400,
    'cashback_mode': 2,
    'source_user_info': _email
}

# 发送请求并处理响应
try:
    response = requests.get(
        "https://api.partnershare.net/partner/conversion?conversion_type=2",
        params=params,
        timeout=5
    )
    response.raise_for_status()
    json_resp = response.json()
    print(json_resp)
    if json_resp["code"] != 200:
        print(f"partner_share charge api 响应错误: {json_resp}")
except requests.exceptions.RequestException as e:
    print(f"partner_share charge api 请求异常: {str(e)}")