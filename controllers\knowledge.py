# -*- coding: utf-8 -*-
# @Time    : 2024/6/20 16:22
# <AUTHOR> hzx1994
# @File    : knowledge.py
# @Software: PyCharm
# @description: 知识库逻辑处理
import asyncio
import random
import re
from pathlib import Path as pa
import json
import threading
import uuid
from io import BytesIO

import aiohttp
from fastapi import UploadFile
from nano_graphrag import GraphRAG, QueryParam
from nano_graphrag.prompt import PROMPTS
from tortoise.expressions import Subquery, RawSQL
from tortoise.functions import Count

import networkx as nx
import aiofiles
import utils
from exections import ComstomException
from libs.RagNano import get_llm_func, get_emb_func, MilvusStorge, fail_response
from models.db.gpt import *
from models.db.proxy import TUser
from models.gpt import GptToken, ToolsId, GPTStatus, InDB,KTasks
from models.response import suc_data, fail_data, StateCode
from utils import Tools, current_timestamp
import base64
WORKING_DIR = pa("/mnt").joinpath("azure").joinpath("kb").joinpath("rag_nano")
if not WORKING_DIR.exists():
    WORKING_DIR.mkdir(parents=True)

class StreamProcessor:
    def __init__(self):
        self.events = []

    def process_stream(self, stream_data):
        # 按行分割数据
        if isinstance(stream_data, tuple):
            stream_data = stream_data[0].strip()
        lines = stream_data.strip().split('\n')

        for line in lines:
            if line.startswith('data:'):
                # 提取 JSON 数据
                json_str = line[5:].strip()  # 移除 'data:' 前缀
                try:
                    event_data = json.loads(json_str)
                    if isinstance(event_data, dict):
                        if "tools" in event_data:
                            event_data['answer'] = "\n".join(event_data.pop("tools"))
                        elif "final_answer" in event_data:
                            event_data['answer'] = event_data.pop("final_answer")
                    self.events.append(event_data)
                except json.JSONDecodeError:
                    Tools.log.debug(f"Error decoding JSON: {json_str}")
                except Exception as e:
                    Tools.log.exception("other error")

    def get_events(self):
        return self.events

async def change_kb_info(kb_id,kb_name:str,kb_info,uid,**kwargs):
    # is_global = kwargs.get("is_global",False)
    kb = await TKnowledgeBase.filter(id=kb_id,uid=uid).first().values("kb_name",'type',"token_id")
    if not kb:
        return fail_data(msg="知识库不存在")
    if await TKnowledgeBase.filter(id__not=kb_id,kb_name=kb_name,uid=uid).exists():
        return fail_data(msg="知识库名称或简介有误",code=StateCode.XxNameExists)
    if len(kb_name.strip())==0:
        return fail_data(msg="知识库名称或简介有误", code=StateCode.XxNameExists)
    await TKnowledgeBase.filter(id=kb_id).update(kb_name=kb_name,kb_info=kb_info)
    if kb.get("type") == "rag_nano":
        await TTokenInfo.filter(token_id=kb.get("token_id")).update(settings=kwargs)
        kb_path = pa(WORKING_DIR) / str(uid) / kb.get('kb_name')
        rag = GraphRAG(
            working_dir=str(kb_path),
            vector_db_storage_cls=MilvusStorge
        )

        kb_path_new = pa(WORKING_DIR) / str(uid) / kb_name
        await rag.entities_vdb.rename(str(kb_path_new))
        kb_path.rename(kb_path_new)


    return suc_data()
async def create_get_kb_token(uid,kb_id=None,kb_name=''):
    from controllers.gpt import create_user_gpt_token
    if kb_id:
        if kb := await TKnowledgeBase.filter(id=kb_id,uid=uid,token_id__gt=0).first():
            return kb.token_id
    kb_name = kb_name+"_"+str(current_timestamp())
    _ = await TTokenMapping.filter(tool_id=ToolsId.KB_EMB.value, user_id=uid,status=2, name=kb_name).first().values("id")
    if _ and _.get("id"):
        return _.get("id")
    gpt = GptToken()
    gpt.tool_id = ToolsId.KB_EMB.value
    gpt.is_robot=0
    gpt.name=kb_name
    # if not await TTokenMapping.filter(user_id=body.get('uid'),status=2,tool_id=ToolsId.KB_EMB.value).exists():
    a = await create_user_gpt_token(uid, status=2,body=gpt)
    return a.get("id")

async def create_knowledge_base_func(**kwargs):
    async def create_rag_nano():
        kb_path = pa(WORKING_DIR) / str(body.get("uid",0)) / body.get('knowledge_base_name')
        if kb_path.exists():
            for i in kb_path.iterdir():
                i.unlink()
            kb_path.rmdir()
            # return fail_data()
        llm_model_name ='gpt-4o-mini'
        embed_model = "jina-clip-v1"
        dimension = 768
        model_id = body.get('model_id') or 0
        llm_model_id = body.get('llm_model_id') or 0
        token_id = await create_get_kb_token(body.get('uid'), kb_name=body.get('knowledge_base_name'))
        emb_model = await TModel.filter(id=model_id).first().values("name",'dimension')
        llm_model = await TModel.filter(id=llm_model_id).first().values("name")

        if llm_model:
            llm_model_name = llm_model.get("name")
        if emb_model:
            embed_model = emb_model.get("name")
            dimension = emb_model.get("dimension")

        obj = await TKnowledgeBase.create(kb_name=body.get("knowledge_base_name"),llm_model=llm_model_name,embed_model=embed_model
                                          ,kb_info=body.get("remark") or f'关于{body.get("knowledge_base_name")}的知识库'
                                          ,uid=body.get("uid"),type='rag_nano',token_id=token_id)

        GraphRAG(
            working_dir=str(kb_path),
            embedding_func=await get_emb_func(body.get("api_key"), base_url=body.get("base_url"),dimension=dimension,model=embed_model),
            vector_db_storage_cls=MilvusStorge

        )
        return suc_data(data=dict(obj))

    async def create_chatchat():
        model_id = body.get('model_id') or 163
        body['model_id'] = model_id
        async with aiohttp.ClientSession() as session:
            async with session.post(url=f"{Tools.config.kb.kb_url}/knowledge_base/create_knowledge_base",
                                    json=body) as resp:
                data = await resp.json()
        Tools.log.debug(data)
        if data.get("code") == 200:
            value = await TKnowledgeBase.filter(uid=body.get('uid'), kb_name=body.get('knowledge_base_name')).first()
            Tools.log.debug(dict(value))
            token_id = await create_get_kb_token(body.get('uid'), kb_name=body.get('knowledge_base_name'))
            await TKnowledgeBase.filter(uid=body.get('uid'), kb_name=body.get('knowledge_base_name')).update(
                token_id=token_id,type='chatchat')
            return suc_data(data=dict(value))
        else:
            code = -1
            if data.get("msg").startswith("已存在同名知识库"):
                code = 9000
            return fail_data(msg=data.get("msg"), code=code)



    body = kwargs['body']
    # 创建用户隐藏的apikey
    if body.get('knowledge_base_name') and len(body.get('knowledge_base_name').strip())==0:
        code = 9000
        return fail_data(msg="kb_name exists", code=code)


    if not body.get('knowledge_base_name'):
        kb_names = await TKnowledgeBase.filter(kb_name__startswith=f'知识库-{datetime.date.today().strftime("%Y%m%d")}-',
                                               uid=body.get('uid')).values("kb_name")
        names = [kb.get("kb_name") for kb in kb_names]
        name = ""
        for i in range(10000):
            name = f'知识库-{datetime.date.today().strftime("%Y%m%d")}-{i+1}'
            if name not in names:
                break

        # number = await TKnowledgeBase.filter(create_time__gt=datetime.date.today()).count()
        # name = await check_name_in_db(name,body.get('uid'))
        body['knowledge_base_name'] = name
        body['remark'] = body['remark'] or name
    if await TKnowledgeBase.filter(kb_name=body.get('knowledge_base_name'),uid=body.get('uid')).exists():
        code = 9000
        return fail_data(msg="kb_name exists",code=code)

    if body.get("kb_type") =='chatchat':
        return await create_chatchat()
    else:
        return await create_rag_nano()

async def get_knowledge_base_list_func(uid,page,page_size,filter_type,text):
    kw = {}
    if filter_type == 'name':
        kw['kb_name__icontains'] = text
    elif filter_type == 'remark':
        kw['kb_info__icontains'] = text
    elif filter_type == 'kb_type':
        kw['type'] = text
    query = TKnowledgeBase.filter(uid=uid,**kw)
    records = await query.order_by("-id").offset((page - 1) * page_size).limit(page_size).all()
    file_count = await TKnowledgeFile.filter(kb_base_id__in=[i.id for i in records],in_db=1).annotate(file_count=Count("id")).group_by("kb_base_id").values("kb_base_id","file_count")
    file_count_dict = {i.get('kb_base_id'):i.get('file_count') for i in file_count}
    total = await query.count()
    settings = await TTokenInfo.filter(token_id__in=[i.token_id for i in records]).values("settings","token_id")
    settings = {s.get("token_id"):s for s in settings}
    records = [dict(record) for record in records]
    for i in records:
        i['settings'] = settings.get(i.get("token_id"),{}).get("settings")
        i['file_count'] = file_count_dict.get(i.get("id"),0)
    return suc_data(data={"records":records,"total":total,'page':page,'page_size':page_size})



async def delete_knowledge_base_func(uid,kb_id):
    records = await TTokenInfo.filter(
        token_id__in=Subquery(TTokenMapping.filter(user_id=uid, tool_id=ToolsId.KB.value).values("id"))).values("extra")
    for record in records:
        extra = record.get("extra")
        if extra.get("kb_id") == kb_id:
            return fail_data(msg="当前知识库正在使用中", code=StateCode.LinkError)
    kb = await TKnowledgeBase.filter(id=kb_id).first().values("type",'kb_name')
    if not kb or kb.get("type",'chatchat') == 'chatchat':
        async with aiohttp.ClientSession() as session:
            async with session.post(url=f"{Tools.config.kb.kb_url}/knowledge_base/delete_knowledge_base", json={"uid":uid,"kb_id":kb_id}) as resp:
                data = await resp.json()
            if data.get("code") == 200:
                return suc_data()
            else:
                return fail_data(msg=data.get("msg"))
    else:
        await TKnowledgeBase.filter(id=kb_id).delete()
        await TKBFileToMilvus.filter(kb_id=kb_id).delete()
        Tools.log.debug(f"知识库索引表删除知识库id：{kb_id}")
        kb_path = pa(WORKING_DIR) / str(uid) / kb.get('kb_name')
        if kb_path.exists():
            for file in kb_path.iterdir():
                file.unlink()
            kb_path.rmdir()

        rag = GraphRAG(
            working_dir=str(kb_path),
            vector_db_storage_cls=MilvusStorge
        )

        await rag.entities_vdb.del_collection()
        return suc_data()




async def get_knowledge_base_info_func(uid,kb_id):
    """
    获取当前对应的知识库的信息
    """
    value = await TKnowledgeBase.filter(uid=uid,id=kb_id).first()
    _ = await TTokenInfo.filter(token_id=value.token_id).first().values("settings")
    value = dict(value) if value else {}
    value.update(_)
    return suc_data(data=value)


async def list_files_func(uid,kb_id,page,page_size):
    """
    返回知识库的文件列表
    """
    if not await TKnowledgeBase.filter(uid=uid,id=kb_id).exists():
        return []
    files = await TKBFiles.filter(kb_id=kb_id).all()
    blob_dict = {file.file_name:file.blob_url for file in files}
    query = TKnowledgeFile.filter(kb_base_id = kb_id).order_by("-id")
    total = await query.count()
    data = await query.order_by("-id").offset((page-1)*page_size).limit(page_size).all()
    records = []
    base = (page - 1) * page_size
    for index,i in enumerate(data):
        tmp = dict(i)
        origin_name = blob_dict.get(i.file_name,"")
        name = blob_dict.get(i.file_name.replace(".md",".pdf"), "")
        if name:
            tmp['file_name'] = i.file_name.replace(".md",'.pdf')
        records.append(tmp)
        tmp["blob_url"] = origin_name or name
        tmp["in_folder"] = True
        tmp['row_number'] = base+index+1
    return suc_data(data={"records":records,"total":total,'page':page,'page_size':page_size})


async def update_docs_fnc(file_names:List[str],kb_id:int,uid:int,split_mode='',separator='',max_chunk_length=4000,is_summarize=False,**kwargs):
    """
    更新指定的文件向量化
    已存在知识库
    """
    from controllers.gpt import upload_blob, get_content_types, get_text_summary
    __token = await TKnowledgeBase.filter(id=kb_id,uid=uid).first().values("token_id","type","kb_name")
    if not __token:
        yield "DONE"
        return

    _ = await TTokenMapping.filter(user_id=uid, id=__token.get("token_id")).select_related(
        "external_token").first().values("external_token__value")
    api_key = _.get("external_token__value")

    if __token.get("type") == 'rag_nano':
        await TKnowledgeFile.filter(kb_base_id=kb_id,file_name__in=file_names).update(in_db=InDB.WAIT.value)
        class File:
            def __init__(self,file_name):
                self.filename = file_name
        background_tasks = kwargs.get("background_tasks")
        background_tasks.add_task(upload_to_rag_backend([File(file_name) for file_name in file_names], kb_id, api_key, Tools.config.kb.base_url, __token.get("kb_name"), uid))
        # await upload_to_rag_backend([File(file_name) for file_name in file_names], kb_id, api_key, Tools.config.kb.base_url, __token.get("kb_name"), uid)
        return

    records = await TKBFiles.filter(file_name__in=file_names,kb_id=kb_id).values("blob_url","file_name")
    data = aiohttp.FormData()
    for record in records:
        if record.get("file_name").endswith('.pdf'):
            text = await to_md(record.get("blob_url"),api_key)
            file_content = text.encode("utf-8")
            record['file_name'] = record.get("file_name").replace(".pdf",".md")
            content_type = get_content_types(record.get("file_name"))
        else:
            async with aiohttp.ClientSession() as session:
                async with session.get(record.get("blob_url")) as resp:
                    file_content = await resp.content.read()
            content_type = get_content_types(record.get("file_name"))
        data.add_field("files", file_content, filename=record.get("file_name"), content_type=content_type)


    data.add_field("kb_id", str(kb_id))
    data.add_field("override", str(False).lower())
    data.add_field("to_vector_store", str(True).lower())
    data.add_field("chunk_size", str(250))
    data.add_field("chunk_overlap", str(50))
    data.add_field("max_chunk_length", str(max_chunk_length))
    data.add_field("zh_title_enhance", str(True).lower())
    data.add_field("uid", str(uid))

    # api_key = "sk-abT0aeiFaHk43WKxiyT1S5xXJCLovu3o2ENh0ITfu9e451eY"

    data.add_field("api_key", str(api_key))
    data.add_field("split_mode", split_mode)
    data.add_field("separator", str(separator))
    data.add_field("base_url", Tools.config.kb.base_url)
    data.add_field("is_summarize", str(is_summarize).lower())

    async with aiohttp.ClientSession() as session:
        async with session.post(url=f"{Tools.config.kb.kb_url}/knowledge_base/upload_docs", data=data) as response:
            async for chunk in response.content.iter_chunked(1024):
                # 处理数据
                processor = StreamProcessor()
                value = chunk.decode().strip()
                processor.process_stream(value)

                # 获取处理后的事件
                events = processor.get_events()
                for event in events:
                    yield event
    for file in file_names:
        await TKnowledgeFile.filter(file_name=file,kb_base_id=kb_id).update(in_db=1)


async def upload_to_rag_backend(file,kb_id,api_key,base_url,kb_name,uid,**kwargs):
    Tools.log.info(f"upload_to_rag_backend: {kb_id} ")
    async def set_fail(filename,kb_base_id,msg=''):

        Tools.log.info(f"file: {filename} load file continue")
        Tools.log.info("error:"+msg)
        await TKnowledgeFile.filter(file_name=filename, kb_base_id=kb_base_id).update(in_db=0)
    from controllers.gpt import upload_blob, get_content_types, get_text_summary
    kb_base = await TKnowledgeBase.filter(id=kb_id).first().values("llm_model","embed_model")
    llm_func = await get_llm_func(api_key=api_key, model=kb_base.get("llm_model") or 'gpt-4o-mini', base_url=base_url + "/v1")
    emb_func = await get_emb_func(api_key=api_key, base_url=base_url + "/v1",model=kb_base.get("embed_model","jina-clip-v1"))
    kb_path = pa(WORKING_DIR) / str(uid) / kb_name
    #获取chunk切片模式
    spliter_name = kwargs.get("split_mode") if kwargs.get("split_mode") else "chunk_size"
    #获取块大小
    spliter_chunk_size  = kwargs.get("chunk_size")
    #获取相邻文本重合长度
    chunk_overlap = kwargs.get("chunk_overlap")
    #自定义分隔符
    separator = kwargs.get("separator")
    #构建切片fun
    chunk_spliter_func = await select_textspilter(textsliter_name=spliter_name,max_token_size=spliter_chunk_size,chunk_overlap=chunk_overlap)
    rag = GraphRAG(
        working_dir=kb_path,
        best_model_func=llm_func,
        cheap_model_func=llm_func,
        embedding_func=emb_func,
        embedding_func_max_async=3,
        embedding_batch_num = 320,
        chunk_func=chunk_spliter_func,
        enable_llm_cache=True,
        vector_db_storage_cls=MilvusStorge
    )
    Tools.log.debug(f"files:{file}")
    if not await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).exists():
            await set_fail(file.filename, kb_id,msg='file not found')
    k_file = await TKBFiles.filter(kb_id=kb_id, file_name=file.filename).first().values("blob_url")
    Tools.log.debug(f"f_file:{k_file}")
    if not k_file:
            await set_fail(file.filename, kb_id,msg='not k_file')
    try:
        result = await get_text_summary(k_file.get("blob_url"), api_key,**kwargs)
        if not result:
            await set_fail(file.filename, kb_id,msg='not result')

        # await rag.ainsert(result)
        #使用celery进行graph的文件插入
        task_name = KTasks.upload_graph
        Tools.log.debug(f"task_name:{task_name}")
        kw_data = {"uid":uid,"kb_name":kb_name,"textsliter_name":spliter_name,
                   "max_token_size":spliter_chunk_size,"chunk_overlap":chunk_overlap,"api_key":api_key,
                   "llm_model":kb_base.get("llm_model","gpt-4o-mini"),"emb_model":kb_base.get("embed_model","jina-clip-v1"),"base_url":base_url,
                   "text":result,"separator":separator}
        Tools.log.debug(f"kw_data:{kw_data}")
        res = await Tools.celery.send_task(task_name, kwargs = kw_data)
        Tools.log.debug(f"res:{res}")
    except:
        Tools.log.exception(f"upload_to_rag_backend: {file.filename} error")
        await set_fail(file.filename, kb_id,msg='get_text_summary error')
    
    if await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id, in_db__gt=InDB.FINISHED.value).exists():
            #获取文件id
            file_db = await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).first().values("id")
            #获取分区名
            pn = await rag.entities_vdb.get_partitions_name()
            Tools.log.debug(f"分区名：{pn}")
            #获取集合名
            collection_name = await rag.entities_vdb.get_collection_name()
            Tools.log.debug(f"集合名：{collection_name}")
            #存进知识库文件索引表
            await TKBFileToMilvus.create(
                kb_id = kb_id,
                kb_name = kb_name,
                file_id = file_db.get("id"),
                file_name = file.filename,
                collection_name = collection_name,
                partition_name = pn,
            )
            _ = await TKnowledgeBase.filter(id=kb_id).first().values("file_count")
            await TKnowledgeBase.filter(id=kb_id).update(file_count=_.get("file_count") + 1)
            await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).update(in_db=1)


async def upload_file_chatchat_background(
    files: List[UploadFile],
    kb_id,
    override,
    to_vector_store,
    chunk_size,
    chunk_overlap,
    zh_title_enhance,
    uid,
    request_id,
    split_mode='',
    separator='',
    max_chunk_length=4000,
    is_summarize=False,
    task_id="",
    **kwargs
):
    data = aiohttp.FormData()
    api_key = kwargs.get("api_key")
    base_url = kwargs.get("base_url")
    data.add_field("kb_id", str(kb_id))
    data.add_field("override", str(override).lower())
    data.add_field("to_vector_store", str(to_vector_store).lower())
    data.add_field("chunk_size", str(chunk_size))
    data.add_field("chunk_overlap", str(chunk_overlap))
    data.add_field("zh_title_enhance", str(zh_title_enhance).lower())
    data.add_field("uid", str(uid))

    for file in files:
        k_file = await TKBFiles.filter(kb_id=kb_id, file_name=file.filename).first().values("blob_url")
        async with aiohttp.ClientSession() as session:
            async with session.get(k_file.get("blob_url")) as f:
                file_content = await f.read()
        Tools.log.debug(file_content)
        if kwargs.get('doc2x') and file.filename.endswith("pdf"):
            text = await doc2md(file_content, token=api_key)
            file_content = text.encode("utf-8")
            file.filename = file.filename.replace(".pdf", ".md")
        data.add_field("files", file_content, filename=file.filename, content_type=file.content_type)

    data.add_field("api_key", str(api_key))
    data.add_field("split_mode", str(split_mode))
    data.add_field("separator", str(separator))
    data.add_field("max_chunk_length", str(max_chunk_length))
    data.add_field("base_url", base_url)
    data.add_field("is_summarize", str(is_summarize).lower())
    data.add_field("language", kwargs.get("language"))
    data.add_field("dynamic_merge", kwargs.get("dynamic_merge"))
    data.add_field("model_name", kwargs.get("model_name"))
    Tools.log.debug(f"meta-chunking选择的源文本语言：{kwargs.get('language')}")
    Tools.log.debug(f"meta-chunking是否需要动态合并：{kwargs.get('dynamic_merge')}")
    Tools.log.debug(f"meta-chunking使用的模型：{kwargs.get('model_name')}")
    async with aiohttp.ClientSession() as session:
        async with session.post(url=f"{Tools.config.kb.kb_url}/knowledge_base/upload_docs", data=data) as response:
            async for chunk in response.content.iter_chunked(1024):
                if await Tools.redis.get(f"crawler_{request_id}"):
                    Tools.log.debug(f"{request_id} 获取成功，任务停止")
                    raise Exception("sss")

                # # 处理数据
                # processor = StreamProcessor()
                # value = chunk.decode().strip()
                # processor.process_stream(value)

                # # 获取处理后的事件
                # events = processor.get_events()
                # Tools.log.debug(events)
                # for event in events:
                #     yield event

    for file in files:
        _ = await TKnowledgeBase.filter(id=kb_id).first().values("file_count")
        await TKnowledgeBase.filter(id=kb_id).update(file_count=_.get("file_count") + 1)
        await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).update(in_db=1,task_id=task_id)

async def  create_knowledge_base_upload_files_func(files:List[UploadFile], kb_id, override, to_vector_store, chunk_size,
                                                         chunk_overlap, zh_title_enhance,uid,request_id=None,split_mode='',separator='',max_chunk_length=4000, is_summarize=False, task_id="",**kwargs):
    """
    上传文件，或者向量化
    """

    from controllers.gpt import upload_blob, get_content_types, get_text_summary
    async def upload_file_chatchat():
        task_id = uuid.uuid4().hex
        background_tasks = kwargs.get("background_tasks")
        Tools.log.debug(f"上传chatchat, background_tasks: {background_tasks}")
        Tools.log.debug(f"上传的chatchat知识库Id:{kb_id}")
        for file in files:
            if not await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).exists():
                await TKnowledgeFile.create(file_name=file.filename, kb_base_id=kb_id,uid=uid,in_db=3)
        #放入后台
        background_tasks.add_task(upload_file_chatchat_background, files, kb_id, override, to_vector_store, chunk_size, chunk_overlap, 
                                  zh_title_enhance, uid, request_id, split_mode, separator,
                                  max_chunk_length, is_summarize, task_id,api_key=api_key,base_url=base_url,
                                  language = kwargs.get("language"),dynamic_merge =kwargs.get("dynamic_merge"),model_name = kwargs.get("model_name"))

        yield '[WAIT]'
    async def upload_file_rag_nano():

        task_id = uuid.uuid4().hex
        if files!=[]:
            file = files[0]
            kb_file = await TKnowledgeFile.filter(file_name=file.filename, kb_base_id=kb_id).first()
            if not kb_file:
                kb_file = await TKnowledgeFile.create(file_name=file.filename, kb_base_id=kb_id, uid=uid,
                                      kb_name=__token.get('kb_name'),in_db=InDB.WAIT.value,task_id=task_id,
                                      document_loader_name=__token.get('type'),
                                      file_version=1,
                                     file_size=0,
                                      )
                
            
            #asyncio.ensure_future(upload_to_rag_backend(file, kb_id, api_key, base_url, __token.get('kb_name'), uid, **kwargs))
            background_tasks = kwargs.get("background_tasks")
            background_tasks.add_task(upload_to_rag_backend,file, kb_id, api_key, base_url, __token.get('kb_name'), 
                                      uid,split_mode = split_mode,chunk_size =chunk_size,chunk_overlap =chunk_overlap,
                                      separator = separator)
            #await upload_to_rag_backend(file, kb_id, api_key, base_url, __token.get('kb_name'), uid, **kwargs)
            
        yield '[WAIT]'

    __token = await TKnowledgeBase.filter(id=kb_id).first().values("token_id",'type','kb_name')
    _ = await TTokenMapping.filter(user_id=uid,id=__token.get("token_id")).select_related("external_token").first().values("external_token__value")
    api_key = _.get("external_token__value")
    base_url = Tools.config.kb.base_url
    for file in files:
        file_content = await file.read()
        kb_f = await TKBFiles.filter(kb_id=kb_id, file_name=file.filename).first().values("blob_url")
        if kb_f:
            if override:
                #使用celery方式上传文件
                task_name = KTasks.upload_blob
                Tools.log.debug(f"task_name:{task_name}")
                encoded_string = base64.b64encode(file_content).decode('utf-8')
                kw_data = {"data":encoded_string,"file_name":file.filename,"pre":f"kb/{kb_id}","proxy_blob":True,"content_type":None}
                blob_url = await rec_celery_blob_url(task_name,kw_data)
                # blob_url = await upload_blob(file_content, file_name=file.filename,
                #                              pre=f"kb/{kb_id}", proxy_blob=True)
                Tools.log.debug(f"blob_url:{blob_url}")
                await TKBFiles.filter(kb_id=kb_id, file_name=file.filename).update(
                    blob_url=blob_url)
        else:
            #使用celery方式上传文件
            task_name = KTasks.upload_blob
            Tools.log.debug(f"task_name:{task_name}")
            encoded_string = base64.b64encode(file_content).decode('utf-8')
            kw_data = {"data":encoded_string,"file_name":file.filename,"pre":f"kb/{kb_id}","proxy_blob":True,"content_type":None}
            blob_url = await rec_celery_blob_url(task_name,kw_data)
            Tools.log.debug(f"blob_url:{blob_url}")
            # blob_url = await upload_blob(file_content, file_name=file.filename,
            #                              pre=f"kb/{kb_id}", proxy_blob=True)
            await TKBFiles.create(file_name=file.filename,
                                  blob_url=blob_url, kb_id=kb_id)


    if not __token.get("type",'chatchat') or __token.get("type",'chatchat') == "chatchat":
        async for res in upload_file_chatchat():
            yield res
    elif __token.get("type") == 'rag_nano':
        async for res in upload_file_rag_nano():
            yield res



async def delete_docs_func(uid, kb_id, file_names,only_vs):

    kb = await TKnowledgeBase.filter(id=kb_id).first().values("type")
    if not kb or kb.get("type",'chatchat') == 'chatchat':
        async with aiohttp.ClientSession() as session:
            for i in file_names:
                if i.endswith('.pdf'):
                    file_names.append(i.replace(".pdf",".md"))
            async with session.post(url=f"{Tools.config.kb.kb_url}/knowledge_base/delete_docs", json={"uid":uid,"kb_id":kb_id,"file_names":file_names,"only_vs":bool(only_vs)}) as resp:
                data = await resp.json()
                if data.get("code") == 200:
                    for file_name in file_names:
                        await TKnowledgeFile.filter(file_name=file_name, kb_base_id=kb_id).update(in_db=0)
                    return suc_data()
                return fail_data()
            
    elif not kb or kb.get("type",'chatchat') == 'rag_nano':
        #临时判断是否为旧知识库
        try:
            #旧知识库无milvus分区
            for file_name in file_names:
                fm_db = await TKBFileToMilvus.filter(kb_id=kb_id,file_name=file_name).first().values("partition_name")
                partition_name = fm_db.get("partition_name")
                partition_name = partition_name.decode('utf-8') if isinstance(partition_name, bytes) else partition_name
                Tools.log.debug("pn:",partition_name)
        except Exception:
            num = await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id,in_db=1).count()
            await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id).delete()
            await TKnowledgeBase.filter(id=kb_id).update(file_count=RawSQL(f'file_count-{num}'))
            return suc_data()

        num = await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id,in_db=1).count()
        await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id).delete()
        await TKnowledgeBase.filter(id=kb_id).update(file_count=RawSQL(f'file_count-{num}'))
        kb = await TKnowledgeBase.filter(uid=uid,id=kb_id).first().values('kb_name')
        kb_name = kb.get("kb_name")
        kb_path = pa(WORKING_DIR) / str(uid) / kb_name
        rag = GraphRAG(
        working_dir=kb_path,
        vector_db_storage_cls = MilvusStorge
    )   
        #循环删除有关文件在milvus的分区数据
        for file_name in file_names:
            fm_db = await TKBFileToMilvus.filter(kb_id=kb_id,file_name=file_name).first().values("partition_name")
            partition_name = fm_db.get("partition_name")
            partition_name = partition_name.decode('utf-8') if isinstance(partition_name, bytes) else partition_name
            Tools.log.debug("pn:",partition_name)
            if await rag.entities_vdb.has_partitions(partition_name):
                try:
                    #删除知识图谱中相关文件的节点
                    partition_data = await rag.entities_vdb.get_partition_data(partition_name)
                    Tools.log.debug("pn_data:",partition_data)
                    entity_name = []
                    for temp in partition_data:
                        Tools.log.debug("temp：",temp)
                        if temp.get("entity_name"):
                            entity_name.append(temp.get("entity_name"))
                    Tools.log.debug("entity_name",entity_name)
                
                    #删除图谱节点
                    await delete_graph_node(uid=uid,kb_name=kb_name,nodes=entity_name)
                    #删除milvus相关分区数据
                    await rag.entities_vdb.release_partition(partition_name)
                    await rag.entities_vdb.delete_partition(partition_name)
                except Exception:
                    return fail_data()
        await TKBFileToMilvus.filter(file_name__in=file_names, kb_id=kb_id).delete()
        Tools.log.debug(f"知识库索引表删除数据:kb_id-{kb_id},file_name-{file_name}")
        return suc_data()
    
    else:
        num = await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id,in_db=1).count()
        await TKnowledgeFile.filter(file_name__in=file_names, kb_base_id=kb_id).delete()
        await TKnowledgeBase.filter(id=kb_id).update(file_count=RawSQL(f'file_count-{num}'))
        return suc_data()

async def query_from_rag_nano(query,api_key,base_url,model='',tok_k=10,uid=0,kb_name='',**kwargs):

    @utils.cache(ttl=60)
    async def get_settings(key):
        map_token = await TTokenMapping.filter(
            external_token_id=Subquery(TToken.filter(value=key).first().values("id"))).first().values("id",
                                                                                                          "user_id")
        token_id = map_token.get("id")
        _ = await TTokenInfo.filter(token_id=token_id).first().values("settings","extra")
        settings = _.get("settings")
        extra = _.get("extra",{})
        kb_id = extra.get("kb_id")
        if kb_id:
            band_token = await TKnowledgeBase.filter(id=kb_id).first().values("token_id","embed_model")
            _2 = await TTokenInfo.filter(token_id=band_token.get("token_id")).first().values("settings")
            settings.update(_2.get("settings"))
            settings.update(band_token)
        return settings
    settings:dict = await get_settings(api_key)
    if not settings or not isinstance(settings,dict):
        settings = {}
    Tools.log.debug("settings:",settings)
    use_prompt = settings.get("kbConfig",{}).get("use_prompt","")
    llm_func = await get_llm_func(api_key=api_key,model=model or 'gpt-4o-mini',base_url=base_url+"/v1")
    emb_func = await get_emb_func(api_key=api_key,base_url=base_url+"/v1",model=settings.get("embed_model") or "jina-clip-v1")
    kb_path = pa(WORKING_DIR) / str(uid) / kb_name
    user_prompt = kwargs.get("user_prompt") if kwargs.get("user_prompt") else use_prompt
    if user_prompt:
        from nano_graphrag.prompt import PROMPTS
        prompt = await get_prompt(user_prompt)
        PROMPTS['global_map_rag_points'] = prompt
        PROMPTS['local_rag_response'] = prompt
    rag = GraphRAG(
        working_dir=kb_path,
        best_model_func=llm_func,
        cheap_model_func=llm_func,
        embedding_func=emb_func,
        vector_db_storage_cls = MilvusStorge

    )
    is_global:bool = bool(settings.get("graph_rag_conf",{}).get("is_global",False))
    answer = await rag.aquery(
        query, param=QueryParam(mode="local" if not is_global else 'global',top_k=tok_k,)
    )
    if answer:
        # import re
        # answer = re.sub(r'\[Data:.*?\]', '', answer)
        for i in range(0,len(answer),20):
            await asyncio.sleep(random.randint(1,50)/1000)
            yield suc_data(data={"answer":answer[i:i+20]}).model_dump_json()
    else:
        yield suc_data(data={"answer": 'sorry, I don\'t know the answer'}).model_dump_json()
    yield "DONE"
async def chat_with_kb_func(**kwargs):
    """
    与知识库聊天
    """
    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=kwargs.get("api_key")).first().values("id"))).first().values("id","user_id")
    uid = map_token.get("user_id")
    token_id = map_token.get("id")
    if token_id:
        token = await TTokenInfo.get(token_id=token_id).values("extra","settings")
        kb_id = token.get("extra").get("kb_id")
        user_prompt = token.get("settings",{}).get("kbConfig",{}).get("use_prompt","")
        kwargs['user_prompt'] = user_prompt
        # 存在可变参数传参的情况，如果不存在的话就用token_id对应的知识库
        kwargs['kb_id'] = kwargs.get("kb_id") or kb_id
    kwargs['uid'] = uid
    # api_key = "sk-abT0aeiFaHk43WKxiyT1S5xXJCLovu3o2ENh0ITfu9e451eY"
    # kwargs['api_key'] = api_key
    async with aiohttp.ClientSession() as session:
        async with session.post(url=f"{Tools.config.kb.kb_url}/chat/knowledge_base_chat", json=kwargs) as response:
            b = b""
            async for chunk in response.content.iter_chunks():
                # 处理数据
                processor = StreamProcessor()

                if chunk[-1]:
                    b += chunk[0]
                else:
                    b+=chunk[0]
                    continue

                processor.process_stream(b.decode())
                b = b""

                # 获取处理后的事件
                events = processor.get_events()
                for event in events:
                    yield suc_data(data=event).model_dump_json()
            yield "DONE"


async def error_handler(status:int):

    """
      "-99": "CAPTCHA_ERROR", // 验证码错误
  "-100": "CHATBOT_DISABLED_ERROR", // 机器人禁用
  "-101": "CHATBOT_DELETE_ERROR", // 机器人删除
  "-10002": "CHATBOT_DISABLED_ERROR2", // 机器人删除
  "-10003": "SERVER_ERROR", // 内部错误
  "-10004": "BALANCE_LIMIT_ERROR", // 账户余额不足
  "-10005": "TOKEN_EXPIRED_ERROR", // token过期
  "-10006": "TOTAL_QUOTA_ERROR", // 聊天机器人总额度不足
  "-10007": "DAILY_QUOTA_ERROR", // 聊天机器人当天额度不足
  "-10012": "HOUR_QUOTA_ERROR", // 聊天机器人该小时内额度
  "-10018": "MONTHLY_QUOTA_ERROR", // 聊天机器人当月余额不足

    """
    code = 0
    if status == GPTStatus.DELETED:
        code = -101
    if status == GPTStatus.DISABLE:
        code = -100
    if status == GPTStatus.EXPIRED:
        code = -10005
    if status == GPTStatus.LIMIT_CURRENT_MONTH:
        code = -10018
    if status == GPTStatus.LIMIT_CURRENT_DATE:
        code = -10007
    if status == GPTStatus.LIMIT_CURRENT_HOUR:
        code = -10012
    if status == -100:
        code = -10004
    if status == -103:
        code = -10006
    if status == -404:  # 当不存在处理
        code = -10003  # 当不存在处理
    if code == 0:
        return None
    return {
        "error": {
            "err_code": code,
            "message": "Token Invalid",
            "type": "api_error"
        }
    }

async def chat_with_agent_func(**kwargs):
    """
    与知识库聊天
    """
    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=kwargs.get("api_key")).first().values("id"))).first().values("id","user_id","status")

    uid = map_token.get("user_id")
    kwargs['uid'] = uid
    async with aiohttp.ClientSession() as session:
        async with session.post(url=f"{Tools.config.kb.kb_url}/chat/agent_chat", json=kwargs) as response:
            b = b""
            async for chunk in response.content.iter_chunks():
                # 处理数据
                processor = StreamProcessor()

                if chunk[-1]:
                    b += chunk[0]
                else:
                    b+=chunk[0]
                    continue

                processor.process_stream(b.decode())
                b = b""

                # 获取处理后的事件
                events = processor.get_events()
                for event in events:
                    yield suc_data(data=event).model_dump_json()
            yield "DONE"

async def doc2md(file_content,token):
    """
    将文件内容转换为md格式
    """
    headers = {
        "Authorization": f"Bearer {token}"
    }
    domain = f"{Tools.config.gpt_api}/doc2x_v2/api/v2/parse/pdf"

    async with aiohttp.ClientSession() as session:
        form_data = aiohttp.FormData()
        form_data.add_field("file", file_content, content_type='application/octet-stream')
        async with session.post(f"{domain}", headers=headers,data=form_data) as resp:
            text_tmp = await resp.text()
            Tools.log.debug(text_tmp)
            if resp.status == 200:
                text = text_tmp
    return text

async def to_md(url, token, headers:dict=None):
    if headers is None:
        headers = {}
    text = ""
    content = b""
    headers['Authorization'] = token
    domain = f"{Tools.config.gpt_api}/jina/reader"

    async with aiohttp.ClientSession() as session:
        async with session.get(f"{domain}/{url}", headers=headers) as resp:
            text_tmp = await resp.text()
            Tools.log.info(f"from jina: {text_tmp[:100]}")
            if resp.status == 200:
                text = text_tmp
    return text

async def get_info(**kwargs):
    kb_id = kwargs.get("kb_id")
    tz = kwargs.get("tz",'Asia/Shanghai')
    _ = await TKnowledgeBase.filter(id=kb_id).first().values("token_id")
    token_id = _.get("token_id")
    _ = await TTokenMapping.filter(id=token_id).select_related("external_token").first().values("external_token__value")
    Authorization =_.get("external_token__value")
    return Authorization,tz

async def download_url(**kwargs):
    token,tz = await get_info(**kwargs)
    sem = asyncio.Semaphore(20)
    depth = kwargs.get("depth",3)
    if depth <= 0:
        return []
    urls = await get_url_list(token=token,sem=sem,**kwargs)
    tmp_res = {"dpath_0":urls}
    for i in range(1,depth):
        urls = tmp_res.get(f"dpath_{i-1}")
        jobs = [get_url_list(url=url,token=token,sem=sem) for url in urls]
        tmp = []
        for s in await asyncio.gather(*jobs):
            if s:
                tmp.extend(s)
        tmp_res[f"dpath_{i}"] = tmp
    links = [kwargs.get("url")]
    for _,v in tmp_res.items():
        for x in v:
            if x not in links:
                links.append(x)
    return links
async def get_url_list(**kwargs):
    import re

    def extract_links(text):
        url_pattern = r'(https?://[^\s)]+)[<\)]'
        links = re.findall(url_pattern, text)
        if not links:
            return []
        return links
    sem=kwargs.get("sem")
    Tools.log.debug(kwargs.get("url"))
    async with sem:
        try:
            Authorization = kwargs.get('token', "")
            from urllib.parse import urlparse
            domain = urlparse(kwargs.get("url")).netloc
            text = await to_md(kwargs.get("url"), token=Authorization, headers={"X-With-Links-Summary": 'true'})
            image_extensions = r'\.(png|jpg|jpeg|gif|bmp|webp|tiff|svg)'

            list_url = filter(lambda x: domain in urlparse(x).netloc and not re.search(image_extensions, x), set(extract_links(text)))
            tmp = list(list_url)
            Tools.log.debug(f"解析{kwargs.get('url')},获取了{len(tmp)}个链接 :{tmp}")
        except:
            return []
        return tmp


import os
async def get_path_list(**kwargs):
    kb_id = kwargs.get("kb_id")
    tz = kwargs.get("tz", 'Asia/Shanghai')
    _ = await TKnowledgeBase.filter(id=kb_id).first().values("token_id")
    token_id = _.get("token_id")
    _ = await TTokenMapping.filter(id=token_id).select_related("external_token").first().values("external_token__value")
    Authorization = _.get("external_token__value")
    depth = kwargs.get("depth", 3)
    t = kwargs.get('t', 10)

    url = kwargs.get("url")
    uid = kwargs.get("uid")
    target_content = kwargs.get("target_content")
    from libs.markdown_crawler import md_crawl
    path = os.path.join('markdown', str(uid))
    loop = asyncio.get_event_loop()
    from functools import partial
    tmp = {}
    if target_content:
        tmp['target_content'] = target_content
    Tools.log.debug(f"当前的depth 为：{depth}")
    if url.endswith("xml"):
        def parse_sitemap(xml_content):
            # 解析 XML 内容
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(xml_content)

                # 获取所有 <loc> 标签的 URL
                urls = [loc.text for loc in root.findall('.//loc')]
            except Exception as e:
                print(e)
                return []
            return urls

        async with aiohttp.ClientSession() as session:
            async with session.get(url) as resp:
                text = await resp.text()
                url_list = parse_sitemap(text)
            jobs = []
            for _url in url_list[:20]:
                job = partial(md_crawl, _url, 0, t, path, **tmp)
                jobs.append(loop.run_in_executor(None, job))
            await asyncio.gather(*jobs)
    else:
        cra = partial(md_crawl, url, depth, t, path, **tmp)
        await loop.run_in_executor(None, cra)

    list_path = os.listdir(path)
    if not list_path:
        raise ComstomException("未获取到内容,path not files", code=StateCode.GenerationError.value)
    all_not = []
    for i in list_path:
        if i.endswith(".md"):
            with open(os.path.join(path,i),"r") as f:
                text = f.read()
                Tools.log.debug(text)
                if not text.strip():
                    all_not.append(True)
                    os.remove(os.path.join(path,i))
                else:
                    all_not.append(False)
    if all(all_not):
        raise ComstomException("未获取到内容", code=StateCode.GenerationError.value)

    return path
async def crawler(**kwargs):
    url = kwargs.get("url")
    uid = kwargs.get("uid")
    kb_id = kwargs.get("kb_id")
    split_mode = kwargs.get("split_mode","")
    separator = kwargs.get("separator", "")
    is_summarize = kwargs.get("is_summarize", False)
    list_path = os.listdir(os.path.join('markdown', str(uid)))
    files = []
    text = ''
    result = []
    tz = kwargs.get("tz", 'Asia/Shanghai')
    path = os.path.join('markdown', str(uid))
    import pytz
    timezone = pytz.timezone(tz)
    timestamp = "_" + datetime.datetime.now(tz=timezone).strftime("%Y%m%d%H%M")
    for i in list_path:
        if i.endswith(".md"):
            with open(os.path.join(path,i),"rb") as f:
                text = f.read()
                file_like_object = BytesIO(text)
                file_name = "".join(i.split(".")[:-1])+timestamp+".md"

                files.append(UploadFile(file=file_like_object, filename=file_name))
            os.remove(os.path.join(path,i))

    result.append({"file_name": url,"title": url, "current": 0, "total": len(list_path),"status":0 if text else -1})
    for index,file in enumerate(files):
        async for resp in create_knowledge_base_upload_files_func([file], kb_id, kwargs.get("override"), kwargs.get("to_vector_store"), kwargs.get("chunk_size"),
                                                             kwargs.get("chunk_overlap"), kwargs.get("zh_title_enhance"), kwargs.get("uid"),split_mode=split_mode,separator=separator, is_summarize=is_summarize):
            Tools.log.debug(resp)
            result[0]['current'] = index+1
            if index == len(list_path)-1:
                result[0]['status'] = 2
            data = suc_data(data=result)
            yield data.model_dump_json()

    yield '[DONE]'
    # md_crawl(url, max_depth=3, num_threads=5, base_dir=os.path.join('markdown',str(uid)))


async def to_md5(**kwargs):
    kb_id = kwargs.get("kb_id")
    is_msg = kwargs.get("is_msg",False)
    split_mode = kwargs.get("split_mode","")
    separator = kwargs.get("separator", "")
    max_chunk_length = kwargs.get("max_chunk_length",4000)
    is_summarize = kwargs.get("is_summarize", False)
    tz = kwargs.get("tz",'Asia/Shanghai')
    _ = await TKnowledgeBase.filter(id=kb_id).first().values("token_id")
    token_id = _.get("token_id")
    _ = await TTokenMapping.filter(id=token_id).select_related("external_token").first().values("external_token__value")
    Authorization =_.get("external_token__value")
    files = []
    result = []
    for url in kwargs.get("url_list"):
        result.append({"file_name": url, "title": "", "current": 0, "total": 1, "status": 0})
    request_id = uuid.uuid4().hex
    for index,url in enumerate(kwargs.get("url_list")):
        text = await to_md(url,token=Authorization)
        if text:
            import re
            name = re.split("[!-/:-@[-`{-~\n\t]",text)[1].strip()
            if not name:
                import urllib.parse
                # 解析url
                url_parts = urllib.parse.urlparse(url)
                name = url_parts.netloc.replace(".","")
            name = name.replace(" ","")
            import pytz
            timezone = pytz.timezone(tz)
            name += "_"+datetime.datetime.now(tz=timezone).strftime("%Y%m%d%H%M")
            title = name + ".md"
            file_like_object = BytesIO(text.encode())
            # Tools.log.debug(file_like_object.read())
        else:
            title = ""
            file_like_object = BytesIO(b'')

        async for resp in create_knowledge_base_upload_files_func([UploadFile(file=file_like_object, filename=f"{title}")], kb_id, kwargs.get("override"), kwargs.get("to_vector_store"), kwargs.get("chunk_size"),
                                                             kwargs.get("chunk_overlap"), kwargs.get("zh_title_enhance"), kwargs.get("uid"),request_id=request_id,split_mode=split_mode,separator=separator,max_chunk_length=max_chunk_length,is_summarize=is_summarize,background_tasks = kwargs.get("background_tasks"),
                                                             language = kwargs.get("language"),dynamic_merge =kwargs.get("dynamic_merge"),model_name=kwargs.get("model_name") 
                                                             ):

            if is_msg:
                if isinstance(resp,str):
                    yield resp
                elif resp['status'] == 2:
                    data = suc_data(data={"msg":url,"request_id":request_id})
                    yield data.model_dump_json()
            else:
                if isinstance(resp,str):
                    yield resp
                else:
                    resp['file_name'] = result[index]['file_name']
                    resp['title'] = title
                    result[index] = resp
                    data = suc_data(data=result)
                    yield data.model_dump_json()
        # files.append(UploadFile(file=file_like_object, filename=f"{title}"))
    # result = [{"file_name": url, "current": 0, "total": 1,"status":0} for file in files]
    # yield suc_data(data=result).model_dump_json()
    # for index,file in enumerate(files):
    #     async for resp in create_knowledge_base_upload_files_func([file], kb_id, kwargs.get("override"), kwargs.get("to_vector_store"), kwargs.get("chunk_size"),
    #                                                          kwargs.get("chunk_overlap"), kwargs.get("zh_title_enhance"), kwargs.get("uid")):
    #         Tools.log.debug(resp)
    #         resp['file_name'] = result[index]['file_name']
    #         result[index] = resp
    #         data = suc_data(data=result)
    #         yield data.model_dump_json()

    yield '[DONE]'


async def list_snippets_func(file_id, uid,page:int,page_size:int,text=""):
    """
    列出文件下的所有snippet
    """
    file_obj = await TKnowledgeFile.filter(id=file_id).first()
    if not file_obj:
        return fail_data(msg="文件不存在")
    file_name = file_obj.file_name
    kb_name = file_obj.kb_name
    kb_id = file_obj.kb_base_id
    async with aiohttp.ClientSession() as session:
        async with session.get(f"{Tools.config.kb.kb_url}/knowledge_base/split/docs", params={"file_name": file_name, "kb_name": kb_name, "text": text
            , "kb_id": kb_id, "uid": uid, "page": page, "page_size": page_size}) as resp:
            if resp.status == 200:
                records = await resp.json()
            else:
                records = [],0
    data = {
        "records":records[0],
        "page":page,
        "page_size":page_size,
        "total":records[1]
    }
    return suc_data(data=data)


async def update_snippets_func(uuid:str,uid:int,kb_id:int):
    async with aiohttp.ClientSession() as session:
        async with session.put(f"{Tools.config.kb.kb_url}/knowledge_base/split/docs/{uuid}?kb_id={kb_id}&uid={uid}") as resp:
            ...




# load GraphML file and transfer to JSON
def graphml_to_json(graphml_file):
    G = nx.read_graphml(graphml_file)
    data = nx.node_link_data(G)
    return json.dumps(data)
# create HTML file
async def create_html(graphml_json):
    html_content = f'''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Visualization</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body, html {{
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }}
        svg {{
            width: 100%;
            height: 100%;
        }}
        .links line {{
            stroke: #999;
            stroke-opacity: 0.6;
        }}
        .nodes circle {{
            stroke: #fff;
            stroke-width: 1.5px;
        }}
        .node-label {{
            font-size: 12px;
            pointer-events: none;
        }}
        .link-label {{
            font-size: 10px;
            fill: #666;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
        }}
        .link:hover .link-label {{
            opacity: 1;
        }}
        .tooltip {{
            position: absolute;
            text-align: left;
            padding: 10px;
            font: 12px sans-serif;
            background: lightsteelblue;
            border: 0px;
            border-radius: 8px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            max-width: 300px;
        }}
        .legend {{
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 10px;
            border-radius: 5px;
        }}
        .legend-item {{
            margin: 5px 0;
        }}
        .legend-color {{
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 5px;
            vertical-align: middle;
        }}
    </style>
</head>
<body>
    <svg></svg>
    <div class="tooltip"></div>
    <div class="legend"></div>
    <script>
        const graphData = {graphml_json};
        
        const svg = d3.select("svg"),
            width = window.innerWidth,
            height = window.innerHeight;

        svg.attr("viewBox", [0, 0, width, height]);

        const g = svg.append("g");

        const entityTypes = [...new Set(graphData.nodes.map(d => d.entity_type))];
        const color = d3.scaleOrdinal(d3.schemeCategory10).domain(entityTypes);

        const simulation = d3.forceSimulation(graphData.nodes)
            .force("link", d3.forceLink(graphData.links).id(d => d.id).distance(150))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2))
            .force("collide", d3.forceCollide().radius(30));

        const linkGroup = g.append("g")
            .attr("class", "links")
            .selectAll("g")
            .data(graphData.links)
            .enter().append("g")
            .attr("class", "link");

        const link = linkGroup.append("line")
            .attr("stroke-width", d => Math.sqrt(d.value));

        const linkLabel = linkGroup.append("text")
            .attr("class", "link-label")
            .text(d => d.description || "");

        const node = g.append("g")
            .attr("class", "nodes")
            .selectAll("circle")
            .data(graphData.nodes)
            .enter().append("circle")
            .attr("r", 5)
            .attr("fill", d => color(d.entity_type))
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        const nodeLabel = g.append("g")
            .attr("class", "node-labels")
            .selectAll("text")
            .data(graphData.nodes)
            .enter().append("text")
            .attr("class", "node-label")
            .text(d => d.id);

        const tooltip = d3.select(".tooltip");

        node.on("mouseover", function(event, d) {{
            tooltip.transition()
                .duration(200)
                .style("opacity", .9);
            tooltip.html(`<strong>${{d.id}}</strong><br>Entity Type: ${{d.entity_type}}<br>Description: ${{d.description || "N/A"}}`)
                .style("left", (event.pageX + 10) + "px")
                .style("top", (event.pageY - 28) + "px");
        }})
        .on("mouseout", function(d) {{
            tooltip.transition()
                .duration(500)
                .style("opacity", 0);
        }});

        const legend = d3.select(".legend");
        entityTypes.forEach(type => {{
            legend.append("div")
                .attr("class", "legend-item")
                .html(`<span class="legend-color" style="background-color: ${{color(type)}}"></span>${{type}}`);
        }});

        simulation
            .nodes(graphData.nodes)
            .on("tick", ticked);

        simulation.force("link")
            .links(graphData.links);

        function ticked() {{
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            linkLabel
                .attr("x", d => (d.source.x + d.target.x) / 2)
                .attr("y", d => (d.source.y + d.target.y) / 2)
                .attr("text-anchor", "middle")
                .attr("dominant-baseline", "middle");

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            nodeLabel
                .attr("x", d => d.x + 8)
                .attr("y", d => d.y + 3);
        }}

        function dragstarted(event) {{
            if (!event.active) simulation.alphaTarget(0.3).restart();
            event.subject.fx = event.subject.x;
            event.subject.fy = event.subject.y;
        }}

        function dragged(event) {{
            event.subject.fx = event.x;
            event.subject.fy = event.y;
        }}

        function dragended(event) {{
            if (!event.active) simulation.alphaTarget(0);
            event.subject.fx = null;
            event.subject.fy = null;
        }}

        const zoom = d3.zoom()
            .scaleExtent([0.1, 10])
            .on("zoom", zoomed);

        svg.call(zoom);

        function zoomed(event) {{
            g.attr("transform", event.transform);
        }}

    </script>
</body>
</html>
    '''
    return html_content

async def get_graph_data(uid,kb_name):
    graphml_file = pa(WORKING_DIR)/str(uid)/kb_name/f"graph_chunk_entity_relation.graphml"
    graphml_json_data = graphml_to_json(graphml_file)
    graphml_json =  graphml_json_data.replace('\\"', '').replace("'", "\\'").replace("\n", "")
    graphml_html = await create_html(graphml_json)
    return graphml_html

async def get_graph_file(uid,kb_name):
    graphml_file = pa(WORKING_DIR)/str(uid)/kb_name/f"graph_chunk_entity_relation.graphml"
    return graphml_file
#更新知识图谱文件中的数据
async def delete_graph_node(uid,kb_name,nodes):
    graphml_file = pa(WORKING_DIR)/str(uid)/kb_name/f"graph_chunk_entity_relation.graphml"
    G = nx.read_graphml(graphml_file)
    # 删除节点及其相关边
    Tools.log.debug(f"删除的节点：{nodes}")
    for entity_name in nodes:
        if f'{entity_name}' in G.nodes:
            G.remove_node(f'{entity_name}')
    nx.write_graphml(G, graphml_file) 


#选择分割器以及设置每个chunk的切片阈值
async def select_textspilter(textsliter_name,max_token_size,chunk_overlap):
    from functools import partial
    from nano_graphrag._op import chunking_by_token_size,chunking_by_seperators

    if textsliter_name == "chunk_size":
        Tools.log.debug(f"当前选择的切片方法为传统切片,切片块大小为{max_token_size},相邻文本重合长度为{chunk_overlap}")
        custom_chunk_func = partial(chunking_by_token_size, max_token_size=max_token_size,overlap_token_size = chunk_overlap)
    
    elif textsliter_name == "separator":
        Tools.log.debug(f"当前选择的切片方法为分隔符切片,切片块大小为{max_token_size},相邻文本重合长度为{chunk_overlap}")
        custom_chunk_func = partial(chunking_by_seperators, max_token_size=max_token_size,overlap_token_size = chunk_overlap)

    return custom_chunk_func


#构建nano-graph的回答prompt
async def get_prompt(prompt):
    prompt_template  =""" 

---Goal---

Generate a response of the target length and format that responds to the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.

If you don't know the answer, just say so. Do not make anything up.No need to explain the data source.
Output the result using natural semantics. 

---Target response length and format---

{response_type}


---Data tables---

{context_data}


---Goal---

Generate a response of the target length and format that responds to the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.

If you don't know the answer, just say so. Do not make anything up.No need to explain the data source.
Output the result using natural semantics. 

---Target response length and format---

{response_type}

Add sections and commentary to the response as appropriate for the length and format. Style the response in markdown.
""" 
    prompt_template = "".join(["\n---Role---\n",prompt,prompt_template])
    return prompt_template 

async def  rec_celery_blob_url(task_name,data):
    blob_url_res = await Tools.celery.send_task(task_name, kwargs = data)
    blob_url = blob_url_res.get("blob_url")
    return blob_url