# -*- coding: utf-8 -*-
# @Time    : 2024/10/17 17:17
# <AUTHOR> hzx1994
# @File    : IP.py
# @Software: PyCharm
# @description:
import ipaddress

ip_type = ('SINGLE','SUBNET','RANGE')

class IP():
    def __init__(self,ip_or_ip_range,list_type:str=None):
        self.ip_or_ip_range = ip_or_ip_range.strip()
        self.data = {}
        if list_type:
            self.data['list_type'] = list_type


    def check(self):
        self.is_valid_ip_range() or self.is_valid_ip() or self.is_valid_cidr()
        return self

    def is_valid_ip_range(self):
        parts = self.ip_or_ip_range.split('-')
        if len(parts) != 2:
            return False
        try:
            start = ipaddress.ip_address(parts[0].strip())
            end = ipaddress.ip_address(parts[1].strip())
            if start <= end:
                self.data["ip_range_start"] = parts[0].strip()
                self.data["ip_range_end"] = parts[1].strip()
                self.data['ip_type'] = "RANGE"
                return True
        except ValueError:
            return False

    def is_valid_ip(self):
        try:
            ipaddress.ip_address(self.ip_or_ip_range)
            self.data["ip_address"] = self.ip_or_ip_range
            self.data['ip_type'] = "SINGLE"
            return True
        except ValueError:
            return False

    def is_valid_cidr(self):
        try:
            ipaddress.ip_network(self.ip_or_ip_range, strict=False)
            self.data["ip_subnet"] = self.ip_or_ip_range
            self.data['ip_type'] = "SUBNET"
            return True
        except ValueError:
            return False

if __name__ == '__main__':
    i = IP('***********-*************').check()
    print(i.data)
    i = IP('***********/16').check()
    print(i.data)
    i = IP('***********').check()
    print(i.data)