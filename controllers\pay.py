# -*- coding: utf-8 -*-
# @Time    : 2023/9/21 14:24
# <AUTHOR> hzx1994
# @File    : pay.py
# @Software: PyCharm
# @description:
import asyncio
import datetime
import json
from keyword import kwlist
from math import e
from pydoc import plain
import time
from _pydecimal import Decimal
import re

import aiohttp
from cycler import K
from fastapi import HTTPException, status
from fastapi.security import api_key
import httpx
from tortoise import Tortoise, connections

import stripe
from starlette.requests import Request
from tortoise.transactions import atomic
from tortoise.expressions import Subquery

import utils
from conf import constants
from controllers.pubilc import add_ready_ip_order, add_email_blacklist
from exections import raise_execption
from libs import Epay
from libs.Epay import check_sign
from libs.SHA256WithRSA import RSAPrvCrypt, RSAPubCrypt
from models.db.proxy import TInviteRef, TIpRoyalRenewLog, TIps, TPayway, TIpOrders, TOrderInfo, TProxyMachines, User<PERSON>n<PERSON>, <PERSON><PERSON><PERSON><PERSON>oken, <PERSON><PERSON><PERSON>, TInviteUserSettings, \
    TInviteOrders, TProxyIp, TIproyalOrder, TIproyalLocation, TTrafficIps, TGeoCountry, TGeoCity, TGeoState, TStripe,TUserStripe,UserInfo,TUserStripe

from models.response import suc_data, fail_data
from utils import Tools, random_string, ip2int, ip2hex, is_from_gpt, current_timestamp, text_to_qrcode_to_b64, proxy, \
    cache

PAY_METHODS = {
    100: "Stripe",
    101: "Stripe(credit card)",
    102: "Stripe(alipay)",
    103: "Stripe(wechat)",
    200: "Paypal",
    201: "Paypal(webhook)",
    202: "Paypal(self-check)",
    300: "Braintree",
    301: "Braintree(credit card)",
    302: "Braintree(paypal)",
    400: "Payssion",
    401: "Payssion(credit card)",
    402: "Payssion(bitcoin)",
    403: "Payssion(alipay)",
    500: "CoinPayments",
    501: "CoinPayments(LTCT)",
    502: "CoinPayments(BTC)",
    503: "CoinPayments(USDT)",
    600: "ChinaGpay",
    601: "Alipay", # (ChinaGpay)
    602: "WechatPay", # (ChinaGpay)

    700:"USDT"
}
class PaymentApiHandler():
    type = 1

    @property
    def redirect_url(self):
        redirect_url = f"https://{Tools.config.dashboard_host}/charge"
        return redirect_url

    @property
    def redirect_url_gpt(self):
        redirect_url = f"https://{Tools.config.gpt_dashboard_host}/charge"
        return redirect_url

    def get_time(self,text="createPayment",extra=""):
        now = time.time()
        self.start = now
        Tools.log.debug(f" execute time: {text} {now} {extra}")

    def __init__(self,uid=0,payway_id=0,host="",is_gpt=False,is_cny=False,is_wechat_pay=False,email='',**kw):
        self.start = 0
        self.payway_id= payway_id
        self.uid = uid
        self.host = host
        self.email = email
        self.phone = kw.get("phone")
        self.is_gpt = is_gpt
        self.is_cny = is_cny
        self.is_wechat_pay = is_wechat_pay
        self.extra = kw

    async def set_token_expired(self,user_id=0,is_expired=True):
        await TUserToken.filter(user_id=user_id,deleted_on=0,la_id__gt=0).update(expired=is_expired,modified_on=time.time())

    async def get_order_by_pay_order(self,pay_order):
        return await TIpOrders.filter(deleted_on=0 ,pay_order=pay_order).first()

    @atomic("default")
    async def change_ip_order_status_by_pay_order(self,pay_order, receive_currency=0, status=1, valid=True,text=""):
        """
        更新订单状态以及用户余额

        """
        t = int(time.time())
        ip_order = await self.get_order_by_pay_order(pay_order)
        ip_order = ip_order.to_dict()
        order_receive = ip_order['receive_currency']

        Tools.log.debug(ip_order)
        if not ip_order:
            raise HTTPException(400,f"not find {pay_order}")
        if ip_order['status'] != 0:
            Tools.log.error(f"ip order {pay_order}  ID:{ip_order['orderid']} has completed. ")
            raise HTTPException(400,f"order {pay_order}   has completed. ")
        ip_order['uid'] = ip_order['user_id']
        uid, amount, type = ip_order['uid'], ip_order['amount'], ip_order['type']

        if type not in ('+', '-'):
            raise HTTPException(400,f"type error, not allow type={type}")
        last_order = await TIpOrders.filter(deleted_on=0,status__not=0,valid=True,user_id=uid).order_by("-orderid").first().values("total_money", "balance")

        # last_order = db.mysql.get("""
        #     SELECT total_money, balance FROM t_ip_orders
        #     WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true ORDER BY orderid DESC LIMIT 1
        # """, uid)

        # 加锁，在流水脚本那边做判断，用户加锁期间跳过用户对应的记录
        key = "t_ip_orders:%s" % uid
        await Tools.redis.setex(key, 30, "")
        total_money, balance = 0, 0
        if last_order:
            if type == "+":
                total_money, balance = last_order['total_money'] + amount, last_order['balance'] + amount
            elif type == "-":
                total_money, balance = last_order['total_money'], last_order['balance'] - amount
        else:
            if type == "+":
                total_money, balance = amount, amount
            elif type == "-":
                total_money, balance = 0, -amount
        if balance < 0:
            raise HTTPException(400,"balance error")
        order_id = int(re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))) // 100
        conn = Tortoise.get_connection("default")
        await conn.execute_query("""UPDATE t_ip_orders SET orderid = %s, modified_on = %s,  
            status = %s, valid = %s, balance = %s, total_money = %s ,checksum = 2
            WHERE  deleted_on = 0 AND pay_order = %s """, [order_id, t, status, valid, balance, total_money, pay_order])
        # await TIpOrders.filter(pay_order=pay_order).update(orderid=order_id, modified_on=t, receive_currency=receive_currency, status=status, valid=valid,
        #                                                     balance=balance, total_money=total_money,text=text,checksum=2)
        await UserInfo.filter(uid = uid).update(balance=balance, total_balance=total_money,ex_balance=total_money-balance,modified_on=t)
        # db.mysql.execute("""
        #     UPDATE t_ip_orders SET orderid = %s, modified_on = %s,  receive_currency = %s,
        #     status = %s, valid = %s, balance = %s, total_money = %s
        #     WHERE  deleted_on = 0 AND pay_order = %s
        # """,order_id, t, receive_currency, status, valid, balance, total_money, pay_order)
        # db.mysql.execute("""
        #     UPDATE t_users_info SET balance = %s, total_balance = %s, ex_balance = %s, modified_on = %s WHERE uid = %s
        # """, balance, total_money, total_money - balance, t, uid)
        return ip_order


    async def _getPaywayObject(self):
        payway_obj =await TPayway.get(id=self.payway_id)
        return payway_obj


    async def post(self,is_gpt=False):
        if not self.uid:
            return
        payway_obj = await self._getPaywayObject()
        if is_gpt:
            pk = Tools.config.stripe_gpt.pk
        else:
            pk = Tools.config.stripe.pk
        if not payway_obj:
            raise_execption("1")
        # if (self.get_cookie("IT", "") != "true" and payway_obj['tries_limit'] >= 0 and
        #         get_count_user_buy_payway(uid, payway_obj['payway']) >= payway_obj['tries_limit']):
        #     self.return_error(constants.ERR_CHARGED_LIMIT)
        #     return
        dict_data = await self.createPayment(self.uid, payway_obj)
        res = {
            "session_id": dict_data.get("session_id", ""),
            "key":"from302_"+random_string(15),
            "jk":pk[:10],
            "hk":pk[10:13],
            "pk":pk[13:20],
            "a":pk[20:],
            "ts":int(datetime.datetime.now().timestamp()),
            "to":dict_data.get("to", ""),
            "type":self.type

        }

        return res

    async def createPayment(self, uid:int, payway) -> dict:
        ...

    async def call_back(self,request:Request) -> dict:
        ...

    async def has_change(self,uid) -> int:
        """
        用户充值次数
        """
        _ = await UserInfo.get(uid=uid).values("total_balance")
        return _.get("total_balance") > 1000

    async def user_change_times(self,uid,created_on=0) -> int:
        """
        用户充值次数
        """
        count = await TIpOrders.filter(user_id=uid,status = 1,valid=True,pay_order__gt="",payway__not="new user",created_on__gt=created_on).count()
        return count

    async def get_user_info(self,uid) ->dict:
        user_info = await UserInfo.get(uid=uid)
        return user_info.to_dict()

    async def get_payway_info(self,pay_way_id) ->dict:
        payway_info = await TPayway.get(id=pay_way_id)
        return payway_info.to_dict()

    async def get_user_by_uid(self,uid) ->dict:
        """
        根据用户id获取用户记录
        """
        user = await TUser.filter(uid=uid).first()
        if user:
            return user.to_dict()

    async def get_inv_user_settings(self,user_id) ->dict:
        settings = await TInviteUserSettings.filter(inv_uid=user_id,deleted_on=0).first()
        if settings:
            return settings.to_dict()

    async def set_invite_order(self,inv_uid: int, payway: str, value: float, uid: int = 0, charge_value: float = 0,
                     charge_num: int = 0, charge_orderid: int = 0, other: str = ""):
        """
                    SELECT * FROM t_invite_orders WHERE deleted_on = 0 AND inv_uid = %s AND status = 1
            ORDER BY order_num DESC LIMIT 1
        """
        max_order = await TInviteOrders.filter(inv_uid=inv_uid, deleted_on=0).order_by("-order_num").limit(1).first()
        max_order = max_order.to_dict()
        # max_order = get_max_invite_order(inv_uid)
        if max_order:
            _amount, _balance = max_order['amount'], max_order['balance']
            order_num = max_order['order_num'] + 1
        else:
            _amount, _balance, order_num = 0, 0, 0
        amount, balance = _amount + max(value, 0), _balance + value
        if balance < 0:
            return 0
        t, order_id = int(time.time()), re.sub(r"-|:|\.| ", '', str(datetime.now()))
        await TInviteOrders.create(order_id=order_id,order_num=order_num,inv_uid=inv_uid,uid=uid,charge_num=charge_num,charge_value=charge_value,
                              charge_orderid=charge_orderid,value=value,balance=balance,amount=amount,other=other,status=1,payway=payway)
        # try:
        #     return db.mysql.execute("""
        #         INSERT INTO t_invite_orders (created_on, modified_on, deleted_on, order_id, order_num,
        #         inv_uid, uid, charge_num, charge_value, charge_order_id, payway, status, value, balance, amount, other)
        #         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        #     """, t, t, 0, order_id, order_num, inv_uid, uid, charge_num, charge_value, charge_orderid,
        #     payway, 1, value, balance, amount, other)
        # except:
        #     log.error(traceback.format_exc())
        #     return 0

    async def set_order(self,amount: int, pay_order: str, text: str = "", pay_method="", user_data={},rate=1):
        order = await self.change_ip_order_status_by_pay_order(pay_order, receive_currency=amount, status=1, valid=True,text="")
        amount = order['receive_currency']

        Tools.log.debug(f"Alipay Log :{pay_order} 充值了,包括手续费 ${amount} ")
        if order:
            await self.set_token_expired(order['uid'],is_expired=False)  # 流量代理设置为 不显示余额不足
            # set_user_traffic_proxy_expired(order['uid'], expired=False)  # 流量代理设置为 不显示余额不足
            user_info = await self.get_user_info(order['uid'])
            Tools.log.debug("user_info", user_info)
            Tools.log.info("RECHARGE LOG: %s 充值了,包括手续费 %s " % (order['uid'], amount))
            # 如果是第一次充值馈赠邀请人
            times = await self.user_change_times(order['uid'])
            if times == 0:
                #  1. 用户邀请
                invite_for = user_info['invite_for']
                Tools.log.debug(f"invite_for {invite_for}")
                if invite_for > 0:  # 有邀请人
                    invite_payway = await self.get_payway_info(constants.INVITE)
                    orderid = await add_ready_ip_order(update_user_info=True,
                                                       user_id=invite_for, type=invite_payway['type'], currency_type="IP", currency=0,
                                                       receive_currency=0,
                                                       payway=invite_payway['payway'], pay_order="", value=0,is_inner=True,
                                                       extra_value=round(order['value'] / invite_payway['pay_value'] + 0.01), status=1, checksum=True, valid=True
                                                       )
                    await self.set_token_expired(invite_for, is_expired=False)  # 设置用户的代理不过期
                    # set_ip_order(
                    #     uid=invite_for, type=invite_payway['type'], currency=0, currency_type="IP",
                    #     receive_currency=0, payway=invite_payway['payway'], pay_order="", is_inner=True,
                    #     value=0, extra_value=round(order['value'] / invite_payway['pay_value'] + 0.01),
                    #     # 首次充值的pay_value%赠送给邀请人
                    #     status=1, checksum=True, valid=True)
                    # self.set_user_traffic_proxy_expired(invite_for, expired=False)  # 流量代理设置为 不显示余额不足
                    accept_invite_payway = await get_payway("accept_invite")
                    if accept_invite_payway:
                        accept_invite_payway = accept_invite_payway.to_dict()
                        await add_ready_ip_order(update_user_info=True,
                                                 user_id=order['uid'], type=accept_invite_payway['type'], currency=0, currency_type="IP",
                                                 receive_currency=0, payway=accept_invite_payway['payway'], pay_order="", is_inner=True,
                                                 value=0, extra_value=round(order['value'] / accept_invite_payway['pay_value'] + 0.01),
                                                 status=1, checksum=True, valid=True)
                        # set_user_traffic_proxy_expired(order['uid'], expired=False)  # 流量代理设置为 不显示余额不足
                        await self.set_token_expired(order['uid'], is_expired=False)  # 设置用户的代理不过期
                # 2. 内部邀请
                it_invite_id = user_info['it_invite_for']
                if it_invite_id:
                    accept_invite_payway = await get_payway("accept_invite")
                    if accept_invite_payway:
                        accept_invite_payway = accept_invite_payway.to_dict()
                        await add_ready_ip_order(update_user_info=True,
                                                 user_id=order['uid'], type=accept_invite_payway['type'], currency=0, currency_type="IP",
                                                 receive_currency=0, payway=accept_invite_payway['payway'], pay_order="", is_inner=True,
                                                 value=0, extra_value=round(order['value'] / accept_invite_payway['pay_value'] + 0.01),
                                                 status=1, checksum=True, valid=True)
                        await self.set_token_expired(order['uid'], is_expired=False)  # 流量代理设置为 不显示余额不足
            # 3. 返现邀请


            u = await self.get_user_by_uid(order['uid'])
            if user_info['reward_invite_for']:
                reward_setting = await self.get_inv_user_settings(user_info['reward_invite_for'])
                if (reward_setting and
                        (reward_setting['invitee_max_days'] < 0
                         or int(time.time()) - u['created_on'] <= datetime.timedelta(
                                    days=reward_setting['invitee_max_days']).total_seconds)
                        and (reward_setting['invitee_max_num'] < 0 or reward_setting[
                            'invitee_max_num'] >= times)):
                    await self.set_invite_order(
                        inv_uid=user_info['reward_invite_for'], payway=constants.REWARD_USER_CHARGE_PAYWAY,
                        uid=user_info['uid'], value=round(order['currency'] * reward_setting['inviter_rebate'], 2),
                        charge_value=order['currency'], charge_num=times, charge_orderid=order['orderid'])
            tz = datetime.timezone(datetime.timedelta(hours=8))  # UTC+8
            dt = datetime.datetime.fromtimestamp(order['created_on'], tz)
            dt_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            pay_method_str = pay_method or constants.PAY_METHODS.get(order.get('pay_method', 101),'Alipay')
            if pay_method_str == "ALipay":
                pay_method_str = "Alipay"
            if order['text'] =='from gpt302':
                name = "GPT302"
            else:
                name = "PROXY302"

            key = f"proxy302:charge:{order['orderid']}"
            email_key = f"proxy302_charge_email_times_{u['uid']}"
            await Tools.redis.incr(key)
            value = await Tools.redis.get(key)
            Tools.log.info(f"key: {key}, value: {value}")
            feishu = Tools.feishu2
            if name == "GPT302":
                feishu = Tools.feishu4
            if value in (1,b"1"):
                feishu.sendText(f"{name}:uid:{u['uid']} 用户 {u['email']+u['phone']} 充值了 ${amount} "
                                f"支付方式:{pay_method_str}\n"
                                f"本次是该用户第 {times} 次充值 \n"
                                f"创建时间为: {dt_str}")
                await Tools.redis.expire(key,60*5)
                await Tools.redis.incr(email_key,1)
                await Tools.redis.expire(email_key,60*10)
                await TUser.filter(uid=u['uid']).update(is_indebted=False)
            if value:=await Tools.redis.get(email_key):
                if value>=b"5" and pay_method_str not in ("Alipay",'Alipay'):
                    feishu.sendText(f"{name}: <at user_id=\"all\">所有人</at> 异常提示： uid:{u['uid']} 用户疑似信用卡盗刷,一小时内连续充值超过5笔，及时处理")



            band_ref = await TInviteRef.filter(uid=user_info['uid']).first().values("ref",'create_on')
            times = await self.user_change_times(order['uid'],band_ref.get('create_on',0))
            Tools.log.info(f"RECHARGE LOG: band_ref {band_ref} times {times}")
            Tools.log.info(f"RECHARGE LOG: reward_charge_limit {constants.PartnerShareConfig.reward_charge_limit}")
            # 4. partner_share返现邀请   https://www.apifox.cn/apidoc/shared-841d79ff-d905-47a3-b6e6-99b28ca8e0e5/api-41034477
            if user_info['created_on']>1709222400 and times <= constants.PartnerShareConfig.reward_charge_limit:
                ref = band_ref.get("ref") or user_info['from_ps_ref'] or user_info['ref']
                if u['email']:
                    email = u['email']
                    _email = email.split("@")[-1]
                    _email = email[:3] + "***@" + _email[0] + "**." + _email.split(".")[-1]
                else:
                    _email = u['phone'][5:]+ "***"
                if not _email:
                    _email = f"**{u['uid']}**"
                if self.is_gpt:
                    product_id = Tools.config.partner_share_gpt.product_id
                    conversion_token = Tools.config.partner_share_gpt.data_return_key
                else:
                    product_id = Tools.config.partner_share.product_id
                    conversion_token = Tools.config.partner_share.data_return_key
                params = {
                    'conversion_token': conversion_token,
                    'product_id': product_id,
                    'ref': ref,
                    'source_user_id': user_info['uid'],
                    'transaction_id': pay_order,
                    'total_money': order['currency'],
                    'start_time_plan': order['created_on'],
                    'cashback_mode': 2,
                    "conversion_type":3 if ref == "dZjFlX" else 2,
                    "event_name": "返佣20%",
                    'source_user_info': _email
                }
                async with aiohttp.ClientSession() as session:
                    resp = await session.get(constants.PartnerShareConfig.charge_req_url, params=params, timeout=5)
                    text = await resp.text()
                    Tools.log.info(f"RECHARGE LOG: partner_share charge api resp: {text}")
                    Tools.log.info(f"RECHARGE LOG: params: {params}")
                    Tools.log.info(f"RECHARGE LOG:charge_req_url: {constants.PartnerShareConfig.charge_req_url}")

                    resp.raise_for_status()
                    if json_resp["code"] != 200:
                        Tools.log.error("RECHARGE LOG: partner_share charge api res Error")

                        Tools.log.error("partner_share charge api res Error")
                    json_resp = await resp.json()
                #
                #     resp = requests.get(constants.PartnerShareConfig.charge_req_url, params=params, timeout=5)
                #     log.info(f"partner_share charge api resp: \n{resp.text}")
                #     resp.raise_for_status()
                #     if resp.json()['code'] != 200:
                #         log.error("partner_share charge api res Error")
                # except:
                #     log.error(traceback.format_exc())
            user_data['email'] = u['email']
            user_data['name'] = u['name']
            return order['orderid']

    async def set_order_info(self,pay_order: str, amount: float, currency_type: str = "", funding: str = "",
                   name: str = "", email: str = "", address1: str = "", address2: str = "",
                   city: str = "", state: str = "", country: str = ""):
        # t = int(time.time())
        # try:
        #     return db.mysql.execute("""
        #         INSERT INTO t_orders_info(created_on, modified_on, deleted_on, pay_order, amount, currency_type, funding,
        #         name, email, address1, address2, city, state, country)
        #         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        #     """, t, t, 0, pay_order, amount, currency_type, funding, name, email, address1, address2, city, state,
        #                             country)
        # except:
        #     return 0

        await TOrderInfo.create(pay_order=pay_order, amount=amount, currency_type=currency_type,
                                funding=funding, name=name, email=email, address1=address1, address2=address2, city=city, state=state, country=country)



class AppPayAliplayHandler(PaymentApiHandler):
    pay_method = 700
    async def get_rate(self):
        return 1
    @staticmethod
    async def verify_receipt(receipt, url="https://buy.itunes.apple.com/verifyReceipt"):
        # 准备请求数据
        data = json.dumps({"receipt-data": receipt})
        headers = {"Content-Type": "application/json"}

        async with aiohttp.ClientSession() as session:
            async with session.post(url, data=data, headers=headers) as response:
                # 发送POST请求

                # 确保请求成功
                response.raise_for_status()

                # 解析JSON响应
                result = await response.json()
                print(result)
                # 检查是否需要使用沙盒环境
                if result.get("status") == 21007:
                    return await AppPayAliplayHandler.verify_receipt(receipt, "https://sandbox.itunes.apple.com/verifyReceipt")

            return result

    async def call_back(self,request:Request,data="",uid=0):
        result = await self.verify_receipt(data)
        if result:
            contains = "charge_"
            product_id = result.get("receipt").get("in_app")[0].get("product_id")
            transaction_id = result.get("receipt").get("in_app")[0].get("transaction_id")
            ptc = product_id.split(".")[-1].replace("ptc", '')
            payway = await TPayway.filter(payway__contains=contains,price=int(ptc)).first()
            # orderid = await self.set_order(payway.price+payway.service_fee, transaction_id,
            #                                pay_method=constants.PAY_METHODS.get(self.pay_method, ""),
            #                                user_data={}, rate=await self.get_rate())
            await add_ready_ip_order(update_user_info=True,payment_id=700,
                                     user_id=uid, type="+", currency=0, currency_type="USD", text='apple',
                                     receive_currency=payway.price+payway.service_fee, payway=payway.payway, pay_order=transaction_id, is_inner=0,
                                     value=payway.pay_value, extra_value=payway.extra_value,
                                     status=1, checksum=True, valid=True)
            res = {"errCode": "00000000", "errMessage": "[成功]()"}
            return res

class ChinaGpayAliplayHandler(PaymentApiHandler):
    pay_method = 601
    transChannel = "ALIPAY_CN"
    payMode = "H5"
    currency = "USD"
    time_out = 60*10
    call_back_path='chinagpay_alipay'

    async def get_rate(self):
        return 1

    async def call_back(self,request:Request) -> dict:
        """
        支付后的回调函数
        """
        self.get_time("Aliplay call_back start")
        post_headers = request.headers
        signature = post_headers.get("Signature", "")
        post_body = await request.body()
        post_body = post_body.decode("utf-8")
        Tools.log.info(f"Alipay Log origin body {repr(post_body)} ")
        Tools.log.info(f"Alipay Log Headers {repr(post_headers)} ")
        # post_body = str(json.loads(post_body))

        # tmp_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7GbB4/HLoIrHQTOcEBavR7zR6quxeR6RQWwPghzWuDfXllOOOx6KVUWOf8vTAC1l6DCDtpYCyzdSthieBcKpCiEqOvGCYdE+aYEPzk6bLGWETlpUXVno9yrawkJEDJ/qUAp6ABSmsX1jQ1/Yze6DUGBRv9Q8h2kmvDCF0viDRlQIDAQAB"
        # print(post_body)

        # if not RSAPubCrypt(tmp_key).verify_sign(post_body, signature):
        if not RSAPubCrypt(Tools.config.alipay.public_key).verify_sign(post_body, signature):
            Tools.log.error(f"chinagpay_alipay 付款失败  签名验证不成功")
            return {
    "errCode" : "99999999",
    "errMessage" : "[失败]()"
}
        event = json.loads(post_body)
        if not (event.get('isSuccess', False) and 'data' in event and event.get('status', '').upper() == "SUCCESS"
                and event['data'].get("status").upper() == "SUCCESS"):
            Tools.log.error(f"chinagpay_alipay 付款失败  {event.get('errMessage', '')}")
            res = {
    "errCode" : "99999999",
    "errMessage" : "[失败]()"
}
        else:
            payment_order_id = event['data'].get("merchantOrderId")
            funding = event['data'].get("currency")
            currency_type = event['data'].get("payMethod")
            amount = int(float(event['data'].get("orderAmount")))
            user_data = {}
            # 回调成功之后操作用户数据
            orderid = await self.set_order(amount, payment_order_id, pay_method=constants.PAY_METHODS.get(self.pay_method, ""),
                                     user_data=user_data,rate=await self.get_rate())
            if orderid:
                await self.set_order_info(payment_order_id, amount=float(event['data'].get("orderAmount")), funding=funding,
                        currency_type=funding, name=user_data.get('name'), email=user_data.get('email'))
            res = {"errCode": "00000000","errMessage": "[成功]()"}
        self.get_time("Aliplay call_back end",extra=f" use {time.time()-self.start} s")
        return res


    async def createPayment(self, uid: int, payway:TPayway):
        self.get_time("Aliplay createPayment start")
        price = int((Decimal(payway.price) + Decimal(payway.service_fee)) * 100)
        product_name = payway.note if True else payway.en_note
        order_time = re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))
        webhook_url = f"https://{Tools.config.service_host}/api/webhook/{self.call_back_path}"
        redirect_url = self.redirect_url
        rate = await self.get_rate()
        data = {
            "accessType": "s2s",
            "carrierId": "P302",
            "charset": "utf-8",
            "currency": self.currency,
            "dcc": "dcc",
            "deviceChannel": "brower",
            "goodsName": product_name,
            "merchantId": Tools.config.alipay.merchant_id,
            "merchantOrderId": order_time,
            "merchantOrderTime": order_time[:14],
            "payMode": self.payMode,
            "merchantRegion": "HK",
            "redirectUrl": redirect_url,
            "notifyUrl": webhook_url,
            "signType": "RSA",
            "transAmt": f"{round(price * rate / 100, 2):.2f}",
            "transChannel": self.transChannel,
            "transTimeout": self.time_out,
            "transType": "PAY",
            "version": "2.0.0"
        }

        Tools.log.debug(data)
        headers = {
            'Content-Type': "application/json;charset=utf-8",
            'signature': RSAPrvCrypt(Tools.config.alipay.private_key).sign(json.dumps(data))
        }
        async with aiohttp.ClientSession() as session:
            try:
                resp = await session.post("https://mpgw.payallglobal.com/mp-gateway/api/mapi" + "/v1/payments/open/api/pay", timeout=30, json=data, headers=headers)
                # resp = await session.post("https://mpgw.chinagpay.com/mp-gateway/api/mapi" + "/v1/payments/open/api/pay", timeout=30, json=data, headers=headers)
            except asyncio.exceptions.TimeoutError as e:
                Tools.log.error(e)
                raise_execption(constants.TIME_OUT_ERROR)


        # resp = requests.post(constants.CHINAGPAY_BASE_URL + "/v1/payments/open/api/pay",
        #                      timeout=5, json=data, headers=headers)
            resp.raise_for_status()
            self.extra = res = await resp.json()
            self.extra['price'] = int(payway.price * rate)
            self.extra['order_ptc'] = payway.price

        Tools.log.debug(res)
        if not res.get("isSuccess", False) or res.get('status', 'FAIL') == "FAIL":
            raise_execption(constants.ERR_INTERNAL_ERROR)
        pay_url = res['data']['payMethodResponse']['termUrl']
        # payorder = res['data']['orderId']
        payorder = res['data']['merchantOrderId']
        orderid = await add_ready_ip_order(
            user_id=uid, type=payway.type, currency_type=payway.currency.lower(), currency=payway.price,receive_currency=payway.service_fee+payway.price,
            payway=payway.payway, pay_order=payorder, value=payway.pay_value,
            extra_value=payway.extra_value, pay_method=self.pay_method,text= 'from gpt302' if self.is_gpt else ''
        )
        self.get_time("Aliplay createPayment end",extra=f" use {time.time()-self.start} s")
        if orderid:
            return ({'to': pay_url})
        else:
            raise_execption(constants.ERR_INTERNAL_ERROR)

class WechatPayHandler(ChinaGpayAliplayHandler):
    pay_method = 602
    transChannel = "WEIXIN"
    payMode = "QRCODE"
    currency = "CNY"
    time_out = 60*10
    call_back_path='wechat_pay'


    async def get_rate(self):
        return 7.2

    async def build(self):
        await self.post(self.is_gpt)
        self.extra = {
            "url":self.extra['data']['payMethodResponse']['codeUrl'], # 微信链接
            "qr_code":"",   # 二维码图片
            "currency":self.extra['data']['currency'],  # 货币类型
            "order_id":self.extra['data']['merchantOrderId'],  # 订单号
            "price":self.extra['price'],  # 商品价格
            "fee":round(float(self.extra['data']['transAmt']) - self.extra['price'],2),  # 手续费
            "amount": self.extra['data']['transAmt'],  # 合计金额，商品价格+手续费
            "order_ptc":self.extra['order_ptc'],  # 原订单的金额（对应用户点击的充值套餐金额）
        }
        Tools.log.info(self.extra)
        path = random_string(16,uppercase=False)
        value = json.dumps(self.extra)
        await Tools.redis.setex(path,self.time_out,value)
        return path


    async def get_from_path(self,path):
        value = await Tools.redis.get(path)
        if not value:
            return
        data = json.loads(value.decode())
        wechat_pay_url = data.get("url")
        data['qr_code'] = text_to_qrcode_to_b64(wechat_pay_url)
        return data



class StripePaymentApiHandler(PaymentApiHandler):
    type = 2
    pay_method = 101
    pay_method = 101

    async def get_obj_from_id(self,customer_id,api_key):
        return stripe.Customer.retrieve(customer_id,api_key=api_key)    

    async def create_or_get_customer(self, api_key, uid):
        """创建或获取 Stripe Customer"""
        try:
            
            user_stripe = await TUserStripe.filter(uid=uid,is_gpt=self.is_gpt).first().values("stripe_customer_id")
            if user_stripe and user_stripe.get("stripe_customer_id"): return user_stripe.get("stripe_customer_id")
            
            user = await TUser.filter(uid=uid).first()
            # 创建新客户
            customer =  stripe.Customer.create(
                api_key=api_key,
                email=user.email,
                phone=user.phone,
                metadata={
                    'uid': str(uid),
                    'auto_renew': 'true',
                    'renew_threshold': '10.0'
                }
            )
            if await TUserStripe.filter(uid=uid,is_gpt=self.is_gpt).exists():
                await TUserStripe.filter(uid=uid,is_gpt=self.is_gpt).update(stripe_customer_id=customer.id)
            else:   
                await TUserStripe.create(uid=uid,stripe_customer_id=customer.id,is_gpt=self.is_gpt)
            return customer.id
        except Exception as e:
            Tools.log.error(f"Create/Get customer error: {str(e)}")
            return None

    # 创建 Stripe Checkout Session的绑卡页面
    async def create_portal_session(self):
        try:
            # 创建 Stripe Checkout Session
            api_key = Tools.config.stripe_gpt.sk if self.is_gpt else Tools.config.stripe.sk
            customer_id = await self.create_or_get_customer(api_key,self.uid)
            url = self.redirect_url_gpt if self.is_gpt else self.redirect_url
            Tools.log.debug(f"customer_id: {customer_id}")
            key = f"stripe_{self.uid}"
            session = stripe.checkout.Session.create(
                api_key=api_key,
                payment_method_types=['card'],
                mode='setup',  # 设置为setup模式，表示只绑卡不收费
                customer=customer_id,  # 替换为实际的客户ID
                success_url=("https://dash-api.302.ai" if self.is_gpt else "https://"+Tools.config.service_host)+f"/proxy/charges/stripe/callback/{key}",  # 绑卡成功后跳转
                cancel_url=url,    # 取消后跳转
            )
            Tools.log.debug(f"session: {session}")
            await Tools.redis.setex(key,600,session.id)
            # 重定向到Stripe页面
            return session.url
        except Exception as e:
            Tools.log.error(f"Payment success callback error: {str(e)}")

    async def band_call_back(self,session_id):
        try:
            value = await Tools.redis.get(session_id)
            if not value:
                return
            session_id = value.decode()
            # 获取 Checkout Session
            api_key = Tools.config.stripe_gpt.sk if self.is_gpt else Tools.config.stripe.sk
            session = stripe.checkout.Session.retrieve(session_id,api_key=api_key)
            # 获取客户ID
            setup_intent_id = session.setup_intent
            setup_intent = stripe.SetupIntent.retrieve(setup_intent_id,api_key=api_key)
            Tools.log.debug(f"setup_intent: {setup_intent}")
            # 获取客户ID
            payment_method_id = setup_intent.payment_method
            customer_id = session.customer
            await self.setup_payment_method(customer_id, payment_method_id, api_key)
            # 创建 PaymentIntent
        except Exception as e:
            Tools.log.error(f"Payment success callback error: {str(e)}")

    async def auto_renew_payment(self):
        """执行自动续费"""
        
            
            # 查找用户的 Customer
        infos = await TUserStripe.filter(enable=True).all()

        if not infos:
            return False
        for info in infos:
            try:
                uid = int(info.uid)
                is_gpt = info.is_gpt
                api_key = Tools.config.stripe_gpt.sk if is_gpt else Tools.config.stripe.sk
                obj = await self.get_obj_from_id(info.stripe_customer_id,api_key)
                u_info = await UserInfo.filter(uid=uid).first()
                Tools.log.info(f"u_info:{u_info.balance/1000},uid:{uid},info:{info.balance}")
                if u_info.balance/1000 >= info.balance:
                    continue
                # 创建 PaymentIntent
                amount = info.amount
                payment_intent = stripe.PaymentIntent.create(
                    api_key=api_key,
                    amount=int(amount * 100),  # 转换为分
                    currency='usd',
                    customer=info.stripe_customer_id,
                    payment_method=obj.invoice_settings.default_payment_method,
                    off_session=True,
                    confirm=True
                )
                
                if payment_intent.status == 'succeeded':
                    # 创建订单记录
                    orderid = await add_ready_ip_order(
                        update_user_info=True,
                        user_id=uid,
                        type='+',
                        currency_type='usd',
                        currency=amount,
                        payway='auto_renew',
                        pay_order=payment_intent.id,
                        value=int(amount * 1000),  # 转换为 PTC
                        receive_currency=amount,
                        extra_value=int(amount*0.1 * 1000) if amount >=200 else 0,
                        pay_method=101,
                        text="from gpt302" if is_gpt else '',
                        status=1,valid=1
                    )
                    
                    await TStripe.create(
                        orderid=orderid,
                        payment_id=payment_intent.id,
                        data=dict(payment_intent)
                    )
                    
                    
            except Exception as e:
                Tools.log.error(f"Auto renewal error: uid:{uid} {str(e)}")

    # 在支付成功回调中添加
    async def on_payment_intent_succeeded(self, event_object):
        try:
            payment_intent = event_object.data.object
            
            # 设置支付方式
            if payment_intent.customer and payment_intent.payment_method:
                api_key = Tools.config.stripe_gpt.sk if self.is_gpt else Tools.config.stripe.sk
                await self.setup_payment_method(
                    payment_intent.customer,
                    payment_intent.payment_method,
                    api_key
                )
            
            # 更新订单状态
            stripe_order = await TStripe.filter(payment_id=payment_intent.id).first()
            if stripe_order:
                await self.change_ip_order_status_by_pay_order(
                    stripe_order.orderid,
                    status=1,
                    valid=True
                )
                
        except Exception as e:
            Tools.log.error(f"Payment success callback error: {str(e)}")


    async def setup_payment_method(self, customer_id, payment_method_id, api_key):
        """设置支付方式"""
        try:
            # 将支付方式附加到客户
            stripe.PaymentMethod.attach(
                payment_method_id,
                customer=customer_id,
                api_key=api_key
            )
            
            # 设置为默认支付方式
            stripe.Customer.modify(
                customer_id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                },
                api_key=api_key
            )
            return True
        except Exception as e:
            Tools.log.error(f"Setup payment method error: {str(e)}")
            return False

    async def create_band_card(self,balance,amount,enable=1):
        if await TUserStripe.filter(uid=self.uid,is_gpt=self.is_gpt).exists():
            await TUserStripe.filter(uid=self.uid,is_gpt=self.is_gpt).update(balance=balance,enable=enable,amount=amount)
        else:
            await TUserStripe.create(uid=self.uid,balance=balance,enable=enable,amount=amount,is_gpt=self.is_gpt)
        
        return True

    async def delete_band_card(self):
        await TUserStripe.filter(uid=self.uid,is_gpt=self.is_gpt).delete()
        return True
    
    async def get_band_info(self):
        info = await TUserStripe.filter(uid=self.uid,is_gpt=self.is_gpt).first().values()
        if not info:
            return {}
        api_key = Tools.config.stripe_gpt.sk if self.is_gpt else Tools.config.stripe.sk
        obj = await self.get_obj_from_id(info.get("stripe_customer_id"),api_key)
        Tools.log.info(f"get_band_info obj:{obj}")
        if not obj or not obj.invoice_settings.default_payment_method:

            return {}
        return info or {}

            
    async def on_stripe_early_fraud_warning_created(self, event_object):
        """
        用户盗刷或者欺诈，需要拉黑用户，同时退款
        """

        payment_intent = event_object.get('payment_intent')
        if not payment_intent:
            return
        msg = f"订单异常\nStripe: charge.dispute.created\n" \
              f"订单号: {payment_intent}\n"
        order_obj:TIpOrders = await self.get_order_by_pay_order(payment_intent)

        # 把uid放入邮箱黑名单，封号
        # 把对应订单对应的uid对应的所有tokens都禁用，然后发邮件通知
        # 释放静态ip-按ip个数结算的ip提供给其他用户购买

        if order_obj:
            order_obj:dict = order_obj.to_dict()
            tz = datetime.timezone(datetime.timedelta(hours=8))  # UTC+8
            dt = datetime.datetime.fromtimestamp(order_obj['created_on'], tz)
            dt_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            msg += f"\n订单创建时间: {dt_str}\n金额: ${order_obj['currency']}\n用户充值总金额: ${order_obj['total_money']/1000}"
            user_obj = await self.get_user_by_uid(order_obj.get('user_id'))
            if user_obj  :
                msg += f"\n用户: {user_obj['email']}({user_obj.get('uid')})"
                if user_obj.get("email"):
                    is_first = await add_email_blacklist(user_obj['email'],"Stripe 订单 因涉嫌非法盗刷信用卡，您的账号在Proxy302已被禁用。如果有任何问题请点击&quot;Message us&quot;或者发邮件 ********************联系客服进行申诉。",
                                                         "Your account has been blocked on Proxy302 due to suspected illegal credit card fraud. If you have any questions, please click &quot;Message us&quot; <NAME_EMAIL> to contact customer service for appeal.", uid=order_obj.get('uid'))
                if not is_first:
                    msg += "\n(已把用户加入到黑名单)"

                else:
                    msg += "\n(黑名单中已存在此用户)"

            Tools.feishu3.sendText(msg)

    async def call_back(self,request:Request) -> dict:
        """
        回调逻辑
        """

        post_headers = request.headers
        # post_body = await request.body()
        # print(repr(post_body.decode("utf-8")))
        if is_from_gpt(request):
            self.is_gpt =True
            secret = Tools.config.stripe_gpt.secret
        else:
            self.is_gpt = False
            secret = Tools.config.stripe.secret

        post_body = await request.body()
        Tools.log.info(f"stripe Log origin body {repr(post_body)} ")
        Tools.log.info(f"stripe Log Headers {repr(post_headers)} ")
        post_body = post_body.decode("utf-8")
        stripe_signature = post_headers.get("Stripe-Signature", "")
        Tools.log.info(f"stripe Log stripe_signature {stripe_signature}")
        Tools.log.info(f"stripe Log secret {secret}")

        event = stripe.Webhook.construct_event(post_body, stripe_signature, secret)

        # event.type = "charge.succeeded"
        if event.type == "charge.succeeded":
            payment_method = event.data.object  # type: stripe.PaymentMethod
            payment_intent_id = payment_method.get("payment_intent")

            # 绑定卡
            # if band and payment_method.customer and payment_method.payment_method:
            #     api_key = Tools.config.stripe_gpt.sk if self.is_gpt else Tools.config.stripe.sk
            #     await self.setup_payment_method(
            #         payment_method.customer,
            #         payment_method.payment_method,
            #         api_key,
            #         pay_order=payment_intent_id
            #     )
            #     Tools.log.info("setup_payment_method")
            #     return True


            currency_type = payment_method.get("currency")
            if currency_type == 'cny':
                num = 7.2
                conn = Tortoise.get_connection('default')
                r = await conn.execute_query_dict("select probability from t_sell_settings where type ='usd_to_cny' ")
                if r:
                    num = r[0].get("probability")
                amount = payment_method.get("amount") // 100 // num
            elif currency_type == 'hkd':
                amount = payment_method.get("amount") // 100 // 8
            else:
                amount = payment_method.get("amount") // 100
            user_data = {}
            orderid = await self.set_order(amount, payment_intent_id,
                                     user_data=user_data)
            await TStripe.create(orderid=orderid, payment_id='', data=event.to_dict(),status=1)

            if orderid:
                user_data.get('email', '')
                card_details = payment_method.get('payment_method_details', {}).get('card', {})
                billing_details = payment_method.get('billing_details', {})
                address_details = billing_details.get('address', {})
                await self.set_order_info(payment_intent_id, amount=round(payment_method.get("amount") / 100, 2),
                               currency_type=currency_type, funding=f"{card_details.get('brand')}",
                               name=billing_details.get('name',user_data.get('name','')) if billing_details.get('name',user_data.get('name','')) else '', email=billing_details.get('email',user_data.get('email','')) if billing_details.get('email',user_data.get('email','')) else '',
                               country=address_details.get('country',"") if address_details.get('country',"") else '', state=address_details.get('state',"") if address_details.get('state',"") else '',
                               city=address_details.get('city',"") if address_details.get('city',"") else '', address1=address_details.get('line1',"") if address_details.get('line1',"") else '',
                               address2=address_details.get('line2',"") if address_details.get('line2',"") else '')
        elif event.type in ("radar.early_fraud_warning.created","charge.dispute.created"):
            await self.on_stripe_early_fraud_warning_created(event.data.object)
        # elif event.type == "charge.dispute.created":
        #     self.on_stripe_dispute_created(event.data.object)
        #     self.return_success()
        # else:
        #     self.return_success()

        return {}

    async def createPayment(self, uid:int, payway:TPayway):
        success_url, cancel_url = "success_url","cancel_url"
        num = 1
        currency = payway.currency.lower()
        payment_method_types = ['card']
        pay_method = 101
        if self.is_wechat_pay and self.is_cny:
            num = 7.2
            conn = Tortoise.get_connection('default')
            r = await conn.execute_query_dict("select probability from t_sell_settings where type ='usd_to_cny' ")
            if r:
                num = r[0].get("probability")
            currency = 'cny'
            payment_method_types = ['wechat_pay']
            pay_method = 103
        price = int(int((Decimal(payway.price) + Decimal(payway.service_fee)) * 100 )* num)
        product_name = payway.en_note
        random_num = random_string(12)
        Tools.log.debug(stripe.api_key)

        if self.is_gpt:
            api_key = Tools.config.stripe_gpt.sk
            image = "https://proxyblob.blob.core.windows.net/gpt/imgs/emails/img_v3_029r_b62baef2-eb30-4f64-9dc8-199c0ec21bfg.png"
            url = self.redirect_url_gpt
        else:
            api_key = Tools.config.stripe.sk
            image = "https://proxyblob.blob.core.windows.net/imgs/proxy302_logo.png"
            url = self.redirect_url
        expire_time = current_timestamp()+3*60*60
        Tools.log.debug("time "+str(expire_time))
        customer_id = await self.create_or_get_customer(api_key, uid)
        Tools.log.info(customer_id)
        checkout_session = stripe.checkout.Session.create(
            api_key=api_key,
            customer_email=self.email if self.email else None,
            stripe_version='2020-08-27',
            payment_method_types=payment_method_types,
            payment_method_options={
                'wechat_pay': {
                    'client': 'web'
                },
                'card': {
                # 'setup_future_usage': 'off_session'  # 允许后续自动扣款
            }
            },
            line_items=[
                {
                    'price_data': {
                        'currency': currency,
                        'unit_amount': price,
                        'product_data': {
                            'name': product_name,
                            'images': [image],
                        },
                    },
                    'quantity': 1,
                },
            ],
            expires_at=expire_time,
            mode='payment',
            success_url=url,
            cancel_url=None if self.extra.get("device_type") == "Android" else url
            
        )
            
        Tools.log.debug(checkout_session.__dict__)
        payment_intent_id = checkout_session.payment_intent
        orderid = await add_ready_ip_order(
            user_id=uid, type=payway.type, currency_type=payway.currency.lower(), currency=payway.price,
            payway=payway.payway, pay_order=payment_intent_id if payment_intent_id else '' , value=payway.pay_value,receive_currency=payway.service_fee+payway.price,
            extra_value=payway.extra_value, pay_method=pay_method,text='from gpt302' if self.is_gpt else ''
        )
        await TStripe.create(orderid=orderid,payment_id=checkout_session.id,data=dict(checkout_session))
        if orderid:
            await Tools.redis.hset(random_num, 'order_id', orderid)
            await Tools.redis.expire(random_num, 86400 * 1)  # 一天有效
            return {'session_id': checkout_session.id}


class StripeAliPay(StripePaymentApiHandler):
    pay_method = 101
    async def createPayment(self, uid:int, payway:TPayway):
        payment_method_types = ['alipay']
        num = 7.2
        conn = Tortoise.get_connection('default')
        r = await conn.execute_query_dict("select probability from t_sell_settings where type ='usd_to_cny' ")
        if r:
            num = r[0].get("probability")
        currency = 'cny'
        price = int(int((Decimal(payway.price) + Decimal(payway.service_fee)) * 100 )* num)
        product_name = payway.en_note
        random_num = random_string(12)
        Tools.log.debug(stripe.api_key)

        api_key = Tools.config.stripe_gpt.sk
        image = "https://proxyblob.blob.core.windows.net/gpt/imgs/emails/img_v3_029r_b62baef2-eb30-4f64-9dc8-199c0ec21bfg.png"
        url = self.redirect_url_gpt

        expire_time = current_timestamp()+3*60*60
        Tools.log.debug("time "+str(expire_time))
        checkout_session = stripe.checkout.Session.create(
            api_key=api_key,customer_email=self.email if self.email else None,
            stripe_version='2020-08-27',
            payment_method_types=payment_method_types,
            line_items=[
                {
                    'price_data': {
                        'currency': currency,
                        'unit_amount': price,
                        'product_data': {
                            'name': product_name,
                            'images': [image],
                        },
                    },
                    'quantity': 1,
                },
            ],
            expires_at=expire_time,
            mode='payment',
            success_url=url,
            cancel_url=url,
        )
        Tools.log.debug(checkout_session.__dict__)
        payment_intent_id = checkout_session.payment_intent
        orderid = await add_ready_ip_order(
            user_id=uid, type=payway.type, currency_type=payway.currency.lower(), currency=payway.price,
            payway=payway.payway, pay_order=payment_intent_id if payment_intent_id else '' , value=payway.pay_value,receive_currency=payway.service_fee+payway.price,
            extra_value=payway.extra_value, pay_method=self.pay_method,text='from gpt302' if self.is_gpt else ''
        )
        await TStripe.create(orderid=orderid,payment_id=checkout_session.id,data=dict(checkout_session))
        if orderid:
            await Tools.redis.hset(random_num, 'order_id', orderid)
            await Tools.redis.expire(random_num, 86400 * 1)  # 一天有效
            return {'session_id': checkout_session.id}




class EpayPayment(PaymentApiHandler):
    pay_method = 700

    @atomic("default")
    async def change_ip_order_status_by_pay_order(self, pay_order, receive_currency=0, status=1, valid=True, text=""):
        """
        更新订单状态以及用户余额

        """
        t = int(time.time())
        ip_order = await self.get_order_by_pay_order(pay_order)
        ip_order = ip_order.to_dict()
        diff = 0
        order_receive = ip_order['receive_currency']
        if receive_currency:
            diff = int(float(receive_currency)) - order_receive

        Tools.log.debug(ip_order)
        if not ip_order:
            raise HTTPException(400, f"not find {pay_order}")
        if ip_order['status'] != 0:
            Tools.log.error(f"ip order {pay_order}  ID:{ip_order['orderid']} has completed. ")
            raise HTTPException(400, f"order {pay_order}   has completed. ")
        ip_order['uid'] = ip_order['user_id']
        uid, amount, type = ip_order['uid'], ip_order['amount'], ip_order['type']
        if diff != 0:
            amount += diff * 1000
            order_receive += diff
            if diff > 0:
                fee = amount * 0.05 + 1000
                amount -= fee

        if type not in ('+', '-'):
            raise HTTPException(400, f"type error, not allow type={type}")
        last_order = await TIpOrders.filter(deleted_on=0, status__not=0, valid=True, user_id=uid).order_by(
            "-orderid").first().values("total_money", "balance")

        # last_order = db.mysql.get("""
        #     SELECT total_money, balance FROM t_ip_orders
        #     WHERE deleted_on = 0 AND uid = %s AND status != 0 AND valid = true ORDER BY orderid DESC LIMIT 1
        # """, uid)

        # 加锁，在流水脚本那边做判断，用户加锁期间跳过用户对应的记录
        key = "t_ip_orders:%s" % uid
        await Tools.redis.setex(key, 30, "")
        total_money, balance = 0, 0
        if last_order:
            if type == "+":
                total_money, balance = last_order['total_money'] + amount, last_order['balance'] + amount
            elif type == "-":
                total_money, balance = last_order['total_money'], last_order['balance'] - amount
        else:
            if type == "+":
                total_money, balance = amount, amount
            elif type == "-":
                total_money, balance = 0, -amount
        if balance < 0:
            raise HTTPException(400, "balance error")
        order_id = int(re.sub(r"-|:|\.| ", '', str(datetime.datetime.now()))) // 100
        conn = Tortoise.get_connection("default")
        await conn.execute_query("""UPDATE t_ip_orders SET orderid = %s, modified_on = %s,  
                status = %s, valid = %s, balance = %s, total_money = %s ,checksum = 2,receive_currency=%s,amount=%s 
                WHERE  deleted_on = 0 AND pay_order = %s """,
                                 [order_id, t, status, valid, balance, total_money, order_receive, amount, pay_order])
        # await TIpOrders.filter(pay_order=pay_order).update(orderid=order_id, modified_on=t, receive_currency=receive_currency, status=status, valid=valid,
        #                                                     balance=balance, total_money=total_money,text=text,checksum=2)
        await UserInfo.filter(uid=uid).update(balance=balance, total_balance=total_money,
                                              ex_balance=total_money - balance, modified_on=t)
        # db.mysql.execute("""
        #     UPDATE t_ip_orders SET orderid = %s, modified_on = %s,  receive_currency = %s,
        #     status = %s, valid = %s, balance = %s, total_money = %s
        #     WHERE  deleted_on = 0 AND pay_order = %s
        # """,order_id, t, receive_currency, status, valid, balance, total_money, pay_order)
        # db.mysql.execute("""
        #     UPDATE t_users_info SET balance = %s, total_balance = %s, ex_balance = %s, modified_on = %s WHERE uid = %s
        # """, balance, total_money, total_money - balance, t, uid)
        return ip_order
    async def post(self, is_gpt=False):
        if not self.uid:
            return
        payway_obj = await self._getPaywayObject()
        if not payway_obj:
            raise_execption("1")
        # if (self.get_cookie("IT", "") != "true" and payway_obj['tries_limit'] >= 0 and
        #         get_count_user_buy_payway(uid, payway_obj['payway']) >= payway_obj['tries_limit']):
        #     self.return_error(constants.ERR_CHARGED_LIMIT)
        #     return
        res = await self.createPayment(self.uid, payway_obj)

        return res

    async def call_back(self, request:Request, order_id="", uid=0, from_data_dict=None) -> dict:
        if from_data_dict is None:
            from_data_dict = {}
        ep = Epay.Epay(Tools.config.epay_conf.get("epay_account"), Tools.config.epay_conf.get("api_key"),
                       call_back_url='', host=Tools.config.epay_conf.get("host"))
        if from_data_dict:  # 这里是走回调逻辑的
            if check_sign(from_data_dict, ep.api_key, from_data_dict.pop("sign")):
                user_data = {}
                orderid = await self.set_order(from_data_dict.get("paymentAmount"), order_id,
                                               user_data=user_data, pay_method="EPAY")
                return True
        else:    # 这里是定时任务逻辑的
            values = await ep.check_order(order_id,uid=uid)
            if values:
                user_data = {}
                orderid = await self.set_order(values[1], order_id,
                                               user_data=user_data,pay_method="EPAY")
                return True


    async def createPayment(self, uid:int, payway:TPayway,is_zh=False):
        today =  datetime.datetime.now().strftime("%Y%m%d")
        payment_intent_id = "epay_"+today+random_string(12)
        orderid = await add_ready_ip_order(
            user_id=uid, type=payway.type, currency_type=payway.currency.lower(), currency=payway.price,
            payway=payway.payway, pay_order=payment_intent_id if payment_intent_id else '', value=payway.pay_value,
            receive_currency=payway.service_fee + payway.price,
            extra_value=payway.extra_value, pay_method=self.pay_method, text='from gpt302' if self.is_gpt else ''
        )
        callback_url = f"https://{Tools.config.service_host}/api/epay/webhook/{payment_intent_id}"
        success_url = f"https://{Tools.config.gpt_dashboard_host if self.is_gpt else Tools.config.dashboard_host}/charge"
        ep = Epay.Epay(Tools.config.epay_conf.get("epay_account"),Tools.config.epay_conf.get("api_key"),call_back_url=callback_url,host=Tools.config.epay_conf.get("host"))
        build_url = await ep.create_payment(payment_intent_id,amount=(payway.price + payway.service_fee),lang='cn' if is_zh else 'en'
                          ,from_name= "302.AI" if self.is_gpt else "PROXY302",suc_url=success_url,fail_url=success_url)
        return build_url

async def get_payway(payway_name:str):
    return await TPayway.filter(payway=payway_name).first()


class ReceiptApi():
    # 账单
    def __init__(self,uid,order_id):
        self.uid = uid
        self.order_id = order_id

    async def get_order_data(self) ->dict:
        """
         SELECT tu.uid, CAST(orderid AS CHAR) as orderid, tio.created_on, tu.name as user_name, tu.email as user_email, text,
        tio.currency, pay_order, pay_method, value, tio.extra_value, note, en_note, price, service_fee,tio.currency+service_fee as receive_currency  FROM t_ip_orders tio
        LEFT JOIN t_users tu on tio.uid = tu.uid
        LEFT JOIN t_payways tp on tio.payway = tp.payway
        WHERE orderid = %s AND status > 0 AND valid = 1 AND tio.deleted_on = 0

        """
        order = await TIpOrders.get(orderid=self.order_id,user_id=self.uid).select_related("user")\
            .values("user_id","orderid","created_on","user__name","user__email","text","currency","pay_order","pay_method","value"
                    ,"extra_value","payway")
        if not order:
            return {}
        payway = await get_payway(order["payway"])
        order['user_name'] = order.pop('user__name')
        order['user_email'] = order.pop('user__email')
        order['en_note'] = payway.en_note
        order['note'] = payway.note
        order['price'] = payway.price
        order['service_fee'] = payway.service_fee

        return order
    async def t_orders_info(self,pay_id) ->dict:
        info = await TOrderInfo.filter(deleted_on=0,id=pay_id).first()
        if info:
            return info.to_dict()


    async def get_order(self):
        order = await self.get_order_data()
        if not order:
            return
        data = {
            'order_id': order['orderid'],
            'order_created_on': order['created_on'],
            'pay_date': order['created_on'],
            'user_name': order['user_name'],
            'user_email': order['user_email'],
            'pay_name': order['user_name'],
            'pay_email': order['user_email'],
            'pay_order': order['pay_order'],
            'note': order['en_note'],
            'price': "{:.2f}".format(order['price']),
            'service_fee': "{:.2f}".format(order['service_fee']),
            'currency': "{:.2f}".format(Decimal(order['price']) + Decimal(order['service_fee'])),  # "{:.2f}".format(order['currency']),
            'extra_value': order['extra_value'],
            'pay_price': "{:.2f}".format(Decimal(order['price']) + Decimal(order['service_fee'])), # "{:.2f}".format(order['receive_currency']),
            'pay_method': constants.PAY_METHODS.get(order['pay_method'], "-"),
            'funding': constants.PAY_METHODS.get(order['pay_method'], "")
        }
        if order['text']:
            data['pay_order'] += f"({order['text']})"
        order_info = await self.t_orders_info(order['pay_order'])
        if order_info :
            data.update({
                'pay_date': order_info['created_on'],
                'pay_name': order_info['name'],
                'pay_email': order_info['email'],
                'pay_price': "{:.2f}".format(order_info['amount']),
                'funding': order_info['funding'],
                'address1': order_info['address1'],
                'address2': order_info['address2'],
                'country': order_info['country'],
                'state': order_info['state'],
                'city': order_info['city']
            })
        return data

def create_secret(params:dict,key:str):
    import hashlib
    
    # 获取参数
    user_id = str(params.get('user_id', ''))
    money = "{:.2f}".format(float(params.get('money', 0)))
    withdrawal_id = str(params.get('withdrawal_id', ''))
    
    # 密钥
    
    # 生成签名字符串
    sign_str = user_id + money + withdrawal_id + key
    
    # 计算SHA256哈希
    sha256 = hashlib.sha256()
    sha256.update(sign_str.encode('utf-8'))
    calculated_secret = sha256.hexdigest()
    return calculated_secret

def check_secret(secret, params,secret_key):
    """
    验证授权码是否正确
    
    Args:
        secret: 授权码
        params: 包含用户ID、金额、提现ID的参数字典
    
    Returns:
        bool: 授权码验证是否通过
    """
    calculated_secret = create_secret(params=params,key=secret_key)
    return secret == calculated_secret

# 通知第三方
async def notify_partner(user_id,money,withdrawal_id,is_gpt=False):
    url = "https://api.partnershare.net/partner/notify/withdrawal-check"
    data = {
        "user_id": user_id,
        "money": "{:.2f}".format(float(money)),
        "withdrawal_id": withdrawal_id,
        "status": 1
    }
    if is_gpt:
        secret_key = Tools.config.partner_share_gpt.user_integration_key
    else:
        secret_key = Tools.config.partner_share.user_integration_key
    secret = create_secret(params=data,key=secret_key)
    data["secret"] = secret
    async with aiohttp.ClientSession() as session:
        async with session.get(url, params=data) as response:
            return await response.text()
    

async def charge_invite_for_ptc(user_id,money,withdrawal_id,secret,is_gpt=False):
    key = Tools.config.partner_share_gpt.user_integration_key if is_gpt else Tools.config.partner_share.user_integration_key
    params = {"user_id":user_id,"money":money,"withdrawal_id":withdrawal_id}
    Tools.log.info(f"charge_invite_for_ptc:params:{params},secret:{secret},key:{key}")
    if not check_secret(secret,params,key):
        Tools.log.error(f"charge_invite_for_ptc:secret is not correct")

        return
    user = await TUser.filter(uid=user_id).first()
    if not user:
        return
    
    await add_ready_ip_order(update_user_info=True,
                             user_id=user_id, type="+", currency=0, currency_type="USD", text="from invite",
                             receive_currency=0, payway="invite", pay_order=withdrawal_id, is_inner=False,
                             value=0, extra_value=money*1000,
                             status=1, checksum=True, valid=True)
    await notify_partner(user_id,money,withdrawal_id,is_gpt)
    return True

async def refund_ai_for_ptc():
    from models.db.gpt import TRefundLog
    logs = await TRefundLog.filter(status=0).all()
    for log in logs:
        await add_ready_ip_order(update_user_info=True,
                                 user_id=log.uid, type="+", currency=0, currency_type="USD", text="from gpt302",
                                 receive_currency=0, payway="refund_ai", pay_order=log.log_id, is_inner=False,
                                 value=0, extra_value=log.refund_amount*1000,
                                 status=1, checksum=True, valid=True)
        await TRefundLog.filter(id=log.id).update(status=1)


async def gift_for_ptc(wjId,uid,extra_value=1000,payway="gift",ip="",band_phone=False,is_zh=True):
    conn = connections.get('default')
    user = await TUser.filter(uid=uid).first()
    if not user:
        return
    email_end = user.email.split("@")[-1] if user.email else ''
    if (ip and Tools.redis.hget("STATISTICAL_GIFT_BLACK_IP",ip)) or Tools.redis.hget("STATISTICAL_GIFT_BLACK_EMAIL_END",email_end):
        return
    # 判断常用邮箱后缀,在常用缓存里的就不再统计
    if email_end and not Tools.redis.hget("STATISTICAL_GIFT_EMAIL_END",email_end):
        Tools.redis.hset("STATISTICAL_GIFT_EMAIL_END",email_end,1)
        key = f"STATISTICAL_GIFT_{email_end}"
        if await Tools.redis.exists(key):
            await Tools.redis.incr(key)
            value = await Tools.redis.get(key)
            if value == b'20':
                Tools.feishu1.sendText("====当前后缀邮箱领取ptc异常,24小时内计数达到20次，已停止当前邮箱赠送ptc====\n"
                                   f"注册IP：{ip}，用户邮箱：{user.email}，请核实是否薅羊毛<at user_id=\"all\">所有人</at>")
            if value >= b'20':
                print(33)
                return

        else:
            await Tools.redis.incr(key)
            await Tools.redis.expire(f"STATISTICAL_GIFT{email_end}",24*3600)
    if not user.phone:
        return
    if user.phone and user.phone.startswith("+86192"):
        return

    record = await conn.execute_query_dict("select * from myAdmin_submit where uid=%s limit 1", [int(uid)])
    if record or band_phone:
        wj_record = await conn.execute_query_dict("select platform from myAdmin_wj where id=%s limit 1", [record[0].get("wjId")])
        platform = wj_record[0].get("platform")
        get_ptc = record[0].get("get_ptc")
        get_ptc = bool(get_ptc)
        if get_ptc:
            return
        key = f"gift_for_ptc_{ip}"
        value = await Tools.redis.get(key)
        if value :
            return
        await Tools.redis.incr(key)
        await Tools.redis.expire(key,60*60*24)
        text = '' if platform=='proxy302' else 'from gpt302'
        await add_ready_ip_order(update_user_info=True,
                                 user_id=uid, type="+", currency=0, currency_type="USD",text= text,
                                 receive_currency=0, payway=payway, pay_order="", is_inner=False,
                                 value=0, extra_value=extra_value,
                                 status=1, checksum=True, valid=True)
        await TUser.filter(uid=uid).update(is_indebted=False)
        await conn.execute_query("update myAdmin_submit set get_ptc=1 where id=%s", record[0].get("id"))



async def kason_gift_for_ptc(file):
    import pandas as pd
    import io
    import chardet
    import asyncio
    from itertools import islice
    
    result = chardet.detect(file)
    encoding = result['encoding']
    file_obj = io.BytesIO(file)

    df = pd.read_excel(file_obj).fillna(0)
    fail_list = []

    async def process_row(row):
        phone = row['手机号']
        u = await TUser.filter(phone="+86"+str(int(phone))).first()
        if u:
            if await TIpOrders.filter(user_id=u.uid,payway='gift',extra_value=4000).exists():
                return None
            await add_ready_ip_order(update_user_info=True,
                                user_id=u.uid, type="+", currency=0, currency_type="USD",text= "from gpt302",
                                receive_currency=0, payway="gift", pay_order="", is_inner=False,
                                value=0, extra_value=4000,
                                status=1, checksum=True, valid=True)
            return None
        else:
            Tools.log.info(f"手机号不存在，手机号是：{phone}")
            return {"手机号":phone,"uid":row['用户ID']}

    # 每批处理10条记录
    batch_size = 20
    rows = df.to_dict('records')
    
    for i in range(0, len(rows), batch_size):
        batch = rows[i:i + batch_size]
        # 并发执行当前批次
        results = await asyncio.gather(*[process_row(row) for row in batch])
        # 过滤掉None值，只保留失败的记录
        fail_list.extend([r for r in results if r is not None])

    return fail_list

async def sign_in_for_ptc(uid,payway="sign_in",extra_value=1000,text=''):
    key = f"sign_in_for_ptc_{uid}"
    if await Tools.redis.exists(key):
        return False
    await Tools.redis.setex(key,60*60*24,'1')
    await add_ready_ip_order(update_user_info=True,
                             user_id=uid, type="+", currency=0, currency_type="USD", text=text,
                             receive_currency=0, payway=payway, pay_order="", is_inner=True,
                             value=0, extra_value=extra_value,
                             status=1, checksum=True, valid=True)
    return True


# iproyal api操作
token = "NhuAp6ycvcodhcAHPFIdRtz3uIrFRSJVTrrLi5M0s32ZTzdaXQ7Cs9jObL8O"
headers = {"X-Access-Token": token, "Content-Type": "application/json"}

#todo 查一个创建订单没接入
dict_api = {

    "get_order_info":("get","https://apid.iproyal.com/v1/reseller/orders/{order_id}"),   # 获取订单信息
    "get_order_info_list":("get","https://apid.iproyal.com/v1/reseller/orders"), # 获取多个订单的信息
    "extend_order":("post","https://apid.iproyal.com/v1/reseller/orders/{order_id}/extend"),  # 续费订单
    "buy_order":("post",'https://apid.iproyal.com/v1/reseller/orders'),  # 购买订单
    "product_msg":("get",'https://apid.iproyal.com/v1/reseller/products'),  # 商品信息
    "user_balance":("get","https://apid.iproyal.com/v1/reseller/balance"),  # 用户余额

}


async def get_iproyal_user_balance():
    """
    return {"balance": 1000.0, "currency": "USD", "currencySymbol": "$", "currencyCode": "USD"}
    """
    async with aiohttp.ClientSession() as session:
        balance_url = dict_api.get("user_balance")
        res = await session.request(balance_url[0], balance_url[1], headers=headers)
        balance = await res.text()
        Tools.log.info(f"get_iproyal_user_balance:{balance}")

        return float(balance)

async def get_iproyal_order_info(order_id,):
    """
    return
    {"id":9002847,"productName":"Static Residential","planName":"30 Days","status":"confirmed","location":"United States"
    ,"quantity":1,"questionAnswers":[],"productInfo":"**************:12323:14a6a30701403:a6ed23cb37","price":"$4.00","expireDate":"2024-01-13 00:00:00"}

    """
    async with aiohttp.ClientSession() as session:
        order_url = dict_api.get("get_order_info")
        res =  await session.request(order_url[0],order_url[1].format(order_id=order_id),headers=headers)
        msg = await res.text()
        Tools.log.info(f"get_iproyal_order_info:{msg}")
        if msg == '"Order not found"':
            return None

        return await res.json()


async def get_iproyal_order_info_list(order_ids:list,extra:dict):
    async with aiohttp.ClientSession() as session:
        order_url = dict_api.get("get_order_info_list")
        order_ids_string = '&'.join([f"order_ids[]={id}" for id in order_ids])
        for k,v in extra.items():
            order_ids_string += f"&{k}={v}"
        res =  await session.request(order_url[0],order_url[1]+f"?{order_ids_string}",headers=headers)
        msg = await res.text()
        Tools.log.debug(f"get:{msg}")
        if msg == '"Order not found"':
            return None

        return await res.json()


async def get_iproyal_order_proxy_info():
    conn = connections.get('default')
    records = await conn.execute_query_dict("""
    select gc.id,gc.name,gc.code,il.location_id,count(distinct tr.id) inventory
    ,coalesce(round(sum(cost)/1000,2),0) as cost,coalesce(round(sum(traffic_usage) /1000/1000/1000,2),0) as traffic_amount 
    from t_iproyal_loacations il left join t_geo_country gc on gc.id=il.country_id   left join t_traffic_ips  tr on tr.country_id=gc.id and tr.la_id = 99  left join t_user_traffic_ips ti  on ti.ti_id=tr.id left join t_user_tokens using(pi_id)    group by gc.id,gc.name,gc.code,il.location_id

    """)
    async with aiohttp.ClientSession() as session:
        url = dict_api.get("product_msg")
        res = await session.request(url[0], url[1], headers=headers)
        data =  await res.text()
        Tools.log.debug(data)
        data =  await res.json()
    msg_dict = {}
    for i in data.get("data"):
        if i.get("id")==9:
            msg_dict = {k.get("id"):k.get("out_of_stock") for k in i.get("locations")}
    for record in records:
        record['out_of_stock'] = msg_dict.get(record['location_id'])
    # return records
    return suc_data(data={"records":records}).dict()

@cache()
async def get_iproyal_product_plans() ->dict:
    async with aiohttp.ClientSession() as session:
        url = dict_api.get("product_msg")
        res = await session.request(url[0], url[1], headers=headers)
        resp =  await res.json()

        for i in resp.get("data"):
            if i.get("id")==9:
                plain = {k.get("name"):k for k in i.get("plans")}
                plain['product_id'] = i.get("id")
                return plain

async def extend_iproyal_order(order_id):
    plans = await get_iproyal_product_plans()
    order_info = await get_iproyal_order_info(order_id)
    plan_name = order_info.get("plan_name")
    plan_id = plans.get(plan_name).get("id")
    kwargs = {"data":{"product_plan_id":plan_id}}

    async with aiohttp.ClientSession() as session:
        order_url = dict_api.get("extend_order")
        conn = connections.get('default')
        res = await session.request(order_url[0], order_url[1].format(order_id=order_id), headers=headers,data=kwargs.get("data"))
        Tools.log.info(res)
        data =  await res.json()
        await TIpRoyalRenewLog.create(order_id=order_id ,resp=data)
        return data
    
async def iproyal_to_mysql(upload_file =None,la_id: int = 99,proxy_line="",network_type="http"):
    Tools.log.info(proxy_line)
    async def get_area( proxy_url):
        Tools.log.info("execute get_area")
        async with httpx.AsyncClient(proxies ={"all://":proxy_url}) as client:
            Tools.log.info(proxy_url)
            resp = await client.get(f"https://lumtest.com/myip.json", timeout=5)
            Tools.log.info(resp.text)
            context_json = resp.json()
            ip = context_json.get("ip")
            country_code = context_json.get("country")
            city_name = context_json.get('geo').get("region_name")
        state_name = context_json.get('geo').get("city")
        country = (await TGeoCountry.filter(code=country_code).first().values("id")) or {}
        city = (await TGeoCity.filter(name=city_name).first().values("id")) or {}
        state = (await TGeoState.filter(name=state_name).first().values("id")) or {}
        return country.get("id", 0), city.get("id", 0), state.get("id", 0), ip

    async def to_mysql(proxy_line: str):
        host, port, username, password = proxy_line.split(":")
        if network_type == "http":
            proxy = f"http://{username}:{password}@{host}:{port}"
        else:
            proxy = f"socks5://{username}:{password}@{host}:{port}"
        country_id, city_id, state_id, ip = await get_area(proxy)
        ip = ip or host
        if not ip:
            return
        ip_int = ip2int(ip)
        ip_hex = ip2hex(ip)
        traffic_id = await TTrafficIps.filter(ip_int=ip_int).first()
        if traffic_id: return traffic_id
        return await TTrafficIps.create(ip=ip, ip_int=ip_int, ip_hex=ip_hex, country_id=country_id, city_id=city_id,
                                           state_id=state_id,
                                           la_id=la_id, username=username, password=password, host=host, port=int(port),
                                           check_proxy=proxy)

    traffic_id = 0
    if proxy_line:
        return await to_mysql(proxy_line)
    if upload_file:
        for ip in upload_file.file.readlines():
            line = ip.decode().strip("\n").strip("\r")
            await to_mysql(line)

    return traffic_id

async def back_to_mysql(iproyal_order_id,order_obj_id,need_await=True):
    msg = {}
    msg = await get_iproyal_order_info(iproyal_order_id)
    if not msg:
        Tools.log.error(f"iproyal_order_id:{iproyal_order_id}")
    proxies = msg.get("proxy_data",{}).get("proxies")
    Tools.log.info(proxies)
    if proxies:
        for proxy in proxies:
            #host, port, username, password 
            proxy = f"{proxy.get('ip')}:12323:{proxy.get('username')}:{proxy.get('password')}"
            traffic = await iproyal_to_mysql(proxy_line=proxy,network_type="http")
        # proxy = f"{proxy.get('ip')}:12324:{proxy.get('username')}:{proxy.get('password')}"
        # traffic2 = await iproyal_to_mysql(proxy_line=proxy,network_type="socks5")
    else:
        traffic = None

    if traffic:
        await TIproyalOrder.filter(id=order_obj_id).update(traffic_id=traffic.id)

async def buy_iproyal_order(country_id,to_traffic=True,proxy='http://brd-customer-hl_3c2c92d3-zone-p_static_traffic_1:<EMAIL>:33335',is_all_ip=False):
    plans = await get_iproyal_product_plans()
    Tools.log.debug(plans)
    # plans: {'30 Days': {'id': 22, 'name': '30 Days', 'price': 4, 'min_quantity': 1, 'max_quantity': 130000}, '1 Day (24 hours)': {'id': 23, 'name': '1 Day (24 hours)', 'price': 2, 'min_quantity': 1, 'max_quantity': 130000}, '60 Days': {'id': 24, 'name': '60 Days', 'price': 8, 'min_quantity': 1, 'max_quantity': 130000}, '90 Days': {'id': 25, 'name': '90 Days', 'price': 12, 'min_quantity': 1, 'max_quantity': 130000}, 'product_id': 9}


    order_url = dict_api.get("buy_order")
    location = await TIproyalLocation.filter(country_id=country_id).first().values("location_id")
    if not location:
        return fail_data()
    location_id = location.get("location_id")
    plan_name = "30 Days"
    plan_id = plans.get(plan_name).get("id")
    price = plans.get(plan_name).get("price")
    data = {
    'product_id': plans.get("product_id"),
    'product_plan_id': plan_id,
    'product_location_id': location_id,
    'quantity': 2,
    'coupon_code': 'adswave',
    'auto_extend': False,
    }
    async with aiohttp.ClientSession() as session:
        Tools.log.debug(f"购买iproyal订单: url:{order_url[1]},data:{data}")
        res = await session.request(order_url[0], order_url[1],json=data, headers=headers)
        text = await res.text()
        Tools.log.debug(f"购买iproyal订单: text:{text}")
        data = await res.json()
        Tools.log.debug(f"购买iproyal订单: data:{data}")
        # data: {'id': 49952623, 'note': None, 'product_name': 'ISP (Static Residential)', 'expire_date': '-', 'plan_name': '30 Days', 'status': 'in-progress', 'location': 'United States', 'quantity': 2, 'questions_answers': [], 'proxy_data': {'ports': {'socks5': 12324, 'http|https': 12323}, 'proxies': []}, 'auto_extend_settings': {'payment_type': 'balance', 'is_enabled': True, 'card_id': None}, 'extended_history': []}
    # data =  {'id': 9251908, 'productName': 'Static Residential', 'planName': '30 Days', 'status': 'in-progress', 'location': 'United States', 'quantity': 1, 'questionAnswers': [], 'productInfo': '', 'price': '$4.00'}
    # data =  {"id":9259549,"productName":"Static Residential","planName":"30 Days","status":"confirmed","location":"United States","quantity":1,"questionAnswers":[],"productInfo":"*************:12323:14a00aaaed01c:35f6c95d6d","price":"$4.00","expireDate":"2024-01-17 00:00:00"}
    # data =  {'id': 9259544, 'productName': 'Static Residential', 'planName': '30 Days', 'status': 'in-progress', 'location': 'United States', 'quantity': 1, 'questionAnswers': [], 'productInfo': '', 'price': '$4.00'}
    orderid = await add_ready_ip_order(
        user_id=0, type='-', currency_type='iproyal_buy', currency=price,
        receive_currency=price,
        payway="buy_iproyal_order", pay_order=data.get("id"), value=0,
        extra_value=0, pay_method=1,status=0
    )
    # 保存iproyal订单信息到数据库 这里创建了一条空的记录，需要后续更新
    Tools.log.debug(f"购买iproyal订单: orderid:{orderid}")
    # 创建记录了
    

    async def save_to_proxy(iproyal_order_id,try_times=5):
        await asyncio.sleep(10)
        msg = await get_iproyal_order_info(iproyal_order_id)
        if not msg:
            Tools.log.error(f"iproyal_order_id:{iproyal_order_id}")
        proxies = msg.get("proxy_data",{}).get("proxies")
        Tools.log.info(proxies)

        if not proxies:
            return await save_to_proxy(iproyal_order_id,try_times-1)
        
        Tools.log.info(msg)
        ps_id = 11
        port = 12324
        source = 'iproyal_residential'
        start = 1
        if to_traffic:
            start = 0

        for proxy in proxies[start:]:
            #host, port, username, password 
            user_name = proxy.get('username')
            password = proxy.get('password')
            ip = proxy.get('ip')
            proxy = f"{proxy.get('ip')}:12323:{proxy.get('username')}:{proxy.get('password')}"
            traffic_id = 0
            pi_id = 0
            if to_traffic:
                traffic = await iproyal_to_mysql(proxy_line=proxy,network_type="http")
                traffic_id = traffic.id
            else:
                traffic_id = 0
                ip_obj = await TIps.filter(ip = ip).first()
                if not ip_obj:
                    ip_obj = await TIps.create(ip_addr=ip2int(ip), ip_address=ip2hex(ip),ip=ip)
                pm_obj = await TProxyMachines.filter(ip_id=ip_obj.id,ps_id = ps_id).first()
                if not pm_obj:
                    pm_obj = await TProxyMachines.create(ip_id=ip_obj.id, forward_port_start=port, forward_port_end=port,ps_id = ps_id)
                pip = await TProxyIp.filter(username=user_name, password=password, source=source,ip_id=ip_obj.id).first()

                if not pip:
                    pip = await TProxyIp.create(username=user_name, password=password, source=source, forward_port=port,port=port, ip_id=ip_obj.id,country_id = country_id,
                                        pm_id=pm_obj.id, online=True, health=True, status=True)
                else:
                    await TProxyIp.filter(id=pip.id).update(online=True, health=True, status=True,source=source)
                pi_id = pip.id
            order_obj = await TIproyalOrder.update_or_create(
                    order_id=iproyal_order_id,
                    pi_id=pi_id,traffic_id=traffic_id,
                    pay_order_id=orderid,
                    location_id=location_id,
                    expired_on = data.get("expire_date",""), 
                    status = 1,
                    expired=0
                )
        return
            

    # if to_traffic:
        # asyncio.ensure_future(back_to_mysql(data.get("id"),order_obj[0].id))
    asyncio.create_task(save_to_proxy(data.get("id")))
    return suc_data(data={"order_id":orderid,"data":data})



async def check_buy_iproyal_order():
    """
    检查iproyal订单，没有绑定traffic_id  的记录，尝试绑定
    """
    order_list = await TIproyalOrder.filter(traffic_id=0,pi_id=0,id__gt=50,expired=0).values("order_id","id")
    for order in order_list:
        try:
            await back_to_mysql(order.get("order_id"), order.get("id"),need_await=True)
        except asyncio.exceptions.TimeoutError as e:
            Tools.log.error(e)
        except Exception as e:
            Tools.log.error(e)

async def fetch_order_info(order_ids, extra):
    try:
        result = await get_iproyal_order_info_list(order_ids, extra=extra)
        Tools.log.info(result)
        return result
    except Exception as e:
        Tools.log.error(f"Error fetching order info: {e}")
        return {}


async def auto_renew_iproyal_order():
    from tortoise.functions import Max
    # query = TTrafficIps.filter(la_id=99).values("id")
    before_date = (datetime.datetime.now()+datetime.timedelta(days=3)).strftime("%Y-%m-%d")
    start_date = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
    order_ids = await TIproyalOrder.filter(expired_on__gt=start_date,expired_on__lt=before_date).distinct().values("order_id")
    order_ids2 = await TIproyalOrder.filter(expired=0,expired_on__in=['','-']).distinct().values("order_id")
    order_ids.extend(order_ids2)
    order_ids = list(set([o.get("order_id") for o in order_ids]))
    Tools.log.info(f"iproyal续费任务：{order_ids}")
    
    suc_ids = []
    fail_ids = []
    msg = []
    async def get_status(order_info):

        status = order_info.get("status")
        if status == 'expired':
            expired = 1
        elif status == 'confirmed':
            expired = 0
        else:
            expired = -1  # 异常状态

        if expired == 1:
            proxy_list =  await TIproyalOrder.filter(order_id=order_info.get("id")).values("traffic_id",'pi_id')

            for p in proxy_list:
                if p.get("pi_id")!= 0:
                    print(p.get("pi_id"))
                    await TProxyIp.filter(id = p.get("pi_id")).update(health=0)
                if p.get("traffic_id") !=0:
                    await TTrafficIps.filter(id = p.get("traffic_id")).update(is_health=0)
        return expired

    def get_expired_on(order_info):
        expireDate = order_info.get("expire_date")
        if expireDate == '-':
            extended_history = order_info.get("extended_history")[-1]
            expireDate = extended_history.get("expired_at")
            expireDate = expireDate.replace("T"," ").replace('Z','').replace('.000000','')
        return expireDate

    async def process_order(order):
        order_id = order
        lock_key = f"iproyal_order_lock_{order_id}"
        
        # 使用Redis的原子操作设置锁，确保只有一个进程能获取锁
        lock_acquired = await Tools.redis.set(lock_key, "1", nx=True, ex=300)
        if not lock_acquired:
            return
        
        try:
            order_info = await get_iproyal_order_info(order_id)
            if not order_info:
                Tools.log.error(f"iproyal续费任务：无法获取订单信息: {order_id}")
                return
                
            expireDate = get_expired_on(order_info)
            if not expireDate or not re.match(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}", expireDate):
                Tools.log.error(f"iproyal续费任务：订单过期日期格式错误: {expireDate}, 订单ID: {order_id}")
                return
                
            expire_date = datetime.datetime.strptime(expireDate, "%Y-%m-%d %H:%M:%S")
            expired = await get_status(order_info)
            await TIproyalOrder.filter(order_id=order_id).update(expired=expired, expired_on=expire_date)
            now_date = datetime.datetime.now()

            total = (expire_date - now_date).total_seconds()
            Tools.log.info(f"iproyal续费任务：订单 {order_id} 剩余时间：{total}秒")
            if total > 3600*24*3:
                Tools.log.info(f"iproyal续费任务：订单 {order_id} 剩余时间大于3天，跳过续费")
                return
            if total < 3600*24*3 and total > 0:
                # 检查是否已经在最近24小时内续费过
                renew_key = f"iproyal_renewed_{order_id}"
                if await Tools.redis.exists(renew_key):
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 最近已续费，跳过")
                    return
                    
                try:
                    data = await extend_iproyal_order(order_id)
                    # 设置续费标记，24小时内不再续费
                    await Tools.redis.setex(renew_key, 86400, "1")
                    
                    order_info = await get_iproyal_order_info(order_id)
                    expired = await get_status(order_info)
                    expire_date = get_expired_on(order_info)
                    status = order_info.get("status")
                    await TIproyalOrder.filter(order_id=order_id).update(expired=expired, expired_on=expire_date)
                    if expired != 0:
                        Tools.feishu3.send_msg(f"iproyal续费任务：续费失败：{order_id}，原过期时间：{expireDate}，续费后时间：{expire_date}，订单状态：{status}")
                    msg.append(f"iproyal续费任务：续费成功：{order_id}，原过期时间：{expireDate}，续费后时间：{expire_date}，订单状态：{expired}\n")
                    suc_ids.append(order_id)
                    
                    return True
                except Exception as e:
                    fail_ids.append(order_id)
                    Tools.log.error(f"iproyal续费任务：续费订单 {order_id} 失败: {str(e)}")
        except Exception as e:
            Tools.log.error(f"iproyal续费任务：处理订单 {order_id} 时发生错误: {str(e)}")
        finally:
            # 确保锁被释放
            await Tools.redis.delete(lock_key)

    # 创建信号量限制并发数为10
    # sem = asyncio.Semaphore(3)

    # async def process_with_semaphore(order):
    #     async with sem:
    #         await process_order(order)

    async def _check_token_auto_renew(proxy_id:int):
        try:
            from models.db.proxy import TStaticIpRenew
            tokens = await TUserToken.filter(pi_id=proxy_id,expired=0).values("id")
            token_id_list = [token.get("id") for token in tokens]
            if not token_id_list:
                return False
            has_renew = await TStaticIpRenew.filter(created_by=-1, is_auto_renew=1, token_id__in=token_id_list).first()
            if has_renew:
                return True
            return False
        except Exception as e:
            Tools.log.error(f"iproyal续费任务：检查token自动续费状态失败: {str(e)}")
            return False

    # 判断订单对应的token是否过期了
    async def check_token_activite(order_id):
        from models.db.proxy import  TUserTrafficIp
        from tortoise.expressions import F, Q
        from tortoise.functions import Sum
        iproyals = await TIproyalOrder.filter(order_id=order_id).all()
        now_timestamp = current_timestamp()
        # if not iproyals:
            # Tools.log.info(f"iproyal续费任务：订单 {order_id} 没有对应的iproyal订单记录，跳过处理,直接当活跃订单，续费")
            # return True
        total_traffic = 0
        for iproyal in iproyals:
            Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的iproyal记录: {iproyal}")
            if iproyal.pi_id == 0 and iproyal.traffic_id > 0:
                pi_ids = await TUserTrafficIp.filter(ti_id=iproyal.traffic_id).values("pi_id")
                if not pi_ids:
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的iproyal记录没有对应的pi_id,跳过处理")
                    continue
                usage = await TUserToken.filter(pi_id__in=[pi.get("pi_id") for pi in pi_ids]).annotate(
                    traffic_usage=Sum(F('req_usage_amount') + F('resp_usage_amount'))
                ).first().values("traffic_usage")
                if not usage:
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token没有流量使用记录,pi_id:{iproyal.pi_id},跳过处理")
                    continue

                traffic_usage = usage.get("traffic_usage",0)
                total_traffic += traffic_usage
                if traffic_usage > 1024*1024*1024:
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token流量使用量大于1GB,pi_id:{iproyal.pi_id},续费")
                    return True
                else:
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token流量使用量小于1GB,pi_id:{iproyal.pi_id}")
            
            if await _check_token_auto_renew(iproyal.pi_id):
                Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token开启自动续费,pi_id:{iproyal.pi_id}，跳过处理")
                return True
            if token:=await TUserToken.filter(pi_id=iproyal.pi_id).filter(Q(
                expired=0),Q(created_on__gt=now_timestamp-F('life_time')*3600*24 )).first():
                # 输出token对应的过期时间和状态和id
                # 过期时间
                expired_on = datetime.datetime.fromtimestamp(token.created_on) + datetime.timedelta(days=token.life_time)
                if (expired_on - datetime.datetime.now()).total_seconds() < 7 * 24 * 3600:
                    Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token过期时间小于7天,pi_id:{iproyal.pi_id}，跳过处理")
                    return False
                Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token有效,pi_id:{iproyal.pi_id}，续费处理,过期时间: {expired_on},状态: {token.expired},id: {token.id}")
                return True
        Tools.log.info(f"iproyal续费任务：订单 {order_id} 对应的token过期,pi_id:{iproyal.pi_id}，跳过处理,总流量使用量: {total_traffic},不续费")
        return False

    # # 并发执行所有订单处理
    # tasks = [process_with_semaphore(order) for order in order_ids]
    # await asyncio.gather(*tasks)
    pre_balance = await get_iproyal_user_balance()
    order_list = []
    for order in [i for i in order_ids if await check_token_activite(i)]:
        Tools.log.info(f"iproyal续费任务：开始处理订单: {order}")
        is_suc = await process_order(order)
        if is_suc:
            order_list.append(order)
    after_balance = await get_iproyal_user_balance()
    Tools.feishu1.sendText(f"iproyal续费任务：处理订单数量: {len(order_list)}, 续费前余额: {pre_balance}, 续费后余额: {after_balance}\n续费order_ids: {order_list}")
    return {"suc_ids": suc_ids, "fail_ids": fail_ids,"msg":msg}


async def add_pay_ref(request,is_gpt,uid):
    ref = request.headers.get("Ref")
    if not ref:
        return
    try:
        Tools.log.debug("=============================add_pay_ref_start=============================")
        o = await TIpOrders.filter(user_id=uid,payway__startswith='charge_',type='+').order_by('-orderid').first().values("orderid")
        orderid = o.get("orderid")
        if not orderid:
            return
        import tortoise
        conn = tortoise.connections.get("default")
        """
        CREATE TABLE `t_tmp_pay_ref` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `ref` varchar(255) DEFAULT NULL,
      `uid` int(11) DEFAULT NULL,
      `is_gpt` tinyint(4) DEFAULT NULL,
      `order_id` varchar(255) DEFAULT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='临时统计用户充值来源 ';
        """
        await conn.execute_query("insert into t_tmp_pay_ref (ref,uid,is_gpt,order_id) values (%s,%s,%s,%s)",(ref,uid,is_gpt,orderid) )
        Tools.log.debug("=============================add_pay_ref_end=============================")
    except Exception as e:
        Tools.log.error(e)
        

async def tmp(conversion_token,product_id,ref,uid,pay_order,order,_email):
    params = {
        'conversion_token': conversion_token,
        'product_id': product_id,
        'ref': ref,
        'source_user_id': uid,
        'transaction_id': pay_order,
        'total_money': order['currency'],
        'start_time_plan': order['created_on'],
        'cashback_mode': 2,
        'source_user_info': _email
    }
    async with aiohttp.ClientSession() as session:
        resp = await session.get("https://api.partnershare.net/partner/conversion?conversion_type=2", params=params, timeout=5)
        resp.raise_for_status()
        json_resp = await resp.json()
        if json_resp["code"] != 200:
            print("partner_share charge api res Error")

        

async def tmp(conversion_token,product_id,ref,uid,pay_order,order,_email):
    params = {
        'conversion_token': conversion_token,
        'product_id': product_id,
        'ref': ref,
        'source_user_id': uid,
        'transaction_id': pay_order,
        'total_money': order['currency'],
        'start_time_plan': order['created_on'],
        'cashback_mode': 2,
        "conversion_type":3 if ref == "dZjFlX" else 2,
        "event_name": "返佣20%",
        'source_user_info': _email
    }
    async with aiohttp.ClientSession() as session:
        resp = await session.get("https://api.partnershare.net/partner/conversion", params=params, timeout=5)
        resp.raise_for_status()
        json_resp = await resp.text()
        print(json_resp)
        # if json_resp["code"] != 200:
        #     print("partner_share charge api res Error")


async def main():
    import pandas as pd
    # order = {
    #     "currency":"5.00",
    #     "created_on":"1743145225",

    # }
    conversion_token = "9JtNT0gyynSdGPrKJUrbzKXCEezkI3lH"
    product_id = "12963"
    # ref = "7iP59o"
    # pay_order = "202503280702081565"
    # uid = 233538
    # _email = "<EMAIL>"


    df = pd.read_csv(r"C:\Users\<USER>\Desktop\返现.csv")
    for index, row in df.iterrows():
        if index == 0 :
            continue
        uid = row['uid']
        order = {
            "currency":row['currency'],
            "created_on":row['created_on'],
        }
        ref = 'dZjFlX'
        pay_order = row['orderid']
        _email = row['name']
        if _email.find("@") != -1:
            _email = _email[:4] + "*" * (_email.find("@")-2) + _email[_email.find("@"):]
        else:
            _email =  "*" *3 + _email[3:]
        print(index)
        v = await tmp(conversion_token,product_id,ref,uid,pay_order,order,_email)
        # print(v)
if __name__ == '__main__':
    """
    [E 230926 07:20:35 payment:279] 获取到的事件信息: Signature:flpBTSg7Sx18lL4wjAdlAzybcle/CQUCrlBWerPsLWqXhpJeirpcJeoJPfRvM6t1MGEq67InUNaVxSJYkqPRMvzgDlWm/eIwL8cEccZBb11YN/A1xTEn9tWPe+DgaJBFwYlx+********************************+nKYbc=
    [E 230926 07:20:35 payment:280] 获取到的事件信息: post_body:{"data":{"completeTime":"20230926152034","currency":"USD","merchantId":"2022000000008104","merchantOrderId":"20230926072002440735","orderAmount":"22.00","orderId":"3587860364125189","payMethod":"ALIPAY_CN","status":"SUCCESS"},"errCode":"00000000","errMessage":"Success","isSuccess":true,"status":"SUCCESS","traceId":"bfbf378ad40ec634"}
    [E 230926 07:20:35 payment:285] 获取到的事件信息: chinagpay_alipay 
    [E 230926 07:20:35 payment:286] {'data': {'completeTime': '20230926152034', 'currency': 'USD', 'merchantId': '2022000000008104', 'merchantOrderId': '20230926072002440735', 'orderAmount': '22.00', 'orderId': '3587860364125189', 'payMethod': 'ALIPAY_CN', 'status': 'SUCCESS'}, 'errCode': '00000000', 'errMessage': 'Success', 'isSuccess': True, 'status': 'SUCCESS', 'traceId': 'bfbf378ad40ec634'}
    [E 230926 07:20:35 payment:287] 结束获取信息： chinagpay_alipay 
    """

    """
    Log origin body '{"data":{"completeTime":"20231109152403","currency":"USD","merchantId":"2022000000008104","merchantOrderId":"20231109072142009497","orderAmount":"22.00","orderId":"3837008544653317","payMethod":"ALIPAY_CN","status":"SUCCESS"},"errCode":"00000000","errMessage":"Success","isSuccess":true,"status":"SUCCESS","traceId":"82bd1b495afda338"}' 
2023-11-09 07:29:05.101 | MainThread | INFO | pay : call_back:356 -  stripe Log Headers Headers({'host': 'dash-api.proxy302.com', 'x-real-ip': '***************', 'x-forwarded-for': '***************', 'x-user-agent': 'okhttp/3.12.2', 'connection': 'close', 'content-length': '335', 'signature': 'LBo1nnAd/MDa3qjL94r3daJHv69WktMU1G853XDZtr4zE1FN1C5nsEPFnxCdOrf7pYJUeTQRpm7TgUWktIUMgjcYdHpNJfgW+MZ4JDSfFFcCD87zdr1Vy9w2pxVyqHGf+UF+jsSZIPWvJi8vwvHrawzAm9x5l0a49YbROZDFiJ4=', 'content-type': 'application/json; charset=utf-8', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/3.12.2'})
    """

    # post_body = '{"data":{"completeTime":"20231115114635","currency":"USD","merchantId":"2022000000008104","merchantOrderId":"20231115034322153683","orderAmount":"22.00","orderId":"3870123930148869","payMethod":"ALIPAY_CN","status":"SUCCESS"},"errCode":"00000000","errMessage":"Success","isSuccess":true,"status":"SUCCESS","traceId":"98dea9fa785d7faf"}'
    # signature = "DNLVfDYrdpJuH8vZ5rKdB6l6X7DbP2ayO2pfzq+9smGfKB2nedKDF/EmVNZ+hXfmwwOAxfJ8no5Bd/q5Z3UFtZqPP76ObbiAKeIR02fyeFeljj+dsYMwXDvfTFuD6D+kJz+x5+8BOZ6QwmBbvy2/BtcBHZabVffkX7BvO9igVzk="
    # v = RSAPubCrypt("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC7GbB4/HLoIrHQTOcEBavR7zR6quxeR6RQWwPghzWuDfXllOOOx6KVUWOf8vTAC1l6DCDtpYCyzdSthieBcKpCiEqOvGCYdE+aYEPzk6bLGWETlpUXVno9yrawkJEDJ/qUAp6ABSmsX1jQ1/Yze6DUGBRv9Q8h2kmvDCF0viDRlQIDAQAB")\
    #     .verify_sign(post_body, signature)
    # print(v)

    import asyncio
    order = {
        "currency":"5.00",
        "created_on":"1743145225",

    }
    conversion_token = "MC1KXVBQ7_QQWslxPlfkhC17BRnEED3M"
    product_id = "13329"
    ref = "7iP59o"
    pay_order = "202503280702081565"
    uid = 233538
    _email = "<EMAIL>"

    v = asyncio.run(main())
    # v = asyncio.run(StripePaymentApiHandler().get_obj_from_id("cus_RYmRyfStI03V7b","sk_test_51OotEiBiAQT7PpBPc5coLitGUso8HMh3wh8H1scpN5SmbOLjpIrE9YGFENT1XqmNigij1zj077W7j1sNUvXQbe7C00YZDqF9C0"))
    print(v)