# -*- coding: utf-8 -*-
# @Time    : 2024/1/25 17:53
# <AUTHOR> hzx1994
# @File    : gpt.py
# @Software: PyCharm
# @description:
from typing import Union, List

from fastapi import Query
from pydantic import BaseModel, field_validator
from enum import IntEnum, Enum


class GPTStatus(IntEnum):
    NORMAL: int = 1
    DELETED: int = -1
    DISABLE: int = -2
    LIMIT_CURRENT_DATE: int = -3
    EXPIRED: int = -4
    LIMIT_CURRENT_HOUR: int = -5
    LIMIT_CURRENT_MONTH: int = -6

class InDB(IntEnum):
    FAIL=0
    FINISHED: int = 1
    IN_PROGRESS: int = 2
    WAIT: int = 3

class ToolsId(IntEnum):
    CustomModel:int = -4
    KB_EMB:int = -3
    KB:int = -2
    MJ:int = -1
    API: int = 0
    Chatbot: int = 1
    GPTs: int = 2
    # >=3
    OTHER: int = 3

    ZERO_SHOT:int=7
    AI_BOX:int=9


class APPName(str, Enum):
    FEISHU: str = "feishu"
    DingDing: str = "dingding"
class tool_model_item(BaseModel):
    extra:dict={}
    model_id:int=0
    tool_id:int=0

class tool_config(BaseModel):
    path:str=''
    enable:bool=True
    tools:List[tool_model_item]=[]


class Locale(str, Enum):
    CN: str = "cn"
    EN: str = "en"
    JP: str = "jp"


class LocaleGPT(str, Enum):
    ZH: str = "zh"
    EN: str = "en"
    JA: str = "ja"

class ListType(str, Enum):
    BLACKLIST: str = "BLACKLIST"
    WHITELIST: str = "WHITELIST"

class IpType(str, Enum):
    SINGLE: str = "SINGLE"
    RANGE: str = "RANGE"
    SUBNET: str = "SUBNET"


class ApiKeyLimit(BaseModel):
    token_id:int = 0
    limit_balance:float = None
    model_id_list: List[int]= None
    ip_whitelist:list= []
    ip_blacklist:list=[]
    remark:str = ""



class GptToken(BaseModel):
    name: str = ""
    is_robot: int = 0
    share_code:str = ""
    limit_cost: Union[int,float] = 0
    expired_on: int = 0
    num: int = 1
    remark:str = ""
    model_id:Union[str,int] = 0
    limit_daily_cost:Union[int,float] = 0
    limit_monthly_cost: Union[int,float] = 0
    tz:str="Asia/Shanghai"
    external_code:str=""
    gpts_code:str=""
    custom_home_page:str=""
    use_gpts:int=1
    open_tts:int=1
    enable_plugins:int=1
    tool_id:int=0
    extra:dict = {}
    kb_id:int=0
    tool_nav_count:int=3
    show_balance:bool=False
    banners:list=[]
    hide_home_page:bool=True
    tool_id_conf:List[tool_config]=[{
        "path": "",
        "enable": True,
        "tools": [
            {"model_id": 0, "tool_id": 0, "extra": {"open_tts": 1}},
            {"model_id": 1, "tool_id": 1, "extra": {"open_tts": 1}}
        ]}
    ]


    @field_validator('share_code')
    def check_sum(cls, v:str):
        if v and (len(v) >= 20 or len(v)<4):
            raise ValueError('name length must be 4-20')
        return v

    @field_validator('limit_cost','limit_daily_cost','limit_monthly_cost')
    def check_num(cls,v:int):
        if v:
            if v < 0:
                raise ValueError('limit_cost must gt 0')
            if v > 1000000:
                raise ValueError('limit_cost must lt 1000000')
        return v


class LimitQuery():
    def __init__(self, page: int = Query(1, description="page,可选，post请求不用"),
                 page_size: int = Query(10, description="page_size,可选，post请求不用"),
                 ):
        self.page = page
        self.page_size = page_size
        # self.lang = lang


class TimeQuery():
    def __init__(self, start_time: int = Query(0, description="开始时间"),
                 end_time: int = Query(0, description="结束时间"),
                 ):
        self.start_time = start_time
        self.end_time = end_time


from typing import List, Literal, Optional, Union
from pydantic import BaseModel, Field
import time
class FunctionCallResponse(BaseModel):
    name: Optional[str] = None
    arguments: Optional[str] = None

class UsageInfo(BaseModel):
    prompt_tokens: int = 0
    total_tokens: int = 0
    completion_tokens: Optional[int] = 0

class ChatMessage(BaseModel):
    role: Literal["user", "assistant", "system", "function"]
    content: str = None
    name: Optional[str] = None
    function_call: Optional[FunctionCallResponse] = None


class DeltaMessage(BaseModel):
    role: Optional[Literal["user", "assistant", "system"]] = None
    content: Optional[str] = None
    function_call: Optional[FunctionCallResponse] = None

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.8
    top_p: Optional[float] = 0.8
    max_tokens: Optional[int] = None
    stream: Optional[bool] = False
    tools: Optional[Union[dict, List[dict]]] = None
    repetition_penalty: Optional[float] = 1.1


class ChatCompletionResponseChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Literal["stop", "length", "function_call"]


class ChatCompletionResponseStreamChoice(BaseModel):
    delta: DeltaMessage
    finish_reason: Optional[Literal["stop", "length", "function_call"]]
    index: int

class ChatCompletionResponse(BaseModel):
    model: str
    id: str
    object: Literal["chat.completion", "chat.completion.chunk"]
    choices: List[Union[ChatCompletionResponseChoice, ChatCompletionResponseStreamChoice]]
    created: Optional[int] = Field(default_factory=lambda: int(time.time()))
    usage: Optional[UsageInfo] = None



class KTasks(str,Enum):
    """
    RAG逻辑Celery任务名
    """
    #测试
    test_deploy =  "tasks.test.delay_add"
    #从url中上传文件
    upload_file_from_url = "tasks.upload_file.update_from_url"
    # 从文件上传
    upload_file = "tasks.upload_file.upload_file"

    #upload_blob文件上传
    upload_blob = "tasks.upload_file.upload_blob"

    #graphRAG文件插入
    upload_graph = "tasks.upload_file.upload_graph"