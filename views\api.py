# -*- coding: utf-8 -*-
# @Time    : 2023/9/26 11:31
# <AUTHOR> hzx1994
# @File    : api.py
# @Software: PyCharm
# @description:
import asyncio
import time

import aiohttp
from httpx import request
import requests

from aiohttp_socks import ProxyConnector

from fastapi import APIRouter, Header, Body, Depends, Form, Path, File, UploadFile, Query
from fastapi import Request
from starlette.responses import RedirectResponse
from tortoise import connections, BaseDBAsyncClient

from controllers.api import get_amount_record, login_partner_share, update_iproyal_ip_func,get_app_version_func
from controllers.gpt import get_models_from_name, get_robot_mapping, cache_summary_data, cache_summary_date_by_date
from controllers.pay import *
from controllers.proxy import create_key, del_key, get_user_keys, create_dynamic_pool_ip, check_host_port, \
    rotation_token, _post, proxy_auto_renew
from controllers.pubilc import get_questionnaire_by_id, check_user_is_submit, send_traffic_alarm, test_user_action, log_event, \
    get_forward_domain_func, run_code_func, send_balance_msg, is_valid_domain, check_domain_accessibility
from controllers.user import get_current_active_user, get_client_ip, test_ip, get_user_from_api_key
from libs.Epay import check_sign
from models.db.proxy import TIpOrders, TIps, TProxyIp, TProxyMachines, TProxyTtl, TGeoCountry, TUserToken, TGeoCity, \
    TGeoState, TTrafficIps, UserInfo, TUser, TPayways
from models.proxy import NetworkType,  StatisticsKey
from models.response import suc_data, fail_data, StateCode
from models.user import UserOut
from utils import ip2int, ip2hex, Tools, current_timestamp, is_from_gpt
from tortoise.expressions import Q, RawSQL, Subquery
app = APIRouter()

@app.post("/crontab/expired_proxy")
async def cache_expired_proxy():
    """
    刷新静态ip过期的记录，数据库没更新的记录强制更新状态
    update t_user_tokens set expired=2 where is_static and (created_on + life_time*3600*24) <UNIX_TIMESTAMP() and expired=0 and la_id=0
    """
    await TUserToken.filter(
        is_static=True,
        created_on__lt=RawSQL("UNIX_TIMESTAMP()-life_time*3600*24"),
        expired=0,
        la_id=0
    ).update(expired=2)



@app.post("/crontab/auto_renew")
async def auto_renew_proxy():
    data = await proxy_auto_renew()
    return suc_data(data)

@app.post("/update/iproyal/ip",summary="更新iproyal的ip")
async def update_iproyal_ip(old_ip:str=Body(...),iproyal_url:str=Body(...),order_id:int=Body(...)):
    await update_iproyal_ip_func(old_ip,iproyal_url,order_id)
    return suc_data()

@app.post("/crontab/summary/data",summary="归档ai的数据")
async def summary_data():
    """
    归档数据
    1. 界清要归档的时间跨度 start_time + n ，到现在结束，且结束时间存储redis
    2. 获取这个时间区间内的时间内的token_id
    3. 查询对应token_id 的时区，并根据时区分组，再去数据库做反查,对应时区加上对应时间补充
    4. 入库
    """
    await cache_summary_data()
    await cache_summary_date_by_date(2)
    return suc_data()




@app.post("/crontab/check/pm",summary="检查动态ip")
async def cache_check_pm():
    """
    检查动态ip
    """

    sem = asyncio.Semaphore(50)
    async def func(i):
        async with sem:
            host = pm_host.get(i.get("pm_id"))
            port = i.get("forward_port")
            ok = await _post(session, host=host, port=port)
            if not ok:
                await TProxyIp.filter(id=i.get("id")).update(online=0, status=0)
                await TUserToken.filter(pi_id=i.get("id")).update(expired=2)

    conn = connections.get("default")
    pm_id_list = await conn.execute_query_dict("""
    select pm_id  from t_911_accounts where deleted_on=0 
    """)
    hosts = await TProxyMachines.filter(id__in=[i.get("pm_id") for i in pm_id_list]).values("ip_id","id")
    tmp_dict = {host.get("ip_id"):host.get("id") for host in hosts}
    _ = await TIps.filter(id__in=[i.get("ip_id") for i in hosts]).values("ip","id")
    pm_host = {tmp_dict.get(i.get("id")):i.get("ip") for i in _}
    session = aiohttp.ClientSession()

    jobs = []
    for i in await TProxyIp.filter(pm_id__in=[i.get("pm_id") for i in pm_id_list], online=1, status=1,deleted_on=0).values("id","forward_port","pm_id"):
        jobs.append(func(i))

    await asyncio.gather(*jobs)
    await session.close()


@app.post("/crontab/user_balance")
async def cache_user_balance():
    _ = await UserInfo.all().annotate(balance=RawSQL("sum(balance)")).filter(uid__in=RawSQL('(select uid from t_users)'),uid__not_in=RawSQL("(select uid from t_users where email like '%@adswave%' "
                                                                                                " union select uid from t_users where email in (select email from t_email_blacklists where deleted_on=0 )"
                                                                                                ")")).values("balance")
    balance = _[0].get("balance")
    conn = connections.get("default")
    _ = await conn.execute_query(
        "select id from t_summary order by id desc limit 1")
    last_id = _[1][0].get("id")
    await conn.execute_query("update t_summary set user_balance=%s where id=%s", (balance//1000, last_id))
    return suc_data()

@app.get("/statistics/log", summary="获取统计上报数据")
async def get_statistics_view(log_type_id: StatisticsKey = Query(StatisticsKey.new_user_guide, description="统计类型id", title="统计类型id"),value:str=Query(...,description="value",title="value"),user: UserOut = Depends(get_current_active_user)):
    await log_event(value,log_type_id.value,user.uid)
    return suc_data()

@app.post("/webhook/{pay_method}")
async def webhook(request: Request, pay_method: str, Signature=Header(None), Stripe_Signature=Header(None)):
    if pay_method == "chinagpay_alipay":
        res = await ChinaGpayAliplayHandler().call_back(request=request)
    elif pay_method == "wechat_pay":
        res = await WechatPayHandler().call_back(request=request)
    else:
        res = await StripePaymentApiHandler().call_back(request=request)
    return res

@app.get("/stripe/auto/renew",summary="自动充值")
async def auto_renew():
    await StripePaymentApiHandler(is_gpt=False).auto_renew_payment()
    return suc_data()


@app.post("/epay/webhook/{order_id}",summary="获取epay回调数据")
async def epay_webhook(order_id: str,request:Request):
    form_data = await request.form()
    form_data_dict = dict(form_data)
    Tools.log.info(f"epay:{form_data_dict}")
    if form_data.get("status") == "7":
        e = EpayPayment()

        await e.call_back(request=request, order_id=order_id,from_data_dict=form_data_dict)

        return 'success'

@app.post("/apple/webhook",summary="获取apple回调数据")
async def apple_webhook(request:Request,data:str=Body(...,description="apple回调数据",title="apple回调数据"),uid: int=Body(0,description="用户id",title="用户id")):
    a = AppPayAliplayHandler()
    return await a.call_back(request=request,data=data,uid=uid)

@app.get("/epay/check/orders")
async def epay_check_orders(request:Request,):
    e = EpayPayment()
    for i in await TIpOrders.filter(created_on__gt=current_timestamp()-7200,pay_order__startswith='epay_',type='+',status=0).all().values("pay_order","user_id"):
        r = await e.call_back(request=request,order_id=i.get("pay_order"),uid=i.get("user_id"))
        Tools.log.debug(r)


@app.post("/epay/{payway_id}",summary="epay支付")
async def epay_pay(request:Request,payway_id: int,user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    
    is_gpt = is_from_gpt(request)
    payment = EpayPayment(uid=user.uid,payway_id=payway_id,is_gpt=is_gpt)
    res = await payment.post()
    if not res:
        return fail_data()
    await add_pay_ref(request, is_gpt=is_gpt, uid=user.uid)
    return suc_data(data={"url":res})


@app.post("/key", summary="创建密钥")
async def add_key(user: UserOut = Depends(get_current_active_user),
                  key_name: str = Form(..., description="密钥名称", title="密钥名称")):
    if resp := await test_user_action(user):
        return resp
    return await create_key(user.uid, key_name=key_name)


@app.delete("/key/{key_id}", summary="删除密钥")
async def del_key_view(user: UserOut = Depends(get_current_active_user),
                       key_id: int = Path(..., description="密钥id", title="密钥id")):
    return await del_key(user.uid, key_id=key_id)


@app.get("/key/list", summary="获取api key列表，用户创建的key上限有限制，可以不分页")
async def get_key_view(user: UserOut = Depends(get_current_active_user)):
    return await get_user_keys(user.uid)



@app.post("/insert/oracle/datacenter_ip", summary="oracle ip插入")
async def insert_iproyal_residential(request:Request,network="http",username='',pwd='',port=0,oracle_host=''):
    """

    """
    ip = await get_client_ip(request)
    res = []
    from tenacity import retry, stop_after_attempt, wait_fixed
    @retry(stop=stop_after_attempt(3), wait=wait_fixed(2))
    async def job():
        print(1111111)
        user_name = username
        password = pwd
        if user_name:
            proxy = f"{network}://{user_name}:{password}@{ip}:{port}"
        else:
            proxy = f"{network}://{ip}:{port}"
        import aiohttp
        import datetime
        if network == 'socks5':
            connector = ProxyConnector.from_url(proxy)
            proxy = None
        else:
            connector = None

        async with aiohttp.ClientSession(connector=connector) as session:
            async with session.get("http://lumtest.com/myip.json",proxy=proxy, timeout=5) as resp:
                if resp.status != 200:
                    resp.raise_for_status()
                data = await resp.json()


        country = await TGeoCountry.filter(code=data.get("country").lower()).first()
        if not country:
            print("no country")
            return
        ip_obj = await TIps.filter(ip_addr = ip2int(ip)).first()
        if not ip_obj:
            ip_obj = await TIps.create(ip_addr=ip2int(ip), ip_address=ip2hex(ip),ip=ip)
        ps_id = 13 if network == "http" else 14
        source = "oracle_http_proxy" if network == "http" else "oracle_socks_proxy"
        pm_obj = await TProxyMachines.filter(ip_id=ip_obj.id,ps_id = ps_id).first()
        if not pm_obj:
            pm_obj = await TProxyMachines.create(ip_id=ip_obj.id, forward_port_start=port, forward_port_end=port,ps_id = ps_id)
        pip = await TProxyIp.filter(username=user_name, password=password, source=source,ip_id=ip_obj.id).first()
        if not pip:
            pip = await TProxyIp.create(username=user_name, password=password, source=source, forward_port=port,port=port, ip_id=ip_obj.id,country_id = country.id,
                               pm_id=pm_obj.id, online=True, health=True, status=True)
        else:
            pip.pm_id = pm_obj.id
            await pip.save()
        if not await TProxyTtl.filter(pi_id=pip.id).limit(1).exists():
            await TProxyTtl.create(pi_id=pip.id,oracle_host=oracle_host )
        print("end")
    job_list = []
    try:
        await job()
    except:
        res = [ip]
    return suc_data(data={"err_ip":res})


### 手动导入luna的ip
@app.post("/insert/iproyal_residential", summary="插入luna_residential")
async def insert_iproyal_residential(source="luna_residential",ps_id=15,file: UploadFile=UploadFile(...),network = "http",username='',pwd=''):
    """
**************,7777,Japan,2023/11/24 10:44

    """
    res = []
    async def job(ip,port,region,expire_date):
        ip = ip
        port = port
        region = region
        user_name = username
        password = pwd
        expire_date = expire_date
        if user_name:
            proxy = f"{network}://{user_name}:{password}@{ip}:{port}"
        else:
            proxy = f"{network}://{ip}:{port}"
        print(proxy)
        import aiohttp
        import datetime
        country = await TGeoCountry.filter(name=region).first()
        if not country:
            print("no country")
            return
        try:
            if network == 'socks5':
                connector = ProxyConnector.from_url(proxy)
                proxy = None
            else:
                connector = None

            async with aiohttp.ClientSession(connector=connector) as session:
                await session.get("http://lumtest.com/myip.json",proxy=proxy, timeout=5)
        except Exception as e:
            print(e)
            res.append(ip)
            return
        ip_obj = await TIps.filter(ip_addr = ip2int(ip)).first()
        if not ip_obj:
            ip_obj = await TIps.create(ip_addr=ip2int(ip), ip_address=ip2hex(ip),ip=ip)
        pm_obj = await TProxyMachines.filter(ip_id=ip_obj.id,ps_id = ps_id).first()
        if not pm_obj:
            pm_obj = await TProxyMachines.create(ip_id=ip_obj.id, forward_port_start=port, forward_port_end=port,ps_id = ps_id)
        pip = await TProxyIp.filter(username=user_name, password=password, source=source,ip_id=ip_obj.id).first()
        if not pip:
            pip = await TProxyIp.create(username=user_name, password=password, source=source, forward_port=port,port=port, ip_id=ip_obj.id,country_id = country.id,
                               pm_id=pm_obj.id, online=True, health=True, status=True)
        else:
            pip.pm_id = pm_obj.id
            await pip.save()
        if not await TProxyTtl.filter(pi_id=pip.id).limit(1).exists():
            await TProxyTtl.create(pi_id=pip.id, expire_date=datetime.datetime.strptime(expire_date[:16], "%Y/%m/%d %H:%M").strftime("%Y-%m-%d %H:%M:%S"))
        print("end")
    x = await file.read()
    x = x.decode("utf-8")
    job_list = []
    for i in x.split("\n"):
        ip,port,region,expire_date = i.split(",")
        job_list.append((ip,port,region,expire_date))

    await asyncio.gather(*[job(*i) for i in job_list])
    return suc_data(data={"err_ip":res})



@app.post("/sign_in", summary="签到送ptc")
async def sign_in(request:Request,user: UserOut = Depends(get_current_active_user)):
    ok = await sign_in_for_ptc(user.uid)
    if not ok:
        return fail_data(msg="签到失败",code=StateCode.SignInError)
    return suc_data()


@app.get("/charge/invite",summary="partnershare回调充值接口")
async def charge_invite(request:Request,
                        user_id:int=Query(...,description="用户id",title="用户id"),
                        money:float=Query(...,description="充值金额",title="充值金额"),
                        withdrawal_id:str=Query(...,description="提现id",title="提现id"),
                        secret:str=Query(...,description="secret",title="secret"),
                                                       ):
    is_gpt = is_from_gpt(request)
    await charge_invite_for_ptc(user_id,money,withdrawal_id,secret,is_gpt=is_gpt)
    return suc_data(code=200,msg="success")

@app.post("/gift", summary="赠送流量")
async def gift(request:Request,uid = Body(...),wjId=Body(...),ip=Body(""),is_zh:bool=Body(True)):
    Tools.log.debug(f"gift:wjId:{wjId},uid:{uid},ip:{ip},is_zh:{is_zh}")
    await gift_for_ptc(wjId,uid,extra_value=1000,ip=ip,is_zh=is_zh)

@app.get("/refund/ai",summary="退款ai,操作 t_refund_log表")
async def refund_ai():
    await refund_ai_for_ptc()
    return suc_data(code=200,msg="success")


@app.post("/gift/kason",summary="赠送金额给卡颂")
async def gift_kason(file: UploadFile = UploadFile(...)): 
    fail_list = await kason_gift_for_ptc(await file.read())
    return suc_data(data={"fail_list":fail_list})

@app.get("/questionnaires", summary="获取调查问卷")
async def get_questionnaires(request:Request,user: UserOut = Depends(get_current_active_user)):
    is_test ='test' in request.headers.get("host")
    is_gpt = is_from_gpt(request)
    data = await get_questionnaire_by_id(user.uid, is_test=is_test, is_gpt=is_gpt)
    return suc_data(data = data)



@app.get("/check/luminati/ip", summary="检查luminati ip，zone对应的池子刷新后，用户名后缀的ip和代理实际的ip对应不上就标记失效")
async def check_luminati_ip():
    # 优化并发控制，避免创建过多任务
    MAX_CONCURRENT = 20  # 降低并发数，避免资源耗尽
    BATCH_SIZE = 100     # 分批处理，避免一次性处理过多数据

    need_update_ids = []

    async def check_ip_batch(session, records_batch, sem):
        """批量检查IP有效性"""
        async def check_ip_is_valid(check_proxy, ip, id):
            async with sem:
                try:
                    # 添加连接超时和读取超时
                    timeout = aiohttp.ClientTimeout(total=10, connect=5)
                    resp = await session.get(
                        "http://lumtest.com/myip.json",
                        proxy=check_proxy,
                        timeout=timeout
                    )
                    if resp.status == 200:
                        data = await resp.json()
                        from_ip = data.get("ip")
                        if from_ip != ip:
                            need_update_ids.append(id)
                    else:
                        Tools.log.warning(f"IP {ip} status != 200")
                except asyncio.TimeoutError:
                    Tools.log.warning(f"IP {ip} timeout")
                except Exception as e:
                    Tools.log.error(f"IP {ip} check failed: {e}")

        sem = asyncio.Semaphore(MAX_CONCURRENT)
        jobs = []
        for record in records_batch:
            check_proxy = record["check_proxy"]
            ip = record["ip"]
            id = record["id"]
            jobs.append(check_ip_is_valid(check_proxy, ip, id))

        await asyncio.gather(*jobs, return_exceptions=True)

    conn = connections.get("default")
    records = await conn.execute_query_dict("select check_proxy,ip,id from t_traffic_ips where deleted_on =0")

    # 分批处理，避免一次性创建过多协程
    total_records = len(records)
    Tools.log.info(f"开始检查 {total_records} 个IP")

    async with aiohttp.ClientSession(
        connector=aiohttp.TCPConnector(limit=MAX_CONCURRENT, limit_per_host=10)
    ) as session:
        for i in range(0, total_records, BATCH_SIZE):
            batch = records[i:i + BATCH_SIZE]
            Tools.log.info(f"处理批次 {i//BATCH_SIZE + 1}/{(total_records + BATCH_SIZE - 1)//BATCH_SIZE}")

            sem = asyncio.Semaphore(MAX_CONCURRENT)
            await check_ip_batch(session, batch, sem)

            # 批次间短暂休息，避免过度占用资源
            await asyncio.sleep(0.1)

    Tools.log.info(f"检查完成，需要更新的IP数量: {len(need_update_ids)}")

    # 批量更新数据库，避免单条更新
    if need_update_ids:
        await conn.execute_query("update t_traffic_ips set deleted_on=1 where id in %s", (need_update_ids,))
        await conn.execute_query("update t_proxy_ips set status = 0,modified_on=10086 where id in (select pi_id from t_user_traffic_ips where ti_id in %s)", (need_update_ids,))




@app.get("/refresh/tokens", summary="刷新代理")
async def refresh_tokens():
    sem = asyncio.Semaphore(1)  # 限制协程数并发


    async def func(record):
        async with sem:
            try:
                resp = await get_iproyal_order_info(record["order_id"])
                if resp['status'] == "expired":
                    expired_ids.append(record["id"])
                else:
                    pi_ids.append(record.get("pi_id"))
                # await TProxyIp.filter(id=record["pi_id"]).update(health=True, status=True, online=True)
            except:
                pi_ids.append(record.get("pi_id"))

    conn: BaseDBAsyncClient = connections.get("default")
    expired_ids = []
    records = await conn.execute_query_dict("select id,pi_id,order_id from t_iproyal_orders where expired = 0")
    jobs = []
    pi_ids = []
    for record in records:
        jobs.append(func(record))
    await asyncio.gather(*jobs)
    if expired_ids:
        await conn.execute_query("update t_iproyal_orders set expired=1 where id in %s", [expired_ids,] )
    if pi_ids:
        await conn.execute_query("update  t_proxy_ips set health=1,online=1,status=1 where id  in %s ",[pi_ids,] )



@app.get("/unexpire/tokens", summary="刷新代理")
async def unexpire_tokens(num:int = 15):
    """
    充值的用户判断是否欠费再刷新对应的欠费状态
    """
    last_time = int(time.time()) - 60*num
    uids = await TIpOrders.filter(created_on__gt=last_time,status=1,pay_method__in=(101,601)).distinct().values("user_id")
    for uid in uids:
        await TUserToken.filter(user_id=uid.get("user_id"), deleted_on=0, la_id__gt=0).update(expired=False,
                                                                                   modified_on=time.time())

    return suc_data(msg=f"刷新了{uids}")


@app.get("/questionnaire/show", summary="调查问卷，弹窗状态")
async def get_questionnaire_show(user: UserOut = Depends(get_current_active_user)):
    return suc_data({"is_show":await check_user_is_submit(user.uid,True)})



@app.get("/update/ip",summary="定时任务,更新ip")
async def update_ip(num:int = 1):
    last_time = int(time.time()) - 60 * 60 * num
    await TIps.filter(created_on__gt=last_time).update(ip=RawSQL("inet_ntoa(ip_addr)"))



@app.post("/load/custom_ip", summary="定时任务,加载自定义ip入库(iproyal)")
async def load_custom_ip(upload_file: UploadFile = File(None),la_id: int = 99,proxy_line:str=Query(None,title="代理信息",description="host:port:username:password")):
    return await iproyal_to_mysql(upload_file,la_id,proxy_line)




@app.get("/iproyal/info/{order_id}",summary="定时任务,获取iproyal订单信息")
async def get_iproyal_info(order_id:int):
    return await get_iproyal_order_info(order_id)


@app.post("/iproyal/order/{order_id}",summary="定时任务,续费订单")
async def iproyal_order(order_id:int):
    return await extend_iproyal_order(order_id)

@app.get("/iproyal/auto/renew",summary="定时任务,自动续费订单")
async def auto_renew_order():
    ...
    # 获取订单信息
    return await auto_renew_iproyal_order()


@app.post("/iproyal/order/{country_id}/{number}",summary="定时任务,购买iproyal订单")
async def iproyal_order(country_id:int,number:int=1):
    to_traffic = True
    for i in range(number):
        msg = await buy_iproyal_order(country_id,to_traffic=to_traffic,is_all_ip=True)
    country = await TGeoCountry.filter(id=country_id).first()
    if to_traffic:
        Tools.feishu2.sendText(f"购买iproyal订单补充流量池,新增了 {country.name} 国家 {number}个静态ip")

    return msg

@app.post("/api/iproyal/order/{country_id}/{number}",summary="定时任务,购买iproyal订单")
async def iproyal_order(country_id:int,number:int=1):
    for i in range(number):
        msg = await buy_iproyal_order(country_id,to_traffic=False)
    country = await TGeoCountry.filter(id=country_id).first()
    return msg

@app.get("/iproyal/check",summary="定时任务,检查iproyal订单，没有绑定traffic_id  的记录，尝试绑定")
async def check():
    await check_buy_iproyal_order()
    return {"status":"ok"}


@app.get("/iproyal/order/info",summary="定时任务,获取iproyal订单信息,用在流量上的")
async def get_info():
    return await get_iproyal_order_proxy_info()




@app.get("/generate/dynamic/ip",summary="定时任务,生成动态ip池")
async def generate_dynamic_ip(host:str,code="US"):
    """
    定时任务；
    构造 指定的host去生成5000 到 5250端口的若干
    ip地址，然后存入数据库，
    """
    return suc_data(data=await create_dynamic_pool_ip(host,code=code))




@app.get("/check/dynamic/ip",summary="定时任务,检查动态ip池")
async def check_dynamic_ip():
    """
    定时任务；
    检查动态池ip
    """
    return suc_data(data=await check_host_port())

@app.get("/check/rotation/tokens",summary="定时任务,ip轮换检查和更新")
async def check_rotation_tokens():
    """
    定时任务；
    检查动态池ip
    """
    # TOKEN_IS_USING_2411499

    await rotation_token()
    return suc_data()

@app.post("/run/code",summary="运行代码")
async def run_code(request: Request,
                   code:str=Body(...,title="代码"),
                   language:str=Body(...,title="编程语言"),
                   Authorizations: str=Header(""),
                   Authorization: str=Header("")):
    """
    运行代码
    """
    current_url = str(request.url)
    # 设置API基础URL
    base_url = "https://api.302.ai"
    # 如果URL中包含'test'，使用测试环境URL
    if 'test' in current_url:
        base_url = "https://test-api.gpt302.com"

    # 获取用户信息
    token_value = Authorizations.split(' ')[-1] or Authorization.split(' ')[-1]
    user_dict = await get_user_from_api_key(token_value)
    uid = user_dict.get("uid")

    # 运行代码
    result = await run_code_func(code, lang=language, token_value=token_value)

    # 成功运行时调用扣费
    if result.get("error") == "":
        # 单次扣费0.005PTC
        required_cost = 5

        url = f"{base_url}/302/submit/code"
        headers = {
            "Authorization": f"Bearer {token_value}",
        }
        req = {
            "language": language,
            "code": code
        }
        log_data = {
            "req_body": json.dumps(req, ensure_ascii=False),
            "resp_body": json.dumps(result, ensure_ascii=False)
        }
        response = requests.post(url, headers=headers, json=log_data)
        if response.status_code == 200:
            Tools.log.info(f"代码运行扣费成功, 使用的API_KEY: {token_value}")
        else:
            Tools.log.info(f"代码运行扣费失败，状态码: {response.status_code}, 响应内容: {response.text}")
            return fail_data(code=response.status_code, msg=response.text)

        # 添加账单
        order_id = await add_ready_ip_order(
            user_id=uid,               # 用户id
            type="-",                  # 类型为扣费
            value=required_cost,       # 扣费金额
            currency_type="USD",       # 货币类型
            currency=0,                # 充值金额
            receive_currency=0,        # 加上手续费的充值金额
            extra_value=0,             # 充值赠送的额外值
            payway="gpt_cost",         # 扣费来源
            pay_order="",              # 第三方平台订单号
            is_inner=False,            # ?是否为内部操作
            status=1,                  # 标记订单为已完成
            checksum=True,             # 是否自校检
            valid=True,                # 是否有效
            update_user_info=True,     # 更新用户余额信息
            text="sandbox代码执行扣费"   # 备注
        )
        Tools.log.info(f"代码运行扣费统计: {order_id}, 使用的API_KEY: {token_value}")

    return suc_data(result)



@app.get("/check",summary="定时任务,检查ip轮换检查和更新")
async def check_rotation(host,port:int):
    _post(host,port)



@app.get("/get/ip_info",summary="获取ip信息")
async def xx(request:Request):
    """
    {
  "ip": "*************",
  "city": "Shenzhen",
  "region": "Guangdong",
  "country": "CN",
  "loc": "22.5455,114.0683",
  "org": "AS4134 CHINANET-BACKBONE",
  "postal": "518000",
  "timezone": "Asia/Shanghai"
}

    """

    return suc_data(await test_ip(request))


@app.get("/switch",summary="开关")
def _():
    return suc_data(data={"charge":False})


@app.get("/model/price",summary="获取模型价格")
async def get_model_price(name=Query("工具超市",title="分类名称"),is_cn: int = Query(0, title="是否国内版本，默认国际版")):
    # return await get_robot_mapping()
    result = await get_models_from_name(name)
    if is_cn:
        filter_name = ['OpenAI模型','Anthropic模型','Google模型','Midjourney']
        for i in result:
            zh_tags,en_tags,items = [],[],[]
            for zh_tag,en_tag,item in  zip(i['tags']['zh'],i['tags']['en'],i['items']):
                if zh_tag not in filter_name:
                    zh_tags.append(zh_tag)
                    en_tags.append(en_tag)
                    items.append(item)
            i['tags'] = {"zh":zh_tags,"en":en_tags}
            i['items'] = items


    return suc_data({"result":result})


@app.get("/forward/domain",summary="获取一个中转服务器域名")
async def get_forward_domain(user: UserOut = Depends(get_current_active_user)):
    return suc_data(data=await get_forward_domain_func())


@app.post("/crontab/msg",summary="定时任务，发送消息,通知余额报警")
async def send_msg():
    return await send_balance_msg()

@app.post("/crontab/traffic/alarm",summary="定时任务，发送流量报警")
async def _send_traffic_alarm():
    return await send_traffic_alarm()

@app.post("/crontab/tmp/4year/job",summary="定时任务，生成4周年活动的临时任务")
async def create_4year_job():
    await TPayways.filter(id=27).update(extra_value=0)
    await TPayways.filter(id=28).update(extra_value=0)
    await TPayways.filter(id=29).update(extra_value=0)
    await TPayways.filter(id=30).update(extra_value=0)
    await Tools.redis.delete('proxy302_cache_get_records_92d61e3b2470c88ed3c12a0bef7a4790')


@app.get("/v1/app/version",summary="获取app的推荐版本和最低版本")
async def check_app_version(platform:str=Query("Android",title="app平台"),lang=Query("zh",title="语言")):
    data = await get_app_version_func()
    return suc_data(data=data)


@app.post("/v1/load/app",summary="上传app，指定版本号")
async def load_app(request:Request,upload_file: UploadFile = File(None),
depict=Body(...,title="描述"),version_name:str=Body(...,title="app版本号"),
platform:str=Body("Android",title="app平台"),lang=Body("zh",title="语言")):
    from controllers.api import upload_app
    await upload_app(upload_file,version_name,platform='Android',depict=depict)
    return suc_data()


@app.get("/v1/file/parsing/to_md", summary="基于MarkItDown实现将文件解析为Markdown格式")
async def convert_to_markdown(url: str = Query(..., description="需要解析的文件URL"),
                              model_name: str = Query("gpt-4o-mini", description="模型名称"),
                              user: UserOut = Depends(get_current_active_user)):
    # token = requests.headers.get("Authorization")
    # 支持的文件类型
    allowed_extensions = {"pdf", "docx", "pptx", "xlsx", "csv", "json", "xml", "html", "zip", "jpg", "jpeg"}
    file_type = url.split('.')[-1]
    url = url.replace('file.302ai.cn','proxyblob.blob.core.windows.net')

    if file_type not in allowed_extensions:
        return fail_data(msg=f"{file_type}为不支持的文件类型")
    
    from markitdown import MarkItDown
    # from openai import OpenAI
    # BASE_URL = "https://api.302.ai/v1"
    # client = OpenAI(api_key=token, base_url=BASE_URL)
    # md = MarkItDown(llm_client=client, llm_model=model_name)
    md = MarkItDown()
    result = md.convert(url)        
    # 返回结果
    return suc_data(data={"markdown": result.text_content})


@app.post("/v1/validate_domain")
async def validate_domain(domain: str):

    # 验证域名格式
    if not is_valid_domain(domain):
        return fail_data(code=0, data={"result": False})

    # 验证域名可访问性
    is_accessible = check_domain_accessibility(domain)
    if not is_accessible:
        return fail_data(code=0, data={"result": False})

    return suc_data(data={"result": True})

@app.get("/v1/partner/login",summary="通过PartnerShare授权码登录")
async def partner_login(code:str=Query("",description="授权码"),x_auth_code:str=Query("",description="授权码"),
                        request:Request=None,ref=Query('',description="推荐码")):
    code = code or x_auth_code
    Tools.log.debug(f"Partner login code: {code}")
    register_ip = await get_client_ip(request)
    token =  await login_partner_share(code,register_ip,ref=ref)
    data ={
        "url":""
    }
    if token:
        data["url"] = "https://dash.proxy302.com/authentication/"+token.get("token").split(" ")[-1]
    return suc_data(data=data)


@app.get("/v1/charge/record/summary",summary="缓存充值数据")
async def get_charge_record_summary():
    return suc_data(data=await get_amount_record())
