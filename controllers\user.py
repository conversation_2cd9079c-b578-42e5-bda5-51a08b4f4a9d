# -*- coding: utf-8 -*-
# @Time    : 2023/8/21 16:08
# <AUTHOR> hzx1994
# @File    : user.py
# @Software: PyCharm
# @description:
import asyncio
import base64
import datetime
import hashlib
import os
import pickle
import re
import time
import uuid
from hashlib import md5
from pickle import loads, dumps
from typing import Union

import aiohttp
from fastapi import HTTPException, Depends,Request, status
from fastapi.security import APIKeyHeader
from starlette.status import HTTP_401_UNAUTHORIZED
from tortoise.expressions import Subquery
from tortoise.transactions import in_transaction

from conf import constants, redis_key
from constant import EMAIL_RESET_PREFIX, EMAIL_RESET_TIME_OUT
from controllers.proxy import cache_or_get_email_info, build_gift_url
from controllers.pubilc import add_ready_ip_order, ip_to_db
from exections import raise_execption, AuthException, ComstomException
from models.db.gpt import TTokenMapping, TKBFiles, TToken
from models.db.proxy import TInvi<PERSON>R<PERSON>, T<PERSON>ef<PERSON><PERSON>ck<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>r<PERSON>n<PERSON>, TIpOrders, TUserEvent, TEmailBlacklists, TIps, TProxyIp, TBalanceAlarm
from models.response import fail_data, StateCode
from models.user import UserOut, UserInModel, UserIn
from utils import Tools, random_string, get_region, get_path, cache

from passlib.context import CryptContext
from jose import JWTError, jwt
from datetime import datetime as dt, timedelta
from typing import Optional


def get_hash_password(password: str, salt: str, created_on: Union[str,int]) -> str:
    if isinstance(created_on,int):
        created_on = str(created_on)
    f = md5((md5(password.encode()).hexdigest() + md5(salt.encode()).hexdigest()).
            encode()).hexdigest()
    f += md5(str(created_on).encode()).hexdigest()
    return md5(f.encode()).hexdigest()


def get_b64_token(username: str, phone,password: str) -> str:
    f = f"{username}:{phone}:{password}"
    return f"Basic {base64.b64encode(f.encode()).decode()}"

async def get_user_with_check(email='',phone:str='',user_id='',uid:int=0) ->dict:
    filter_dict = {}
    if uid:
        filter_dict = {"uid": uid}
    elif phone:
        filter_dict = {"phone": phone}
    elif email:
        filter_dict = {"email": email}
    elif user_id:
        filter_dict = {"user_id": user_id}

    if not filter_dict:
        raise AuthException()
    Tools.log.debug(filter_dict)
    user = await TUser.filter(**filter_dict).first().values("uid", "email","name", "password", "salt", "created_on", "is_use","deleted_on")
    if user:
        invite_code_dict = await UserInfo.filter(uid=user.get("uid")).first().values("invite_code")
        user.update(invite_code_dict)
    return user


async def get_user_without_pwd(email: str="",phone:str='',user_id='',uid=0):
    """
    :param email:
    :param password:
    :return:
    """
    Tools.log.debug(f"email:{email},phone:{phone},user_id:{user_id}")
    user = await get_user_with_check(email,phone,user_id,uid=uid)
    if user:
        return UserOut(**user)
    return False

async def save_user_phone(phone,uid):
    await TUser.filter(uid=uid).update(phone=phone)
async def change_user_pwd(uid,password):
    await TUser.filter(uid=uid).update(password=password)

async def delete_user(uid):
    await TUser.filter(uid=uid).delete()
    await UserInfo.filter(uid=uid).delete()
    return True

async def get_user(uid:int=0,email: str='', password:str='',phone:str="",need_pwd:bool= True):
    """
    :param email:
    :param password:
    :return:
    """
    if uid:
        user = await TUser.filter(uid=uid).first().values("uid", "email","name", "password", "salt", "created_on", "is_use","deleted_on")
    else:
        user = await get_user_with_check(email=email,phone=phone)
    if not user:
        return False
    if need_pwd:
        md5_pw = get_hash_password(password, user.get("salt"), str(user.get("created_on")) )
        if md5_pw != user.get("password"):
            return False
    if user:
        return UserOut(**user)
    return False

async def get_user_by_old_token(token: str):
    """
    :param email:
    :param password:
    :return:
    """
    uid = await Tools.redis.get(token)
    if not uid:
        return
    user = await TUser.filter(uid=uid.decode()).first().values("uid", "email","name", "password", "salt", "created_on", "is_use","deleted_on")
    if user:
        invite_code_dict = await UserInfo.filter(uid=user.get("uid")).first().values("invite_code")
        user.update(invite_code_dict)
    if user:
        return UserOut(**user)
    return False


async def change_pw(uid,o_pw,c_pw):
    user = await get_user(uid=uid,password=o_pw)
    if user:
        salt = user.salt
        created_on = user.created_on
        pw = c_pw
        password = get_hash_password(pw,salt,created_on)
        await TUser.filter(uid=uid).update(password=password)
        return True


async def save_region(region,uid):
    info = await UserInfo.filter(uid=uid).first()
    if region != info.region:
        info.region = region
        domain = Tools.config.gpt_dashboard_cn_host if region == 0 else Tools.config.gpt_dashboard_host
        await info.save()
        return domain
    return ''

async def save_resource_area(resource_area,uid):
    await TUser.filter(uid=uid).update(resource_area=resource_area)
    return True

async def reset_pw(email=None,password=None,credential=None):
    async def set_key(email):
        random_key = random_string(15)
        key = f"{EMAIL_RESET_PREFIX}{random_key}"
        await Tools.redis.setex(key,EMAIL_RESET_TIME_OUT,email)
        return random_key

    async def set_pw(password,credential):
        key = f"{EMAIL_RESET_PREFIX}{credential}"
        email = await Tools.redis.get(key)
        if not email:
            return False
        user = await get_user_with_check(email.decode())
        if user:
            salt = user.get("salt")
            created_on = user.get("created_on")
            password = get_hash_password(password, salt, created_on)
            await TUser.filter(uid=user.get("uid")).update(password=password)
            await Tools.redis.delete(key)
            return True

    async def check_key(credential):
        key = f"{EMAIL_RESET_PREFIX}{credential}"
        email = await Tools.redis.get(key)
        if not email:
            return False
        return True

    if email:
        has_email = await TUser.filter(email=email).exists()
        if has_email:
            return await set_key(email)
        else:
            return False
    elif password and credential:
        return await set_pw(password,credential)
    elif credential:
        return await check_key(credential)
    else:
        return False

async def is_in_db(ip):
    await TProxyIp.filter(ip_id=Subquery(TIps.filter(ip=ip).first().values("id"))).exists()

async def is_in_partner_share(product_id,conversion_token,ref):
        async with aiohttp.ClientSession() as session:
            url = "https://api.partnershare.net/partner/check-ref"
            data = {
                'conversion_token': conversion_token,
                'product_id': product_id,
                'ref': ref,
            }
            async with session.get(url, params=data) as response:
                Tools.log.info(f"_push_partner_share:{await response.text()}")
                if response.status != 200:
                    return False
                import json
                res = json.loads(await response.text())
                if res.get("code") == 200:
                    return True
                return False
            
async def is_user_share_code(product_id,conversion_token,user_id,ref):
    """
    判断用户是否用了自己的邀请码来注册或者绑定
    """
    async with aiohttp.ClientSession() as session:
        url = "https://api.partnershare.net/partner/api/user/detail"
        data = {
            'conversion_token': conversion_token,
            'product_id': product_id,
            'user_id': user_id,
            }
        async with session.get(url, params=data) as response:
            Tools.log.info(f"_push_partner_share:{await response.text()}")
            if response.status != 200:
                return False
            import json
            res = json.loads(await response.text())
            if res.get("code") == 200:
                short_chain_list = res.get("data").get("short_chain_list")
                for short_chain in short_chain_list:
                    Tools.log.info(f"uid:{user_id} short_chain:{short_chain}")
                    if short_chain.get("promote_code") == ref:
                        return True
                return False
            return False        
    
async def gift_ref(uid,ref,is_gpt=False):
    from models.db.proxy import TInviteRef
    text = "from gpt302" if is_gpt else ''
    key = "ref_blacklist"
    if await Tools.redis.hget(key,ref):
        Tools.log.info(f"gift_ref：ref:{ref} 在黑名单中")
        return False
    is_need_gift = False
    if await TInviteRef.filter(uid=uid).exists():
        Tools.log.info(f"gift_ref：uid:{uid} 已经注册过ref:{ref}")
        return False
    Tools.log.info(f"gift_ref：uid:{uid} 未注册过ref:{ref}, is_gpt:{is_gpt}, 开始检查是否是用户分享的")
    product_id = Tools.config.partner_share_gpt.product_id if is_gpt else Tools.config.partner_share.product_id
    conversion_token = Tools.config.partner_share_gpt.data_return_key if is_gpt else Tools.config.partner_share.data_return_key

    is_ok = await is_user_share_code(product_id,conversion_token,uid,ref)
    if is_ok:
        Tools.log.info(f"gift_ref：uid:{uid} 是用户分享的ref:{ref}, 使用了自己的邀请码")
        return False
    # 检查ref是否在白名单中
    white_key = "REF_WHITELIST"
    if await Tools.redis.hget(white_key,ref):
        Tools.log.info(f"gift_ref：ref:{ref} 在白名单中")
        is_need_gift = True
    else:
        is_need_gift = await is_in_partner_share(product_id,conversion_token,ref)
    
        count = await TInviteRef.filter(ref=ref,create_on__gt=time.time()-3600).count()
        if count >= 10:
            is_need_gift = False
            Tools.log.info(f"gift_ref：ref:{ref}在过去一小时内已经注册10次了，不再赠送,已缓存到黑名单中")
            
            Tools.feishu1.sendText(f"ref:{ref}在过去一小时内已经注册10次了，不再赠送,已缓存到黑名单中")
            Tools.redis.hset(key,ref,1)
            await TRefBlacklists.create(ref=ref)
            return False
    if is_need_gift:
        payway="gift"
        extra_value = 1000
        await add_ready_ip_order(update_user_info=True,
                    user_id=uid, type="+", currency=0, currency_type="USD",text= text,
                    receive_currency=0, payway=payway, pay_order="", is_inner= False,
                    value=0, extra_value=extra_value,
                    status=1, checksum=True, valid=True)
        
        await TInviteRef.create(ref=ref,uid=uid)
        return True

async def bind_ref(uid,ref,is_gpt=False):
    if await gift_ref(uid,ref,is_gpt=is_gpt):
        ref_info = await UserInfo.filter(uid=uid).first().values("ref")
        if ref_info:
            if not ref_info.get("ref"):
                await UserInfo.filter(uid=uid).update(ref=ref)
            return True
    return False

async def create_user(user:UserIn,register_ip,ref,ip_id,is_gpt=False,**kwargs):
    """
    创建用户记录
    """
    

    async def _push_partner_share( ref: str, uid: int, email: str) -> bool:
        try:
            if not email:
                _email = ''
            else:
                _email = email.split("@")[-1]
                _email = email[:3] + "***@" + _email[0] + "**." + _email.split(".")[-1]
            conversion_token = Tools.config.partner_share_gpt.data_return_key if is_gpt else Tools.config.partner_share.data_return_key
            product_id = Tools.config.partner_share_gpt.product_id if is_gpt else Tools.config.partner_share.product_id
            data =  {
                'conversion_token': conversion_token,
                'product_id': product_id,
                'source_user_id': str(uid),
                'source_user_info': _email,
                'ref': ref,
            }
            Tools.log.info(f"_push_partner_share:{data}")
            Tools.log.info(f"_push_partner_share: url:{constants.PartnerShareConfig.register_req_url},data:{data}")

            async with aiohttp.ClientSession() as session:
                async with session.get(constants.PartnerShareConfig.register_req_url, params=data) as response:
                    Tools.log.info(f"_push_partner_share:{await response.text()}")
                    if response.status != 200:
                        return False
        except Exception as e:
            Tools.log.error(f"_push_partner_share:{e}")
            Tools.log.error("error", exc_info=True)
        return False

    created_on = int(time.time())
    salt = random_string(10)
    pw = user.password
    password = get_hash_password(pw, salt, created_on)
    from_invite_code = user.from_invite_code or user.ref
    user = user.to_dict() if isinstance(user,UserIn) else user.to_dict()
    user["password"]=password
    user["salt"]=salt
    user["is_use"] = 1
    user["register_ip"] = register_ip
    user["register_ip_id"] = ip_id
    user["created_on"] = created_on
    if kwargs.get("lang"):
        user["lang"] = kwargs.get("lang")

    info = {}
    if from_invite_code:
        info['from_ps_ref'] = from_invite_code



    invite_code = random_string(6)
    async with in_transaction("default"):
        tmp_user = await TUser.create(**user)
        now = datetime.datetime.now()
        order_id = int(re.sub(r"-|:|\.| ", '', str(now))) // 100
        
        text = "from gpt302" if is_gpt else ''
        await TIpOrders.create(orderid=order_id, user_id=tmp_user.uid, type="+", currency_type="IP", currency=0, payway='new user',text = text,
                               pay_order=0, status=1, valid=True,
                               token_id=0, is_inner=1, traffic_usage=0, pay_method=0)
        await UserInfo.create(uid=tmp_user.uid,invite_code=invite_code,region=user.get("region",1),ref=ref,**info)
        if ref :
            await gift_ref(tmp_user.uid,ref,is_gpt=is_gpt)
    # 登记记录
    if "from_ps_ref" in info:
        await _push_partner_share(ref, tmp_user.uid, tmp_user.email)
    return tmp_user

async def check_user(email) ->bool:
    """
    检查用户是否存在
    """
    if await TUser.filter(email=email).first().values("uid"):
        return True

async def change_user_name(user_id,name:str,auth_token):
    await TUser.filter(uid=user_id).update(name=name)
    await change_redis_user_info(auth_token,name)


async def add_user(user: UserIn, ref,register_ip,is_gpt=False,check=True):
    """
    创建用户
    """
    if check and await check_user(user.email):
        return False
    try:
        ip_id = await ip_to_db(register_ip)
    except:
        # ipv6或者特殊情况
        ip_id = 0
    return  await create_user(user,register_ip,ref,ip_id,is_gpt=is_gpt)

@cache(ttl=3600*24*30)
async def get_api_key(uid):
    tm = await TTokenMapping.filter(user_id=uid,status=1,deleted_on=0,is_robot=0).select_related("external_token").first().values("external_token__value")
    if not tm:
        # 创建默认token
        from controllers.gpt import create_user_gpt_token
        from models.gpt import GptToken

        token = await create_user_gpt_token(uid=uid,body=GptToken(name=uuid.uuid4().hex))
        tm = await TTokenMapping.filter(id=token.get("id")).select_related(
            "external_token").first().values("external_token__value")
    api_key = tm.get("external_token__value")
    return api_key

@cache(ttl=3600*2)
async def check_has_gift(uid):
    has_gift = await TIpOrders.filter(user_id=uid, status__gt=0,type='+', payway=('gift')).exists()
    return has_gift

@cache(ttl=3600)
async def get_gift_count(uid):
    times = await TIpOrders.filter(user_id=uid, status__gt=0,type='+', pay_order__gt="", payway__not_in=("new user",'gift')).count()
    return times

# @cache(ttl=20)
async def get_user_info(uid:int,region='',is_gpt=False):
    _ = await TUser.filter(uid=uid).first().values("phone","resource_area","register_from")

    user_info_dict = await UserInfo.get(uid=uid).values("balance","invite_code","is_new_user","gpt_cost","gpt_request_times","region")
    # if "/docs" in refer_host:
    #     return
    # if user_info_dict.get("region") and user_info_dict.get("region") != region:
    #     domain = Tools.config.gpt_dashboard_cn_host if user_info_dict.get("region") == 0 else Tools.config.gpt_dashboard_host
    #     user_info_dict['domain'] = domain

    is_new_user = user_info_dict.get("is_new_user")
    has_gift = False
    # 老用户或者赠送过金额
    if is_new_user:
        times = await get_gift_count(uid)
        if times >= 1:
            await UserInfo.filter(uid=uid).update(is_new_user=False)
            user_info_dict["is_new_user"] = False
    api_key = await get_api_key(uid)
    has_gift = await check_has_gift(uid)
    if has_gift:
        has_gift = True
    ref_info = await TInviteRef.filter(uid=uid).first().values("ref")
    user_info_dict['ref'] = ""
    if ref_info:
        user_info_dict['ref'] = ref_info.get("ref")
    
    user_info_dict['has_gift'] = has_gift

    from utils import get_partner_share_auth_code
    code = await get_partner_share_auth_code(Tools.config.ad_swave.base_url,uid,Tools.config.ad_swave.product_key,Tools.config.ad_swave.secret_key,Tools.config.ad_swave.target_product_key)
    if code:
        user_info_dict['ad_swave_code'] = code
    user_info_dict['phone'] = _.get("phone")
    user_info_dict['resource_area'] = _.get("resource_area")
    user_info_dict['register_from'] = _.get("register_from")
    url = await build_gift_url(uid, "zh",is_gpt)
    user_info_dict['invitation_link'] = url
    user_info_dict['api_key'] = api_key
    return user_info_dict


async def get_lang_from_req(request):
    lang = request.headers.get("lang",'zh-CN') or 'zh-CN'
    return lang

async def get_client_ip(request):
    """
    获取用户ip
    """
    register_ip = request.headers.get("X-Forwarded-For", request.client.host)

    if not register_ip:
        register_ip = str(request.client.host)
    else:
        register_ip = register_ip.split(',')[0].strip()
    register_ip = register_ip.split(',')[0].strip()

    return register_ip

@cache()
async def get_user_from_api_key(api_key):
    """
    根据token是用户给定的apiKey 获取用户信息
    """
    token = await TToken.filter(value=api_key).first().values("id")
    if not token:
        return None
    token_id = token.get("id")
    map_token = await TTokenMapping.filter(external_token_id = token_id).first().values("user_id")
    if not map_token:
        return None
    user = await get_user_without_pwd(uid=map_token.get("user_id"))
    if user:
        return dict(user)


SECRET_KEY = os.getenv("SECRET_KEY") or "f&K=F'XWmy@2Dq-`lH"  # 应该将其设置为环境变量
ALGORITHM = os.getenv("ALGORITHM") or "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60*24*7


class MyAPIKeyHeader(APIKeyHeader):

    @staticmethod
    async def cache_key(user:dict,key=None) -> str:
        def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
            to_encode = data.copy()
            if expires_delta:
                expire = datetime.datetime.utcnow() + expires_delta
            else:
                expire = datetime.datetime.utcnow() + timedelta(minutes=15)
            to_encode.update({"exp": expire})
            encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
            return "Basic "+encoded_jwt

        ## 用户单机登录，多地登陆强制退出
        # email = user.get("email")
        # user_login_token_key = f"user_login_token_{email}"
        # user_login_token_en = await Tools.redis.get(user_login_token_key)
        # if user_login_token_en:
        #     user_login_token = loads(user_login_token_en)
        #     await Tools.redis.delete(user_login_token_key)
        #     await Tools.redis.delete(user_login_token)

        # redis_key = "user_login_" + user.get("email")
        # await Tools.redis.set(user_login_token_key, dumps(key), ex=60 * 60 * 24)

        if key:
            user_login_token = key
        else:
            user_login_token = get_b64_token(user.get("email",random_string(5)), user.get("phone",random_string(8)),random_string(2))

        key = f"user_login_{user_login_token.split(' ')[1]}"
        await Tools.redis.set(key, dumps(user), ex=60 * 60 * 24 * 30)
        return user_login_token
        if not key:
            key = create_access_token(user,timedelta(ACCESS_TOKEN_EXPIRE_MINUTES))
        return key

    @staticmethod
    async def del_key(request: Request):
        key = request.headers.get("Authorization")
        value = key.split(" ")
        if len(value) != 2:
            raise AuthException()
        _, token = value
        redis_key = f"user_login_{token}"
        await Tools.redis.delete(redis_key)

    async def __call__(self, request: Request) -> UserOut:

        @cache(ttl=60)
        async def get_user_password(uid=0):
            user = await TUser.filter(uid=uid).first()
            return user.password

        async def get_current_user_from_jwt(token: str = None):
            try:
                user_dict = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            except Exception as e:
                user_dict = None

            return user_dict
        async def check_ip_black(ip: str):
            return await Tools.redis.hget(redis_key.BLACKED_OUT_IP, ip)

        ip = await get_client_ip(request)
        if await check_ip_black(ip):
            Tools.log.info(f"用户ip被封禁：{ip}")
            raise AuthException()

        key = await super().__call__(request)
        value = key.split(" ")
        if len(value) != 2:
            Tools.log.info(f"用户token 不正确")
            raise AuthException()
        _, token = value

        if token.startswith("sk-"):
            # 兼容使用api key的方式登陆
            user_dict = await get_user_from_api_key(token)
            if not user_dict:
                Tools.log.info(f"用户jwt鉴权失败")
                raise AuthException()
        else:
            user_dict = await Tools.redis.get(redis_key.USER_LOGIN.format(token=token))
            
            if not user_dict:
                user_dict = await get_current_user_from_jwt(token)
                if not user_dict:
                    raise AuthException()
            else:
                user_dict = loads(user_dict)
                password = await get_user_password(user_dict.get("uid"))
                if password != user_dict.get("password"):
                    raise AuthException()
        if await Tools.redis.hget(redis_key.BLACKED_OUT_UID, user_dict.get("uid")):
            Tools.log.info(f"用户存在 BLACKED_OUT_UID 中：uid{user_dict.get('uid')}")
            raise AuthException()
        # method = request.method  #请求方法
        # if method == "POST":
        #     user_path = request.base_url.path  #用户路由
        #     uid = user_dict.get("uid")
        #     hash_key = hashlib.md5(f"{method}{user_path}".encode()).hexdigest()
        #     user_path_key = f"user_path_{uid}_{hash_key}"  #记录用户的路由
        #     user_black_key = f"user_black_{uid}"  #记录用户的黑名单操作次数
        #
        #     # 如果用户在下列的行为中M分钟触发N次，那么说明这个用户大概率就是直接走接口的恶意用户
        #     # 相当于封号M分钟
        #     if user_black_time := await Tools.redis.get(user_black_key):
        #         if int(user_black_time.decode()) >= 5:
        #             raise ComstomException(detail="")
        #     if value := await Tools.redis.get(user_path_key) :
        #         if int(value.decode()) >= 10:
        #             if not Tools.redis.get(user_black_key):
        #                 await Tools.redis.incr(user_black_key)
        #                 await Tools.redis.expire(user_black_key, 60*5)
        #             else:
        #                 await Tools.redis.incr(user_black_key)
        #             try:
        #                 await Tools.redis.delete(user_path_key)
        #             except:
        #                 ...
        #             await asyncio.sleep(0.5)
        #             raise ComstomException(detail="Access too frequent. Please try again later.",code=StateCode.CallFrequently.value)
        #         await Tools.redis.incr(user_path_key)
        #         if ttl :=await Tools.redis.ttl(user_path_key):
        #             if ttl == -1:
        #                 await Tools.redis.expire(user_path_key, 3)
        #     else:
        #         await Tools.redis.incr(user_path_key)
        #         await Tools.redis.expire(user_path_key, 3)




        if await TEmailBlacklists.filter(email=user_dict.get("email"),deleted_on=0).exists():
            await Tools.redis.hset(redis_key.BLACKED_OUT_UID, user_dict.get("uid"),1)
            Tools.log.info(f"用户邮箱已被封禁：uid{user_dict.get('uid')}")
            raise AuthException()

        if not user_dict.get('email') and not user_dict.get('phone'):
            user_obj = await TUser.filter(uid=user_dict.get("uid")).first().values("email",'phone')
            user_dict['email'] = user_obj.get("email")
            user_dict['phone'] = user_obj.get("phone")
        request.scope['user'] = user_dict
        user = UserOut(**user_dict)
        # 注入user

        return user









security = MyAPIKeyHeader(name="Authorization")


async def get_current_active_user(current_user=Depends(security)):
    # if not current_user.is_use:
    #     raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

from tortoise import Tortoise

async def get_current_admin(current_admin=Depends(security)):
    if not current_admin:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    query = "SELECT name, email FROM t_admins WHERE deleted_on = 0"
    result = await Tortoise.get_connection("default").execute_query_dict(query)

    valid_admins = {(item['name'], item['email']) for item in result}

    if (current_admin.name, current_admin.email) not in valid_admins:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access forbidden: admin privileges required"
        )
    
    return current_admin

async def change_redis_user_info(auth_token,new_name):
    value = auth_token.split(" ")
    if len(value) != 2:
        raise_execption("token error", HTTP_401_UNAUTHORIZED)
    _, token = value
    redis_key = f"user_login_{token}"
    user_dict = await Tools.redis.get(redis_key)
    user_dict = loads(user_dict)
    user_dict['name'] = new_name
    await Tools.redis.set(redis_key, dumps(user_dict), ex=60 * 60 * 24)



async def mark_user(email_base64:str=None,email=None,job="3周年活动",ref="邮件通知"):
    if not job:
        return
    if email_base64:
        email = base64.b64decode(email_base64).decode()
    user = await TUser.filter(email=email).first().values("uid","name")
    if not user:
        return

    # 活动只记录一次
    name = user.get("name")
    uid = user.get("uid")
    exists = await TUserEvent.filter(job=job,uid=uid,ref=ref).exists()
    if exists:
        return
    await TUserEvent.create(uid=uid,name=name,job=job,ref=ref,created_on=int(time.time()),email=email )



async def verify_captcha(code,captcha,ip,agent):
    value = await cache_or_get_email_info(code)
    if not value:
        return fail_data(code=StateCode.CaptchaError)
    dict_info = value #type:dict
    last_ip = dict_info.get("ip")
    last_agent = dict_info.get("agent")
    code_md5 = dict_info.get("code_md5")
    md5 = hashlib.md5()
    md5.update(captcha.upper().encode('utf-8'))
    value = md5.hexdigest()
    Tools.log.info(f"用户输入的验证码md5：{value},缓存code的md5：{code_md5}")
    if value != code_md5 :
        return fail_data(code=StateCode.CaptchaError)


async def code_to_email(email:str,action:str="send",code:str="123456",zh=False,is_gpt=False):
    email_b64 = base64.b64encode(email.encode()).decode()
    key = "email_code_"+email_b64
    async def send_code_to_email():
        email_code = random_string(6,uppercase=False,lowercase=False)
        Tools.email.sendCode(email,email_code,zh,is_gpt)
        await Tools.redis.set(key,email_code,ex=60*60*2)

    async def get_code_from_email():
        return await Tools.redis.get(key)
    if action == "send":
        await send_code_to_email()
    else:
        value = await get_code_from_email()
        return code == value.decode()
async def test_ip(request=None, ip=None):
    current_ip = ip
    if request:
        current_ip = await get_client_ip(request)
    async with aiohttp.ClientSession() as session:
        resp = await session.get(f"https://ipinfo.io/{current_ip}?token=70b4c704f73e76")
        return await resp.json()


async def get_user_alarm_conf(uid,is_gpt=0):
    return await TBalanceAlarm.filter(uid=uid,is_gpt=is_gpt).first().values()

async def alarm_balance_create_or_update(uid,alarm_balance:float,alarm_type:str,alarm_value:str,is_gpt:bool,lang:str,enable_alarm:bool):
    """
    创建或者更新用户余额告警
    :param uid:
    :param alarm_balance:
    :param balance_alarm:
    :param balance_alarm_type:
    :return:
    """
    values = {
        "uid": uid,
        "alarm_type": alarm_type,
        "alarm_value":alarm_value,
        "status": 0,
        "is_gpt":is_gpt,
        "next_time": int(time.time())+3600*2,
        "alarm_balance": alarm_balance,
        "lang":lang,
        "enable_alarm":enable_alarm
    }
    record = await TBalanceAlarm.filter(uid=uid,is_gpt=is_gpt).exists()
    if record:
        await TBalanceAlarm.filter(uid=uid,is_gpt=is_gpt).update(**values)
    else:
        await TBalanceAlarm.create(**values)

    return True


async def get_user_balance(uid):
    user_info = await UserInfo.filter(uid=uid).first().values("balance")
    data = {"balance":user_info.get("balance")}
    return data


async def band_email_func(uid,email):
    user = await TUser.filter(uid=uid,email=email).first()
    if user:
        user.email = email
        await user.save()
        return True
    else:
        await TUser.filter(uid=uid).update(email=email)
    return True
