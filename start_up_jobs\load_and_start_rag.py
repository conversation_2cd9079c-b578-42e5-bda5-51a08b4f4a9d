# -*- coding: utf-8 -*-
# @Time    : 2024/9/11 11:13
# <AUTHOR> hzx1994
# @File    : load_and_start_rag.py
# @Software: PyCharm
# @description:
class File():
    def __init__(self,file_name):
        self.filename = file_name

async def start_job():
    from utils import Tools
    from controllers.knowledge import upload_to_rag_backend
    from models.db.gpt import TKnowledgeFile,TKnowledgeBase,TTokenMapping,TToken
    import json,asyncio,random
    rag_params_list_key = "unfinished_rag_list"
    async def load_unfinished_rag():
        if await Tools.redis.exists(rag_params_list_key):
            return
        Tools.log.info("load_unfinished_rag")
        files = await TKnowledgeFile.filter(in_db__gt=1).values("kb_base_id", 'uid', 'kb_name', 'file_name')

        await Tools.redis.lpush(rag_params_list_key, *[json.dumps(file) for file in files])

    await asyncio.sleep(random.randint(0, 200)/100)
    await load_unfinished_rag()
    Tools.log.info("start_job")
    # async with Tools.redis.lock():
    while value:=await Tools.redis.lpop(rag_params_list_key):
        value = json.loads(value)
        file = File(file_name=value.get("file_name"))
        base = await TKnowledgeBase.filter(id=value.get("kb_base_id")).first().values("token_id")
        token = await TTokenMapping.filter(id=base.get("token_id")).first().values("external_token_id")
        _ = await TToken.filter(id=token.get("external_token_id")).first().values("value")
        await upload_to_rag_backend([file],value.get("kb_base_id"),_.get("value"), Tools.config.kb.base_url, value.get("kb_name"), value.get("uid"))