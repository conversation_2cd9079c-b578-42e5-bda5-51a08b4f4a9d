# -*- coding: utf-8 -*-
import asyncio
import hashlib
import pickle
import urllib.parse
import os.path
import random
import time
from pathlib import Path as Pathlib
from asyncio import TimeoutError
from typing import List

import aiofiles
import aiohttp
from pandas._config import display
from regex import S
import requests
from fastapi import APIRouter, Header
from fastapi.params import Path, Depends, Query, Form, Body
from starlette.background import BackgroundTask
from starlette.requests import Request
from starlette.responses import FileResponse, RedirectResponse, Response

from controllers.pay import ChinaGpayAliplayHandler, StripePaymentApiHandler, ReceiptApi, WechatPayHandler, \
    StripeAliPay, add_pay_ref
from controllers.proxy import buy_traffic, change_iproyal_ip, get_id_from_text, get_traffic_alarm,  get_traffic_consume_log, get_traffic_consume_log_summary, get_traffic_payway_list, get_traffic_pool, get_traffic_pool_remaining, get_traffic_pool_summary,  save_dynamic_proxy_line, switch_tokens, get_countries, get_states, get_cities, get_payway_list, rec_celery_res, \
    static_ip_verify, get_statistics, get_charts_list, on_off_token, del_token, get_charges_records, change_remark, \
    change_auto_renew_status, get_qr_code, get_paway_id, download_times_add_1, get_extension, cache_area, \
    get_announcements, build_gift_url, del_token_batch, get_cost, cache_or_get_email_info, create_quick_access, \
    get_quick_access, delete_quick_access, update_quick_access, set_auto_rotation, get_secret,on_off_tokens, update_traffic_alarm
from controllers.pubilc import test_user_action
from controllers.user import get_current_active_user
from models.conf import CacheSaveWhere, Language
from models.db.proxy import TAccessories, TProxyHost, TUserToken
from models.response import suc_data, BaseData, fail_data, StateCode
from models.proxy import GenerateType, ProxyType, BaseParamsQuery, PayWayParams, Task, StaticParams, Protocol, \
    StatisticsType, DynamicByIpForm, BaseParamsForm, UseIn, ToolType, Platform, ProxyStatus, VmType, TokenIn, \
    QuickAccessIn
from models.user import UserOut
from utils import Tools, cache, random_string,  is_from_gpt

app = APIRouter()


@app.get("/payway/{payway_name}", description="get payway msg | 获取支付方式信息",
         summary="获取支付方式信息|get payway msg", )
async def get_payway(request: Request, payway_name: PayWayParams = Path(..., title="代理类型",
                                                                        description="获取payway_id ,static_data_center 为数据中心，static_residential为静态住宅")
                     , user=Depends(get_current_active_user)):
    result = await get_payway_list(payway_name)
    return suc_data(data={"data": result})


@app.post("/proxy/dynamic/traffic", description="创建动态流量", summary="创建动态流量", response_model=BaseData)
async def get_dynamic_traffic(request: Request, params: StaticParams = Depends(StaticParams),
                              protocol: Protocol = Form(Protocol.http, description="协议 http socks5"),
                              user: UserOut = Depends(get_current_active_user),
                              s: int = Form(None, description="线路 1 2")):
    if resp := await test_user_action(user):
        return resp
    data = {'country': params.country_id or 233, 'state': params.state_id, 'city': params.city_id, 'uid': user.uid,
            "protocol": protocol.value,
            'lang': "cn", 's': s}
    # if params.country_id==0:
    #     return fail_data(code=4004,msg="must to choice country")
    task_name = Task.dynamic_traffic_proxy
    suc_times = 0
    for i in range(params.generate_times):
        res = await rec_celery_res(task_name, data)
        if res.code == 0:
            suc_times += 1
            token_id = res.data.get("data").get("token_id")
            await save_dynamic_proxy_line(token_id,s)
    if suc_times > 0:
        return suc_data(msg=f"success {suc_times} times")

    return res
    # return await rec_celery_res(task_name, data)


@app.post("/proxy/dynamic/common/traffic", description="创建动态流量-通用代理生成", summary="创建动态流量-通用代理生成",
          response_model=BaseData)
async def build_common(s:int=Form(2,title='线路'),only_country: bool = Form(True, description="精确到国家| 精确到州/省、城市",
                                                 title="精确到国家| 精确到州/省、城市"),protocol: Protocol = Form(Protocol.http, description="协议 http socks5"),
                       user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    data = {'only_country': only_country, "uid": user.uid,"protocol":protocol.value,"s":s}
    task_name = Task.dynamic_traffic_blur_proxy

    res = await rec_celery_res(task_name, data)
    if res.code == 0:
        token_id = res.data.get("data").get("token_id")
        await save_dynamic_proxy_line(token_id,s)   
    return res



@app.post("/proxy/dynamic/ip/by_area", description="创建动态ip", summary="创建动态ip")
async def get_dynamic_ip(request: Request, params: StaticParams = Depends(StaticParams),
                         user: UserOut = Depends(get_current_active_user),
                         is_auto_rotation:int=Form(0, description="是否开启自动切换"),
                         protocol: Protocol = Form(Protocol.http, description="协议 http socks5")
                         ):
    """
    createProxyTask tasks.dynamic_ip_proxy.make_proxy_by_area  timeout:100 Task:{'country': '233', 'state': '0', 'city': '0', 'protocol': 'http', 'uid': 10042, 'lang': 'cn'}

    :return:
    """
    if resp := await test_user_action(user):
        return resp
    if protocol.value == "socks5":
        protocol = "socket"
    else:
        protocol = "http"
    data = {'country': params.country_id or 233, 'state': params.state_id, 'city': params.city_id, 'uid': user.uid,
            'lang': "cn", 'protocol': protocol}
    suc_times = 0
    for i in range(params.generate_times):
        task_name = Task.dynamic_traffic_proxy_ip_by_area
        res = await rec_celery_res(task_name, data)
        if res.code == 0:
            suc_times += 1
            if is_auto_rotation:
                await set_auto_rotation(res.data.get("data").get("token_id"),uid=user.uid)
    if suc_times > 0:
        return suc_data(msg=f"success {suc_times} times")
    else:
        return res


@app.put("/token/rotation/{token_id}",summary="自动轮换开关")
async def change_auto_rotation(request: Request, token_id: int, user: UserOut = Depends(get_current_active_user)):
    await set_auto_rotation(token_id, uid=user.uid)
    return suc_data()

@app.post("/proxy/dynamic/ip/by_ip", description="创建动态ip,根据ip", summary="创建动态ip,根据ip")
async def get_dynamic_ip(request: Request, data: DynamicByIpForm = Depends(),
                         user: UserOut = Depends(get_current_active_user)):
    """
    createProxyTask tasks.dynamic_ip_proxy.make_proxy_by_area  timeout:100 Task:{'country': '233', 'state': '0', 'city': '0', 'protocol': 'http', 'uid': 10042, 'lang': 'cn'}

    :return:
    """
    if resp := await test_user_action(user):
        return resp
    data = {'ip_addr': data.ip, 'result_same_area': data.result_same_location, 'protocol': data.protocol,"uid":user.uid}
    task_name = Task.dynamic_traffic_proxy_ip_by_ip
    return await rec_celery_res(task_name, data)


@app.post("/proxy/static/ip", description="创建静态ip", summary="创建静态ip")
async def get_static_ip(request: Request, base_params: BaseParamsForm = Depends(),
                        params: StaticParams = Depends(StaticParams), user: UserOut = Depends(get_current_active_user),
                        payway_id: int = Form(None, title="支付方式id", description="支付方式id")
                        , is_auto_renew=Form(0, title="是否自动续费", description="0:否 1:是")):
    """
    Task tasks.static_ip_proxy.buy_private_static_ip
    data = {'uid': uid, 'payway_id': payway_id, 'is_data_center': is_data_center, 'country_id': country_id,
        'protocol': "socks5" if is_socks5 else "http", 'lang': self.get_lang(),'is_auto_renew':is_auto_renew}

    :return:
    """
    if resp := await test_user_action(user):
        return resp
    task_name = Task.buy_private_static_ip.value
    data = {'uid': user.uid, 'payway_id': payway_id, 'is_data_center': base_params.is_data_center,
            'country_id': params.country_id,
            'protocol': base_params.protocol.value, 'is_auto_renew': is_auto_renew}
    resp =  await rec_celery_res(task_name, data)
    if resp.code != 0:
        return resp
    token_id = resp.data.get("data").get("token_id")
    if not base_params.is_data_center:
        await TUserToken.filter(id=token_id).update(url="us.proxy302.com")
    token = await TUserToken.filter(id=token_id).first().values("passwd", "username", "url")
    
    return suc_data(data=token)

@app.get("/proxy/host",summary="获取代理主机")
async def get_proxy_host(request: Request):
    data = await TProxyHost.filter(enable=1).all().values("host","id","name")
    return suc_data(data=data)

@app.put("/proxy/host",summary="更新代理主机")
async def update_proxy_host(request: Request,host_id:int=Body(...,description="代理主机id"),token_id:int=Body(...,description="代理id")):
    host = await TProxyHost.filter(id=host_id).first()
    if not host:
        return fail_data(code=4004,msg="代理主机不存在")
    await TUserToken.filter(id=token_id).update(url=host.host)
    return suc_data()

@app.post("/proxy/static/traffic", description="创建静态流量", summary="创建静态流量", response_model=BaseData)
async def get_static_traffic(request: Request,
                             is_data_center: bool = Form(True, description="是否数据中心", title="是否数据中心"),
                             params: StaticParams = Depends(StaticParams)
                             , user=Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    if is_data_center == 0:
        task_name = Task.static_traffic_proxy.value
    else:
        task_name = Task.static_data_center_traffic_proxy.value

    data = {'country': params.country_id, 'state': params.state_id, 'city': params.city_id, 'uid': user.uid,
            'protocol': params.protocol,"s":params.s}
    suc_times = 0
    for i in range(params.generate_times):
        res = await rec_celery_res(task_name, data)
        if res.code == 0:
            suc_times += 1
            if is_data_center == 0:
                token_id = res.data.get("data").get("token_id")
                await TUserToken.filter(id=token_id).update(url="us.proxy302.com")
    if suc_times > 0:
        return suc_data(msg=f"success {suc_times} times")

    return res


@app.get("/extension/info", description="获取插件需要扩展信息", summary="获取插件扩展信息")
async def get_extension_info(token_id:int=Query(...,description="代理id")):
    data= {"data" :await get_extension(token_id)}
    return suc_data(data=data)

@app.get("/tokens/{generate}/{proxy_type}", summary="获取代理列表")
@app.get("/output/tokens/{generate}/{proxy_type}", summary="获取代理列表,批量导出")
async def get_tokens(request: Request, generate: GenerateType = Path(..., description="生成类型"),
                     proxy_type: ProxyType = Path(..., description="代理类型"),
                     user: UserOut = Depends(get_current_active_user),
                     page: int = Query(1, description="page"), page_size: int = Query(10, description="page_size"),
                     text: str = Query("", description="文本模糊查询，包括ip username password 等等"),
                     status: ProxyStatus = Query(ProxyStatus.all.value, description="""all = 0 normal = 1 invalid = 2 expired = 3 unhealthy = 4 on = 5 off = 6"""),
                     created_start: int = Query(0, description="创建开始时间"),
                     created_end: int = Query(0, description="创建结束时间"),
                     is_deleted: bool = Query(False, description="是否删除"),
                     file_type:str = Query("csv",description="文件类型"),
                     filter_type:str = Query("",description="搜索类型"),
                     sort_field:str = Query("id"),
                     sort_type:str = Query("desc")

                     ):
    """
    获取代理列表
    :param generate: 生成类型
    :param proxy_type: 代理类型
    \n
    status :
    normal = 0
    invalid = 1
    expired = 2
    unhealthy = 3
    \n
    return: 代理列表 List[dict]
    """
    if not sort_field:
        sort_field = "id"
    if not sort_type:
        sort_type = "desc"
        
    if page_size == 9999999:
        await Tools.redis.hset("PAGE_SIZE_999999",user.uid,1)
        return suc_data()
    is_output = request.url.path.startswith("/proxy/output")
    if is_output:
        page_size = 10000
        if user.uid in (64192,65177):
            page_size = 99999
        
        cls = switch_tokens(generate, proxy_type, uid=user.uid, page=page, page_size=page_size, is_deleted=is_deleted,is_output=is_output
                            , created_start=created_start, created_end=created_end+3600*24, text=text, status=1,filter_type=filter_type,
                            sort_field=sort_field,sort_type=sort_type
                            )
        name = ".".join([random_string(13),file_type])
        file_path = await cls.download(name)
        # back = BackgroundTask(lambda :file_path.unlink())
        name = f"tmp/{name}"
        async with aiofiles.open(file_path,"rb") as f:
            content = await f.read()
        await Tools.redis.set(name,content,ex=5*60)
        url = f"https://{Tools.config.service_host}/proxy/static?name={name}"

        return suc_data(data = {"url":url})
    else:
        cls = switch_tokens(generate, proxy_type, uid=user.uid, page=page, page_size=page_size, is_deleted=is_deleted,is_output=is_output
                            , created_start=created_start, created_end=created_end, text=text, status=status,filter_type=filter_type
                            , sort_field=sort_field,sort_type=sort_type
                            )
        return suc_data(data=await cls())
    

@app.post("/output/tokens/{generate}/{proxy_type}", summary="获取代理列表,批量导出")
async def _get_tokens(request: Request, generate: GenerateType = Path(..., description="生成类型"),
                     proxy_type: ProxyType = Path(..., description="代理类型"),
                     user: UserOut = Depends(get_current_active_user),
                     created_start: int = Body(0, description="创建开始时间"),
                     created_end: int = Body(0, description="创建结束时间"),
                     selected_ids:list=Body([]),
                     file_type:str=Body("csv")

                     ):
    """
    获取代理列表
    :param generate: 生成类型
    :param proxy_type: 代理类型
    \n
    status :
    normal = 0
    invalid = 1
    expired = 2
    unhealthy = 3
    \n
    return: 代理列表 List[dict]
    """
    page_size = 5000
    filter_type = ""
    page= 1
    Tools.log.debug("selected_ids_view")
    Tools.log.debug(selected_ids)
    created_end += 24*3600 
    
        
    cls = switch_tokens(generate, proxy_type, uid=user.uid, page=page, page_size=page_size, is_deleted=False,is_output=True
                        , created_start=created_start, created_end=created_end, text=None, status=1,filter_type=filter_type,selected_ids=selected_ids
                        )
    
    name = ".".join([random_string(13),file_type])
    file_path = await cls.download(name)
    # back = BackgroundTask(lambda :file_path.unlink())
    name = f"tmp/{name}"
    async with aiofiles.open(file_path,"rb") as f:
        content = await f.read()
    await Tools.redis.set(name,content,ex=5*60)
    url = f"https://{Tools.config.service_host}/proxy/static?name={name}"

    return suc_data(data = {"url":url})



@app.get("/token/qr_code", summary="获取二维码")
async def get_qrcode(req_url: str = Query(..., description="代理url"),
                     network: Protocol = Query(Protocol.http, description="网络类型"), ):
    """
    获取代理二维码
    :param req_url: 代理url
    :return: 二维码
    """
    return suc_data({"value": get_qr_code(req_url, network.lower())})

@app.put("/token/status/batch/{switch}", summary="批量更新代理状态")
async def change_tokens_status_batch(token_in:TokenIn,user: UserOut = Depends(get_current_active_user),switch=Path(...,description="开关 on|off")):
    await on_off_tokens(user.uid,switch,token_in.tokens)
    return suc_data()


@app.put("/token/status/{token_id}", summary="更新代理状态")
@app.delete("/token/{token_id}", summary="删除代理")
async def change_token_status(request: Request, token_id: int = Path(..., description="代理id"),
                              user: UserOut = Depends(get_current_active_user)):
    """
    更新或者删除代理
    """
    if request.method == "PUT":
        await on_off_token(user.uid, token_id)
    else:
        await del_token(user.uid, token_id)
    return suc_data()

@app.delete("/tokens", summary="批量删除代理")
async def del_tokens(token_ids:TokenIn,user: UserOut = Depends(get_current_active_user)):
    await del_token_batch(user.uid, token_ids.tokens)
    return suc_data()


@app.post("/token/renew/{token_id}", summary="代理续费")
async def renew_token(request: Request, token_id: int = Path(..., description="代理id"),
                      payway_id: int = Form(..., description="支付方式id"),
                      is_auto_renew: bool = Form(False, description="是否自动续费")
                      , user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    task_name = "tasks.static_ip_proxy.renew_private_static_ip"
    data = {'uid': user.uid, 'token_id': token_id, 'payway_id': payway_id, "is_auto_renew": is_auto_renew,
            "execute_user": user.uid}
    await change_auto_renew_status(token_id, is_auto_renew, payway_id=payway_id, uid=user.uid)
    return await rec_celery_res(task_name, data)


@app.put("/token/renew/{token_id}", summary="修改代理续费状态")
async def renew_token(request: Request, token_id: int = Path(..., description="代理id"),
                      user: UserOut = Depends(get_current_active_user)):
    await change_auto_renew_status(token_id, is_auto_renew=None, uid=user.uid)
    return suc_data()


@app.put("/token/remark/{token_id}", summary="更新备注")
async def change_token_remark(request: Request, token_id: int = Path(..., description="代理id"),
                              user: UserOut = Depends(get_current_active_user),
                              remark: str = Form("", description="备注")):
    """
    删除备注
    """
    await change_remark(user.uid, token_id, remark)
    return suc_data()


@app.post("/test", summary="代理测速")
def test_proxy(request: Request,
                     proxy_url: str = Form(..., description="代理url"),
                     protocol: Protocol = Form(Protocol.http, description="协议"), ):
    proxy_url = ("http://" if protocol == Protocol.http and proxy_url.endswith("2222") else "socks5h://") + proxy_url
    url = "https://proof.ovh.net/files/1Mb.dat"
    Tools.log.debug(f"url :{url}")
    Tools.log.debug(f"proxy_url: {proxy_url}")
    proxies = {
        "https": proxy_url,
        "http": proxy_url
    }
    try:
        # 直接发送 GET 请求，开始计时
        start = time.time()
        res = requests.get(url, proxies=proxies, timeout=15)
        if res.status_code != 200:
            Tools.log.error(res)
            return fail_data(msg="connection error", code=StateCode.ConnectionError.value)
    except requests.exceptions.Timeout:
        return fail_data(msg="timeout error",code=StateCode.ConnectionTimeOut.value)
    except Exception:
        Tools.log.exception("unknown connection error") 
        return fail_data(msg="unknown connection error",code=StateCode.ConnectionError.value)
    end = time.time()

    speed = (1024000 / (end - start)*0.7) / 1024 *15
    if speed > 5000:
        speed = speed * 0.3
    return suc_data(data={"speed": ("%.2f KB/s" % speed)})


@app.get("/download", summary="下载测速使用", include_in_schema=False)
async def down_load_file():
    return FileResponse("download_files/test1Mb.db")


## /proxy/static/proxy302_logo.png
@app.get("/static", summary="获取静态文件")
async def download_file(id:int=Query(0, description="文件id"),name: str = Query(..., description="文件名")):
    
    content = await Tools.redis.get(name)
    filenames = name.split('/')
    if content:
        await Tools.redis.delete(name)
        return Response(content, media_type="application/octet-stream", headers={
            'Content-Disposition': f'attachment; filename="{filenames[-1]}"'
        })
    file = os.path.join("download_files", *filenames)
    back = None
    if filenames[0] == "tmp":
        back = lambda : Pathlib(file).unlink()
    if id:
        asyncio.ensure_future(download_times_add_1(id))
    media_type = None
    if filenames[-1].endswith("png"):
        media_type = "image/png"
    return FileResponse(file, filename=filenames[-1],media_type=media_type)


@app.get("/static/image",summary="获取图片验证码")
async def download_image(request:Request, code:str = Query(...,description="随机字符串")):
    try:
        length = await Tools.redis.llen("cache_img")
        random_index = random.randint(0, length - 1)
        img,file_content = pickle.loads(await Tools.redis.lindex("cache_img", random_index))
        # img,file_content = random.choice(Tools.cache_img)
            
        await cache_or_get_email_info(code, img.split(".")[0], request)

        return Response(file_content, media_type="image/jpeg")
            
    except OSError as e:
        Tools.log.error(f"File operation error: {str(e)}")
        return fail_data(msg="System error")
    except Exception as e:
        Tools.log.error(f"Unexpected error: {str(e)}")
        return fail_data()


@app.get("/file/{tool_type}", summary="获取对应文件")
async def get_file(tool_type: ToolType = Path(..., description="工具类型， 浏览器插件| 辅助工具"),
                   user: UserOut = Depends(get_current_active_user),
                   platform: Platform = Query(Platform.chrome, title="平台", description="平台")):
    """
    获取国家列表
    :return: 国家列表 List[dict]
    """
    if tool_type == ToolType.browser:
        filename = "download_files/test1Mb.db"
    return suc_data()


@app.get("/area/countries", summary="获取国家列表")
async def area_countries(name_or_code=Query(None, title="国家名称|code"),
                         user: UserOut = Depends(get_current_active_user)
                         , params: BaseParamsQuery = Depends(),
                         use_in: UseIn = Query(..., description="要使用在哪个代理创建", title="")):
    """
    获取国家列表
    :return: 国家列表 List[dict]
    """

    countries = await get_countries(name_or_code,use_in=use_in, **params.to_dict())
    return suc_data(data=countries)


@app.get("/area/states", summary="获取state列表")
async def area_state(country_id: int = Query(..., title="国家id", description="国家id"),
                     state_name=Query(None, title="state名称", description="state名称,用于模糊查询"),
                     params: BaseParamsQuery = Depends(), user: UserOut = Depends(get_current_active_user),
                     use_in: UseIn = Query(..., description="要使用在哪个代理创建", title="")):
    """
    获取state列表
    :return: state List[dict]
    """
    states = await get_states(country_id, state_name, **params.to_dict(),use_in=use_in)
    return suc_data(data=states)


@app.get("/area/cities", summary="获取city列表")
async def area_state(state_id: int = Query(..., title="state_id", description="state_id,必填"),
                     city_name=Query(None, title="city名称", description="city名称,用于模糊查询"),
                     params: BaseParamsQuery = Depends(), user: UserOut = Depends(get_current_active_user),
                     use_in: UseIn = Query(..., description="要使用在哪个代理创建", title="")):
    """
    获取state列表
    :return: state List[dict]
    """
    states = await get_cities(state_id, city_name, **params.to_dict(),use_in=use_in)
    return suc_data(data=states)


@app.get("/summary/proxies/{proxy_type}/{statistics_type}", summary="24小时流量用量汇总| 30天流量用量汇总")
async def summary(proxy_type: ProxyType = Path(..., title="代理类型,ip或者流量"),
                  statistics_type: StatisticsType = Path(..., title="获取类型，天还是hour "),
                  user: UserOut = Depends(get_current_active_user)):
    res = await get_statistics(user.uid, statistics_type, proxy_type)
    return suc_data(data=res)

@app.get("/summary/cost/{statistics_type}", summary="24小时消耗用量汇总| 30天消耗用量汇总")
async def cost(statistics_type: StatisticsType = Path(..., title="获取类型，天还是hour "),
                  user: UserOut = Depends(get_current_active_user),start_time: int = Query(0) ,end_time:int = Query(0)):
    if not start_time:
        start_time =  int(time.time() - 24 * 60 * 60 * 7)
    if not end_time:
        end_time = int(time.time())
    res = await get_cost(user.uid, statistics_type,start_time,end_time)
    return suc_data(data=res)


@app.get("/summary/traffic_pool",summary="流量池流量消耗统计")
async def traffic_pool(user: UserOut = Depends(get_current_active_user),start_time: int = Query(0) ,end_time:int = Query(0)):
    if not start_time:
        start_time =  int(time.time() - 24 * 60 * 60 * 7)
    if not end_time:
        end_time = int(time.time())
    res = await get_traffic_pool_summary(user.uid,start_time,end_time)
    return suc_data(data=res)


# ----------- 浏览器插件 -----------
@app.get("/browser/plugin", summary="获取浏览器插件列表")
async def _get_plugin_msg(Lang:str=Header(..., description="语言",title="语言")):
    # 改用数据库记录存储，不硬编码
    is_zh = Lang.startswith("zh")
    res = await TAccessories.filter(type="browser_plugin",deleted_on=0).all().order_by("id")
    records = [{"label": i.label if is_zh else i.en_label , "child": [dict(i)]} for i in res]
    if not is_zh :
        for record in records:
            for c in record['child']:
                c['title'] = c.get("en_title")
                c['detail'] = c.get("en_detail")



    return suc_data(data={"records": records})


@app.get("/tools", summary="获取辅助工具列表")
@cache(CacheSaveWhere.redis)
async def get_tools():
    res = await TAccessories.filter(type="tools",deleted_on=0).all()
    winwin_child = []
    mac_child = []
    for i in res:
        if i.label == "Windows":
            winwin_child.append(i.__dict__)
        if i.label == "Mac":
            mac_child.append(i.__dict__)
    records = [
        {
            "label": "Windows",
            "child": winwin_child
        },

        {
            "label": "Mac",
            "child": mac_child
        }
    ]

    return suc_data(data={"records": records})


# ----------- 充值接口 -----------

@app.get("/charges/record/{order_id}", summary="获取充值账单")
async def order_id_msg(order_id: int = Path(..., description="订单id"),
                       user: UserOut = Depends(get_current_active_user)):
    """
    获取充值账单
    """
    api = ReceiptApi(uid=user.uid, order_id=order_id)
    return suc_data(await api.get_order())


@app.get("/charges/records", summary="获取账单列表")
async def charges_records(request: Request,user: UserOut = Depends(get_current_active_user),
                          page: int = Query(1, description="页码", title="页码")
                          , page_size: int = Query(10, description="页大小", title="页大小")):
    """
    获取账单
    """
    is_gpt = is_from_gpt(request)
    return suc_data(await get_charges_records(user.uid, page=page, page_size=page_size,is_gpt=is_gpt))


@app.get("/charges/list", summary="获取充值列表（充值选项）")
async def get_charges(request: Request,user: UserOut = Depends(get_current_active_user)):
    """
    获取充值列表
    """
    is_gpt = bool(request.headers.get("Isgpt"))

    return suc_data({"values": await get_charts_list(user.uid,is_gpt=is_gpt)})

@app.get("/charges/{id}", summary="通过id获取充值明细")
async def get_charges(request: Request,user: UserOut = Depends(get_current_active_user),id:int=Path(...)):
    """
    获取充值列表
    """
    is_gpt = bool(request.headers.get("Isgpt"))
    charts = await get_charts_list(user.uid, is_gpt=is_gpt, payway_id=id)
    return suc_data(data=charts[0])


@app.post("/charges/alipay", summary="支付宝充值")
async def pay(request: Request, payway_id: int = Form(..., description="代理类型", title="代理类型"),
              user: UserOut = Depends(get_current_active_user)):
    """
    充值,逻辑后补充
    """
    is_gpt = bool(request.headers.get("Isgpt"))
    now = int(time.time())
    st = 1706018400
    ed = 1706020200
    if now >= st and now<=ed:
        return fail_data(code=99)
    if resp := await test_user_action(user):
        return resp
    pay = ChinaGpayAliplayHandler(user.uid, payway_id=payway_id, host=request.client.host,is_gpt=is_gpt)
    await add_pay_ref(request, is_gpt=is_gpt, uid=user.uid)
    return suc_data(await pay.post())

@app.post("/charges/wechat_pay", summary="微信支付充值")
async def wechat_pay(request: Request, payway_id: int = Form(..., description="代理类型", title="代理类型"),
              user: UserOut = Depends(get_current_active_user)):
    """
    充值,逻辑后补充
    """
    is_gpt = bool(request.headers.get("Isgpt"))
    if resp := await test_user_action(user):
        return resp
    pay = WechatPayHandler(user.uid, payway_id=payway_id, host=request.client.host,is_gpt=is_gpt)
    await add_pay_ref(request, is_gpt=is_gpt, uid=user.uid)
    return suc_data({"path":await pay.build()})


@app.post("/charges/wechat/pay/{path}",summary="根据path获取微信支付的配置信息")
async def get_data_from_path(request: Request,path:str):
    is_gpt = bool(request.headers.get("Isgpt"))
    pay = WechatPayHandler( host=request.client.host, is_gpt=is_gpt)
    value =await pay.get_from_path(path)
    if not value:
        return fail_data(code=StateCode.Pay_Qr_Time_Out)

    return suc_data(data=value)

# 设置自动续费的stripe，加上绑定信用卡
@app.post("/charges/stripe/info", summary="stripe,信用卡充值,设置金额，余额，是否启用")
async def pay(request: Request, 
            amount:float = Form(..., description="金额", title="充值的金额"),
            balance: float = Form(..., description="余额", title="最低的余额"),
            enable: int = Form(0, description="是否启用", title="是否启用"),
            user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    await StripePaymentApiHandler(is_gpt=is_gpt, uid=user.uid).create_band_card(amount=amount,balance=balance,enable=enable)
    return suc_data()

@app.get("/charges/stripe/callback/{session_id}", summary="stripe,绑卡成功的回调")
async def _callback(request: Request,session_id: str):
    is_gpt = is_from_gpt(request)
    await StripePaymentApiHandler(is_gpt=is_gpt).band_call_back(session_id=session_id)

    return RedirectResponse(url=f"https://{Tools.config.gpt_dashboard_host}/charge" if is_gpt else f"https://{Tools.config.dashboard_host}/charge")

@app.delete("/charges/stripe/band", summary="stripe,解绑")
async def _deleted(request: Request,user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    await StripePaymentApiHandler(is_gpt=is_gpt, uid=user.uid).delete_band_card()
    return suc_data()


@app.post("/charges/stripe/band", summary="stripe,绑定")
async def _band(request: Request,user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    url = await StripePaymentApiHandler(is_gpt=is_gpt, uid=user.uid).create_portal_session()
    return suc_data(data={"url":url})

@app.get("/charges/stripe/info", summary="stripe,获取绑卡的信息，主要是余额和开关状态")
async def _band_info(request: Request,user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    return suc_data(data=await StripePaymentApiHandler(is_gpt=is_gpt, uid=user.uid).get_band_info())


@app.post("/charges/stripe_wechat", summary="stripe,信用卡充值wechat_pay")
async def pay(request: Request, payway_id: int = Form(..., description="代理类型", title="代理类型"),
              user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    pay = StripePaymentApiHandler(user.uid, payway_id=payway_id, host=request.client.host,is_gpt=is_gpt
                                  ,is_wechat_pay=True,is_cny=True,email=user.email)
    await add_pay_ref(request,is_gpt=is_gpt,uid=user.uid)
    return suc_data(await pay.post(is_gpt=is_gpt))

@app.post("/charges/stripe_alipay")
async def pay_alipay(request: Request, payway_id: int = Form(..., description="代理类型", title="代理类型"),
              user: UserOut = Depends(get_current_active_user)):
    is_gpt = is_from_gpt(request)
    if resp := await test_user_action(user):
        return resp
    pay = StripeAliPay(user.uid, payway_id=payway_id, host=request.client.host,is_gpt=is_gpt
                                  ,is_wechat_pay=True,is_cny=True,email=user.email)
    await add_pay_ref(request, is_gpt=is_gpt, uid=user.uid)
    return suc_data(await pay.post(is_gpt=is_gpt))



@app.post("/charges/stripe", summary="stripe,信用卡充值")
async def pay(request: Request, payway_id: int = Form(..., description="代理类型", title="代理类型"),device_type:str=Form("",description="设备类型",title="设备类型"),
              user: UserOut = Depends(get_current_active_user)):
    """
    充值,逻辑后补充
    """
    is_gpt = is_from_gpt(request,is_from_gpt(request))
    kw = {"device_type":device_type}
    if resp := await test_user_action(user):
        return resp
    pay = StripePaymentApiHandler(user.uid, payway_id=payway_id, host=request.client.host,is_gpt=is_gpt,email=user.email,**kw)
    await add_pay_ref(request, is_gpt=is_gpt, uid=user.uid)
    return suc_data(await pay.post(is_gpt=is_gpt))


# ----------- 充值接口 -----------


# ----------- vm -----------
@app.post("/vm", summary="创建虚拟机")
async def create_vm(country_id: int = Form(..., description="国家id"), days: int = Form(..., description="天数"),
                    vm_conf_id: int = Form(..., description="虚拟机配置id")
                    , auto_renew: bool = Form(False, description="是否自动续费"),
                    user: UserOut = Depends(get_current_active_user)):
    """
    创建虚拟机
    """
    if resp := await test_user_action(user):
        return resp
    task_name = Task.create_vm
    payway_id = await get_paway_id(days, vm_conf_id)
    data = {"country_id": country_id, "payway_id": payway_id, "auto_renew": auto_renew, 'vm_username': "proxy302","uid": user.uid,
            'vm_password': "Proxy302.com", }
    res = await Tools.vm_celery.send_task(task_name, kwargs=data)
    if not res:
        return fail_data(code=StateCode.GenerationError.value)
    return res


@app.get("/vm", summary="获取虚拟机")
async def get_vm(user: UserOut = Depends(get_current_active_user)):
    """
    获取虚拟机
    """
    task_name = Task.vm_list
    data = {
        "uid": user.uid
    }
    res = await Tools.vm_celery.send_task(task_name, kwargs=data)
    print(res)
    for i in res['data']['vm_tokens']:
        i['expired_on'] = i['created_on'] + i['life_time'] * 24 * 60 * 60
        if i['expired_on'] < time.time():
            i["expired"] = True
        else:
            i["expired"] = False
    return res
    # return suc_data()


@app.get("/vm/execute/{action}", summary="操作虚拟机")
async def start_vm(action: VmType = Path(..., title="操作类型"), vm_id: int = Query(..., title="虚拟机id"),
                   user: UserOut = Depends(get_current_active_user)):
    """
    启动虚拟机
    """
    if action == VmType.cancel:
        await TUserToken.filter(id=vm_id).update(is_static=0)
        return suc_data()
    task_name = Task.vm_start
    data = {
        'uid': user.uid,
        'id': vm_id,
        'action': action,
    }
    return await rec_celery_res(task_name, data,celery=Tools.vm_celery)

    # return suc_data()

@app.get("/change/iproyal/ip",summary="更换iproyal的ip")
async def _change_iproyal_ip(order_id:int=Query(...,title="订单id")):
    """
    更换iproyal的ip
    """
    # ids = get_id_from_text()
    # for order_id in ids:
        # await change_iproyal_ip(order_id)
    await change_iproyal_ip(order_id)
    return suc_data()

@app.get("/vm/renew/auto", summary="自动续费虚拟机")
async def auto_renew_vm(user: UserOut = Depends(get_current_active_user),switch:bool=Query(False, title="是否开启自动续费"),vm_id: int = Query(..., title="虚拟机id"),
days: int = Query(..., description="天数"),
                    vm_conf_id: int = Query(..., description="虚拟机配置id")
                        ):
    """
    自动续费虚拟机
    """
    task_name = Task.vm_auto_renew
    payway_id = await get_paway_id(days, vm_conf_id)
    data = {
        'uid': user.uid,
        'on_off': switch,
        "id":vm_id,
        "payway_id":payway_id,

    }
    return await rec_celery_res(task_name, data,celery=Tools.vm_celery)


@app.get("/vm/renew/manual", summary="手动续费虚拟机")
async def manual_renew_vm(user: UserOut = Depends(get_current_active_user),vm_id: int = Query(..., title="虚拟机id"),
days: int = Query(..., description="天数"),
                    vm_conf_id: int = Query(..., description="虚拟机配置id")
                        ):
    """
    手动续费虚拟机
    """
    task_name = Task.vm_renew
    payway_id = await get_paway_id(days, vm_conf_id)
    data = {
        'uid': user.uid,
        'id':vm_id,
        "payway_id":payway_id,

    }
    return await rec_celery_res(task_name, data,celery=Tools.vm_celery)


@app.get("/vm/remote", summary="远程操作虚拟机")
async def remote_vm(user: UserOut = Depends(get_current_active_user),ip:str = Query(..., title="ip"),
user_name: str = Query(..., description="用户"),
                    password: str = Query(..., description="密码")):
    """
    远程操作虚拟机
    """
    return suc_data(data={"url":f"http://mstsc.proxy302.com/?address={ip}&username={user_name}&password={password}"})

# ----------- vm -----------

@app.get("/cache/new")
async def cache():
    await cache_area()


@app.get("/announcements",summary="公告")
async def announcements(request: Request):
    is_gpt = is_from_gpt(request)
    return suc_data(data=await get_announcements(is_gpt=is_gpt))


@app.get("/invitation/{lang}/{uid}",summary='邀请有礼')
async def gift(request: Request,uid:int=Path(...,description="用户id",title="用户id"),lang:Language=Path(...,description="语言",title="语言")):
    url = await build_gift_url(uid, lang)
    Tools.log.debug(f"invitation:{url}")
    if not url:
        return fail_data(code=StateCode.UserExistError.value)
    if isinstance(url,RedirectResponse):
        return url
    return RedirectResponse(url,status_code=302)

@app.get("/invitation_gpt/{lang}/{uid}",summary='gpt邀请有礼')
async def gift(request: Request,uid:int=Path(...,description="用户id",title="用户id"),lang:Language=Path(...,description="语言",title="语言")):
    url = await build_gift_url(uid, lang,is_gpt=True)
    Tools.log.debug(f"invitation:{url}")
    if not url:
        return fail_data(code=StateCode.UserExistError.value)
    if isinstance(url,RedirectResponse):
        return url
    Tools.log.debug(url)
    return RedirectResponse(url,status_code=302)


# 快捷访问
@app.post("/quick_access",summary="创建快捷访问")
async def quick_access(data:QuickAccessIn,user: UserOut = Depends(get_current_active_user)):
    return await create_quick_access(user.uid,data)

@app.get("/quick_access",summary="快捷访问列表")
async def quick_access_list(user: UserOut = Depends(get_current_active_user),page:int=1,page_size:int=10):
    return await get_quick_access(user.uid,page,page_size)

@app.delete("/quick_access/{id}",summary="删除快捷访问")
async def quick_access_delete(user: UserOut = Depends(get_current_active_user),id:int=Path()):
    return await delete_quick_access(user.uid,id)


@app.put("/quick_access/{id}",summary="编辑快捷访问")
async def quick_access_update(id:int=Path(),user: UserOut = Depends(get_current_active_user),data:QuickAccessIn=Depends()):
    return await update_quick_access(id,user.uid,data)


@app.get("/secret",summary= "获取代理的加密密钥")
async def secret(network:str=Query("HTTP",title="网络类型"),protocol:str=Query("http",title="协议")):
    return suc_data(data=await get_secret(network=network,protocol=protocol))


@app.get("/cloud/phone/{lang}/{uid}",summary="云手机")
def cloud_phone(request: Request,uid:int=Path(...,description="用户id",title="用户id"),lang:str=Path(...,description="语言",title="语言")):
    def build_login_url(user_id, partner, secret_key, timestamp):
        user_id = str(user_id)
        secret = hashlib.sha256((user_id + partner + secret_key + str(timestamp)).encode('utf-8')).hexdigest()

        # 可选项
        locale = lang

        # query参数
        query = {
            # 以下为必须
            'user_id': user_id,
            'partner': partner,
            'timestamp': timestamp,
            'secret': secret,
            # 以下为选填
            'locale': locale,
        }

        # 生成最终网址示例
        login_url = url + '?' + urllib.parse.urlencode(query)
        return login_url
    url = Tools.config.cloud_phone.url
    partner = Tools.config.cloud_phone.partner
    secret_key = Tools.config.cloud_phone.secret_key
    timestamp = int(time.time())
    user_id = uid
    url = build_login_url(user_id, partner, secret_key, timestamp)
    return RedirectResponse(url,status_code=302)

# 流量池


@app.get("/traffic/payway",summary="流量池购买套餐列表")
async def _traffic_payway_list(user: UserOut = Depends(get_current_active_user),type:str=Query(...,description="类型",title="类型")):
    """
    流量池购买套餐列表
    """
    return suc_data(data=await get_traffic_payway_list(type))   
    ...

# 统计流量包剩余流量
@app.get("/traffic/pool/remaining",summary="统计流量包剩余流量")
async def _traffic_pool_remaining(user: UserOut = Depends(get_current_active_user)):
    """
    统计流量包剩余流量
    """
    return suc_data(data=await get_traffic_pool_remaining(user.uid))



@app.get("/traffic/pool",summary="流量池列表,用户购买的流量池，相当于购买日志")
async def _traffic_pool(user: UserOut = Depends(get_current_active_user),page:int=1,page_size:int=10):
    """
    流量池列表
    """
    return suc_data(data= await get_traffic_pool(user.uid,page,page_size))

@app.post("/traffic/buy",summary="流量池购买")
async def _traffic_buy(user: UserOut = Depends(get_current_active_user),payway_id:int=Form(...,description="套餐id",title="套餐id")):
    """
    流量池购买
    """
    rsp = await buy_traffic(user.uid,payway_id)
    return rsp

# 消耗日志
@app.get("/traffic/consume/{id}",summary="消耗日志")
async def _traffic_consume_log(user: UserOut = Depends(get_current_active_user),id:int=Path(...,description="流量池id",title="流量池id"),page:int=1,page_size:int=10):
    """
    消耗日志
    """
    return suc_data(data=await get_traffic_consume_log(user.uid,id,page=page,page_size=page_size))

@app.get("/traffic/consume/log",summary="消耗日志")
async def _traffic_consume_log_without_id(user: UserOut = Depends(get_current_active_user),page:int=1,page_size:int=10):
    """
    消耗日志
    """
    return suc_data(data=await get_traffic_consume_log(user.uid,page=page,page_size=page_size))

@app.get("/traffic/consume/log/summary",summary="消耗日志汇总")
async def _traffic_consume_log_summary_without_id(user: UserOut = Depends(get_current_active_user),start_time:int=Query(...,description="开始时间",title="开始时间"),end_time:int=Query(...,description="结束时间",title="结束时间")):
    """
    消耗日志汇总
    """
    return suc_data(data=await get_traffic_consume_log_summary(user.uid,start_time,end_time))


# @app.get("/traffic/consume/log/summary/{id}",summary="消耗日志汇总")
# async def _traffic_consume_log_summary(user: UserOut = Depends(get_current_active_user),id:int=Path(...,description="流量池id",title="流量池id"),start_time:int=Query(...,description="开始时间",title="开始时间"),end_time:int=Query(...,description="结束时间",title="结束时间")):
#     """
#     消耗日志汇总
#     """
#     return suc_data(data=await get_traffic_consume_log_summary(user.uid,start_time,end_time,id=id))


# 流量报警
@app.get("/traffic/alarm",summary="流量报警")
async def _traffic_alarm(user: UserOut = Depends(get_current_active_user)):
    """
    流量报警
    """
    return await get_traffic_alarm(user.uid)

@app.post("/traffic/alarm",summary="流量报警")
async def _traffic_alarm(user: UserOut = Depends(get_current_active_user),enable:bool=Form(1,description="是否启用",title="是否启用"),
                         traffic:int=Form(...,description="流量",title="流量"),type:str=Form(...,description="类型",title="类型"),alarm_value:str=Form(...,description="报警值",title="报警值")):
    """
    流量报警
    """
    is_ok = await update_traffic_alarm(user.uid,enable,traffic,type,alarm_value)
    if not is_ok:
        return fail_data()
    return suc_data()

