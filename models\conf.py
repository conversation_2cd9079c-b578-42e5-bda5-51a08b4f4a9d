# -*- coding: utf-8 -*-
# @Time    : 2023/8/15 14:33
# <AUTHOR> hzx1994
# @File    : conf.py
# @Software: PyCharm
# @description:
from enum import Enum

from pydantic import BaseModel

class CacheSaveWhere(str, Enum):
    local = "local"  # 直接全局缓存
    redis = "redis"  # redis

class Language(str,Enum):
    zh = "zh"
    en = "en"

class DB(BaseModel):
    db_type: str
    host: str
    port: int
    database: str
    user: str
    password: str
    min_size: int = 3
    max_size: int = 20


class Redis(BaseModel):
    host: str
    port: int
    password: str = None


class Celery(BaseModel):
    broker: str
    backend: str


class ServiceConf(BaseModel):
    http_port: int
    socks5_port: int


class AliPay(BaseModel):
    mode: str
    merchant_name: str
    merchant_id: int
    private_key: str
    public_key: str


class Stripe(BaseModel):
    sk: str
    pk: str
    secret: str

class PartnerShare(BaseModel):
    product_id: int
    data_return_key: str
    user_integration_key: str

class Oss2Config(BaseModel):
    access_key_id: str
    access_key_secret: str
    endpoint: str
    bucket_name: str

class KB(BaseModel):
    base_url: str = 'https://test-api.gpt302.com'
    kb_url: str = ''

class CloudPhone(BaseModel):
    url:str
    partner:str
    secret_key:str

class Adswave(BaseModel):
    base_url:str = "https://testing-api.partnershare.net"
    secret_key:str = "Z8C8PcyM1bzOkuhkjm31Mp3Vh"
    product_key:str = "phh3Hkj8u83"
    target_product_key:str = "izVdrh5g630"

class Config(BaseModel):
    """
    配置文件
    """
    db: DB
    celery: Celery
    redis_url: str
    redis_url_14: str
    redis_url_us: str = "redis://********:6379/0"
    service_conf: ServiceConf
    sendgrid: dict
    reset_url: str
    mongo_url: str = "mongodb://mongodb:27017"
    gpt_host:str= "test-chat.gpt302.com"
    alipay: AliPay
    service_host: str
    gpt_dashboard_host:str='http://test-dash.gpt302.com'
    gpt_dashboard_cn_host:str='http://test-dash_cn.gpt302.com'
    tool_302_base_url:str='https://test-tools.gpt302.com'
    dashboard_host: str
    stripe: Stripe
    stripe_gpt: Stripe
    feishu: dict
    partner_share: PartnerShare
    partner_share_gpt: PartnerShare
    debug:bool=False
    epay_conf:dict = {}
    rly_conf:dict = {}
    app_conf:dict = {}  # 集成回调地址
    gpt_api:str='https://test-api.gpt302.com'
    oss2: Oss2Config
    adswave:dict = {}
    kb:KB
    cloud_phone: CloudPhone
    proxy_github_conf: dict = {}
    ai302_github_conf: dict = {}
    ad_swave:Adswave = Adswave()
