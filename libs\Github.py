# -*- coding: utf-8 -*-
# @Time    : 2024/9/19 17:14
# <AUTHOR> hzx1994
# @File    : Github.py
# @Software: PyCharm
# @description:
import os
from requests_oauthlib import OAuth2Session




# 在开发环境中设置环境变量，允许不安全的 HTTP 传输
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
# 从 GitHub OAuth 应用获取
client_id = 'Ov23lirSSXK2iqJy1oYd'
client_secret = 'd5e92f351a83249c6ebc200b90362fc6beb96060'

# OAuth 2 endpoints 提供的 URLs
authorization_base_url = 'https://github.com/login/oauth/authorize'
token_url = 'https://github.com/login/oauth/access_token'
user_api_url = 'https://api.github.com/user'
emails_api_url = 'https://api.github.com/user/emails'

class Github:

    def __init__(self, client_id=client_id, client_secret=client_secret):
        self.client_id = client_id
        self.client_secret = client_secret
    def login(self):
        github = OAuth2Session(self.client_id, scope="user:email")
        authorization_url, state = github.authorization_url(authorization_base_url)
        return authorization_url,state


    def call_back(self,state,url):
        github = OAuth2Session(self.client_id, state=state)
        token = github.fetch_token(
            token_url,
            client_secret=self.client_secret,
            authorization_response=str(url)
        )

        github = OAuth2Session(self.client_id, token=token)

        # 获取用户的电子邮件
        emails_resp = github.get(emails_api_url)
        emails = emails_resp.json()
        primary_email = next((email for email in emails if email.get('primary')), {}).get('email')

        resp = github.get(user_api_url)
        github_user = resp.json()
        user_name = github_user.get("login")
        user_id = github_user.get("id")
        return user_name,user_id,primary_email