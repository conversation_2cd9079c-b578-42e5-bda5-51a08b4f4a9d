

.alter-state {
    position: fixed;
    z-index: 999999;
    box-shadow: 0px 0px 5px #888888;
}

.alter-state.left-top {
    top: 20px;
    left: 20px;
}

.alter-state.right-top {
    top: 20px;
    right: 20px;
}


.add-proxy-btn {
    width: 43px;
    height: 43px;
    position:relative;
    background: url("/static/img/add_btn.png") no-repeat;
    background-size: 43px 43px;
    border-radius: 50%;
    border: 0;
    border-color: transparent;
    outline: none;
}
.add-proxy-btn:focus{
    outline: none;
}


.row.space {
    margin: 10px 0;
}

.proxy-pwd-item {
    color: white;
    background: rgba(90, 98, 104, 0.6);
    border-radius: 10px;
    padding: 5px 10px;
    margin: 0 5px;
}

.proxy-pwd-edit-icon {
    width: 30px;
    height: 30px;
    color: rgba(90, 98, 104, 0.6);
    margin-left: 10px;
}


.nav-logo{
    width: 192px;
    height: 43px;
    padding: 8px 3px;
}


.copy_btn{
    width: 14px;
    height: 16px;
    border: 0;
    border-color: transparent;
    background: url("/static/img/copy_btn.png") no-repeat;
    background-size: 14px 16px;
    outline: none;
    margin: 0 2px;
}
.copy_btn:focus{
    outline: none;
}

.edit_btn{
    width: 10px;
    height: 10px;
    border: 0;
    border-color: transparent;
    background-color: transparent;
    color: gray;

    /*background: url("/static/img/edit_btn.png") no-repeat;*/
    /*background-size: 16px 16px;*/
    outline: none;
}
.edit_btn:focus{
    outline: none;
}

.qrcode_btn{
    width: 14px;
    height: 16px;
    border: 0;
    border-color: transparent;
    background: url("/static/img/qrcode_btn.png") no-repeat;
    background-size: 14px 16px;
    outline: none;
    margin: 0 2px;
}
.qrcode_btn:focus{
    outline: none;
}