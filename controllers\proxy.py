# -*- coding: utf-8 -*-
# @Time    : 2023/8/25 14:38
# <AUTHOR> hzx1994
# @File    : proxy.py
# @Software: PyCharm
# @description:
import abc
import asyncio
import hashlib
import pickle
import random
import re
import urllib.parse
from pathlib import Path

import aiohttp
import loguru
import pandas as pd
import base64
import datetime
import io
import json
import time
import qrcode
from dateutil.relativedelta import relativedelta
from starlette.requests import Request
from tortoise import  Tortoise
import tortoise
from tortoise.expressions import Q, RawSQL, Subquery, F, Case, When
from tortoise.functions import  Sum, Count

from conf import constants
from controllers.pay import PaymentApiHandler, get_iproyal_order_info
from controllers.pubilc import test_user_action, add_ready_ip_order
from exections import raise_execption
from models.conf import CacheSaveWhere
from models.db.proxy import TIproyalOrder, TProxyHost, TProxyLine, TTrafficAlarm, TTrafficIps, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TGeoCountry, TGeoState, TGeoCity, TPayway, TIpOrders, TProxyIp, TStaticIpRenew, \
    TApiToken, TUserTrafficPool, TVmLevel, TVmLocations, TCountryFilter, TAccessories, TUser, TDynamicIpPool, TProxyMachines, TIps, \
    TProxyUrlGroup, TTokenTag, UserInfo, TUserTrafficIp
from models.proxy import GenerateType, ProxyType, NetworkType, PayWayParams, Protocol, StatisticsType, ProxyStatus, \
    UseIn, PayWays, QuickAccessIn

from models.response import suc_data, fail_data, StateCode
from models.user import UserOut
from utils import Tools, int2ip, to_ptc, random_string, load_func, cache, ip2int, ip2hex, current_timestamp


async def tokens(uid, is_deleted=False, page=1, page_size=10):
    await TUserToken.filter(user_id=uid, is_deleted=is_deleted).order_by("-id").offset((page - 1) * page_size).limit(
        page_size).all()


def switch_tokens(generate_type, proxy_type, **kwargs):
    """
    代理实例工厂
    """
    return {
        (GenerateType.dynamic, ProxyType.traffic): DynamicTrafficTokens,
        (GenerateType.dynamic, ProxyType.ip): DynamicIpTokens,
        (GenerateType.static, ProxyType.traffic): StaticTrafficTokens,
        (GenerateType.static, ProxyType.ip): StaticIpTokens,
    }[(generate_type, proxy_type)](**kwargs)


def get_qr_code(text,network):
    def create_qrcode(text):
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=5,
            border=1,
        )
        qr.make(fit=True)
        qr.add_data(text)
        img = qr.make_image()

        buf = io.BytesIO()
        img.save(buf)
        image_stream = buf.getvalue()
        return base64.b64encode(image_stream).decode('utf-8')

    return create_qrcode(("http" if network == NetworkType.http else "socks") + \
                       "://" + base64.b64encode(text.encode('utf-8')).decode('utf-8').replace("=",""))

class Tokens(abc.ABC):
    is_static = False
    la_id_is_null = False



    def __init__(self, is_deleted=False, page=1, page_size=10, uid=0,
    created_start=0,created_end=0,text='',status=0,filter_type='',**kwargs):
        self.is_deleted = is_deleted
        self.page = page
        self.uid = uid
        is_output = kwargs.get("is_output",False)
        self.selected_ids = kwargs.get("selected_ids")
        if is_output:
            if uid in (64192,65177):
                self.page_size = page_size if page_size < 99999 else 99999
            else:
                self.page_size = page_size if page_size < 5000 else 5000
        else:
            self.page_size = page_size if page_size <= 100 else 100
        self.count = 0
        self.country_dict = {}
        self.state_dict = {}
        self.city_dict = {}
        self.keys = []

        self.created_start = created_start
        self.created_end = created_end
        self.text = text
        self.filter_type = filter_type
        self.status = status
        self.sort_field = kwargs.get("sort_field","id")
        self.sort_type = kwargs.get("sort_type","desc")
        self.kwargs=kwargs

    async def __call__(self, *args, **kwargs):
        data = await self.get()
        return {"total": self.count, "page": self.page, "page_size": self.page_size, "data": data}

    async def get(self):

        res = await self.get_tokens()
        return res

    async def download(self,filename:str):
        records = await self.get()

        list_data = [{
            "IP":i.get("ip_addr",""),
            "Address:Port:Username:Password":i.get("ads_proxy_url"),
            "Country-State(Province)-City":i.get("proxy_area"),
            "Network_Protocol":i.get("network"),
            "Status":ProxyStatus(i.get("status")).name,
        } for i in records]
        path = Path("download_files")/"tmp"
        if not path.exists():
            path.mkdir()
        save = path/filename
        #todo 同步代码
        df = pd.DataFrame(list_data,index=None)
        if filename.endswith("csv"):
            df.to_csv(save)
        else:
            df.to_excel(save)
        return save

    async def generate_token(self):
        """
        构造主sql的函数
        """

        # 其他过滤条件
        others = {}

        if self.la_id_is_null:
            others["la_id"] = 0
        else:
            others["la_id__gt"] = 0
        if self.status:
            if self.status ==1:
                others['proxy__status'] = 1
                others["expired"] = 0
            elif self.status ==2 :
                others['proxy__status'] = 0
            elif self.status == 3:
                others['expired'] = 1
            elif self.status == 4:
                others['proxy__health'] = 0
            elif self.status == 5:
                others['proxy__health'] = 1
            elif self.status == 6:
                others['proxy__health'] = 0



        key = 'deleted_on__gt' if self.is_deleted else "deleted_on"
        others[key] = 0
        others['payway_id'] = 0
        if self.created_end:
            if self.created_start == self.created_end:
                self.created_end = self.created_start+3600*24-1
            others['created_on__gte'] = self.created_start
            others['created_on__lte'] = self.created_end
        ordery_by_key = self.sort_field
        if self.sort_type == 'desc':
            ordery_by_key = '-' + self.sort_field
        query = TUserToken.all(). \
            select_related("proxy"). \
            select_related("la"). \
            select_related("proxy__ip"). \
            filter(user_id=self.uid, is_deleted=self.is_deleted,
                   is_static=self.is_static, network__in=('http', 'socks5','socket')). \
            order_by(ordery_by_key). \
            filter(**others)

        Tools.log.debug("selected_ids")
        Tools.log.debug(self.selected_ids)
        if self.selected_ids:
            query = query.filter(id__in=self.selected_ids)

        if self.filter_type and self.text:
            if self.filter_type == "username":
                query = query.filter(username__icontains=self.text)
            if self.filter_type == "ip":
                query = query.filter(proxy__ip__ip__contains=self.text)
            if self.filter_type == "password":
                query = query.filter(passwd__icontains=self.text)
            if self.filter_type == "location":
                query = query.filter(Q(proxy__country__name__icontains=self.text)|Q(proxy__country__code__icontains=self.text))
            if self.filter_type == "remark":
                query = query.filter(remark__icontains=self.text)
                
            #     country_ids = await TGeoCountry.filter(code__contains=self.text).values("id")
            #     if country_ids:
            #         query = query.filter(Q(proxy__country_id__in=[i.get("id") for i in country_ids]))
            #
            #
            # query = query.filter(Q(username__contains=self.text) | Q(passwd__contains=self.text)
            #                      | Q(proxy__ip__ip__contains= self.text) | Q(proxy__country__name__icontains= self.text)  )

        count = await query.count()
        records = await query.offset((self.page - 1) * self.page_size).limit(self.page_size).values(
            "proxy__ip__ip_addr", "proxy__source",
            "proxy__country__name",
            "proxy__country__code"
            , "proxy__state__name",
            "proxy__city__name",
            "created_on", "traffic_usage", "cost",
            "proxy__health", "proxy__status",
            "expired", "life_time", "network",
            "data_center","la_id",
            "url", "username", "passwd",
            'is_deleted', 'remark', 'id', "proxy__state_id",
            "proxy__country_id","proxy__city_id",'proxy__online'

        )
        return count, records

    def create_area(self,country, code, state, city):
        area = ""
        if country:
            area += country + f" ({code})"
        if state:
            area += f"-{state}"
        if city:
            area += f"-{city}"
        return area
    @load_func()
    async def get_tokens(self) -> dict:
        self.count, records = await self.generate_token()

        def func(health, expired_on, status,create_time,expired):
            """
            处理状态
            """
            if expired or expired_on < time.time() and create_time != expired_on:
                # 过期
                return ProxyStatus.expired
            # if health == 0:
            #     return ProxyStatus.unhealthy
            if status == 0:
                # 失效
                return ProxyStatus.invalid
            return ProxyStatus.normal

        def traffic_usage_func(traffic_usage):
            """
            流量自适应转换单位
            """
            if 0 <= traffic_usage and traffic_usage <= 1000:
                traffic_usage = f"{traffic_usage} B"
            elif 1000 < traffic_usage and traffic_usage <= 1000000:
                traffic_usage = f"{round(traffic_usage / 1000, 2)} KB"
            elif 1000000 < traffic_usage and traffic_usage <= **********:
                traffic_usage = f"{round(traffic_usage / 1000000, 2)} MB"
            elif ********** < traffic_usage:
                traffic_usage = f"{round(traffic_usage / **********, 2)} GB"
            return traffic_usage


        hosts = await TProxyHost.filter(enable=1).all().values("host","id","name")
        host_dict = {i.get("host"):i.get("id") for i in hosts}
        for record in records:
            record['is_deleted'] = bool(record.pop("is_deleted"))
            record['host_id'] = host_dict.get(record.get("url"),1)
            record["ip_addr"] = int2ip(record.pop("proxy__ip__ip_addr"))
            record["proxy_source"] = record.pop("proxy__source")
            record["expired_on"] = record.get("created_on", 0) + record.get("life_time", 0) * datetime.timedelta(
                days=1).total_seconds()
            expired_time = record['expired_on'] - int(time.time())-30
            record['life_time'] = (expired_time)//(3600*24) +1 if expired_time > 0 else 0
            record["port"] = Tools.config.service_conf.http_port if record.get(
                "network") == NetworkType.http else Tools.config.service_conf.socks5_port
            record['network'] = NetworkType.http.value if record['network'] == NetworkType.http.value else NetworkType.socks5.value
            record[
                "req_proxy_url"] = f"{record.get('username')}:{record.get('passwd')}@{record['url']}:{record['port']}"
            record[
                "ads_proxy_url"] = f"{record['url']}:{record['port']}:{record.get('username')}:{record.get('passwd')}"
            record['enable'] = bool(record['proxy__health'])
            record["status"] = func(record.get("proxy__health"), record.get("expired_on"), record.get("proxy__status"),record.get("created_on", 0),record.get("expired"))
            record["proxy_type"] = ("Static" if self.is_static else "Dynamic") + (
                "data center" if record.get("data_center") else "residential")
            record["proxy_type_zh"] = ("静态" if self.is_static else "动态") + (
                "数据中心" if record.get("data_center") else "住宅")
            record['proxy_type_en'] = ("Static" if self.is_static else "Dynamic") +" "+ (
                "data center" if record.get("data_center") else "residential")  
            record['proxy_type_jp'] = ("静的" if self.is_static else "動的") + ("データセンター" if record.get("data_center") else "住宅")
            record['proxy_type_ru'] = ("Статический" if self.is_static else "Динамический") +" " + ("Центральный" if record.get("data_center") else "Жилье")
            record['proxy_area'] = self.create_area(record['proxy__country__name'],record['proxy__country__code'],record['proxy__state__name'],record['proxy__city__name'],
  )

            # country_dict = await get_area(pipe,"country", "proxy__country_id")
            # state_dict = await get_area(pipe,"state", "proxy__state_id")
            # city_dict = await get_area(pipe,"city", "proxy__city_id")


            record['traffic_usage'] = traffic_usage_func(record.get("traffic_usage"))
            record['cost'] = str(round(record['cost'] / 1000, 2)) if record['cost'] > 0 else "0"
        # res = await pipe.execute()
        # while res:
        #     try:
        #         country =res.pop(0)
        #         key = self.keys.pop(0)
        #         if country:
        #             self.country_dict[key] = json.loads(country.decode())
        #         state = res.pop(0)
        #         key = self.keys.pop(0)
        #         if state:
        #             self.state_dict[key] = json.loads(state.decode())
        #         city = res.pop(0)
        #         key = self.keys.pop(0)
        #         if city:
        #             self.city_dict[key] = json.loads(city.decode())
        #     finally:
        #         ...
        return records


class DynamicTrafficTokens(Tokens):
    is_static = False
    la_id_is_null = False

    async def get(self):
        def traffic_usage_func(traffic_usage):
            """
            流量自适应转换单位
            """
            if 0 <= traffic_usage and traffic_usage <= 1000:
                traffic_usage = f"{traffic_usage} B"
            elif 1000 < traffic_usage and traffic_usage <= 1000000:
                traffic_usage = f"{round(traffic_usage / 1000, 2)} KB"
            elif 1000000 < traffic_usage and traffic_usage <= **********:
                traffic_usage = f"{round(traffic_usage / 1000000, 2)} MB"
            elif ********** < traffic_usage:
                traffic_usage = f"{round(traffic_usage / **********, 2)} GB"
            return traffic_usage

        records = await super().get()
        token_ids = [i.get("id") for i in records]
        res = await TProxyLine.filter(token_id__in=token_ids).values("token_id","s")
        traffic_pool = await TTrafficPoolLog.filter(token_id__in=token_ids).group_by("token_id").annotate(traffic=Sum("traffic")).values("token_id","traffic")
        Tools.log.debug("--------动态流量---------")
        Tools.log.debug(token_ids)
        for i in traffic_pool:
            Tools.log.debug(i)
        Tools.log.debug("--------动态流量---------")


        _ = {i.get("token_id"):i.get("s") for i in res}
        o = {i.get("token_id"):i.get("traffic") for i in traffic_pool}
        for record in records:
            record['s'] = _.get(record.get("id"),"")
            record['traffic_pool_cost'] = traffic_usage_func(o.get(record.get("id"),0))

            # record.pop("life_time")
            # record.pop("expired_on")
            if record['proxy__country_id'] and record['proxy__country_id'] < 0:
                if record['proxy__state_id'] == 0:
                    record['proxy_area'] = "Accurate to country"
                else:
                    record['proxy_area'] = "Accurate to city"
                record['generate_type'] = "by_common"
            else:
                record['generate_type'] = "by_area"
        return records


class DynamicIpTokens(Tokens):
    is_static = False
    la_id_is_null = True
    @staticmethod
    def func(health, expired_on, status, create_time,expired):
        """
        处理状态
        """
        if expired or expired_on < time.time() and create_time != expired_on:
            # 过期
            return ProxyStatus.expired
        if health == 0:
            return ProxyStatus.unhealthy
        if status == 0:
            # 失效
            return ProxyStatus.invalid
        return ProxyStatus.normal

    async def get(self):
        records = await super().get()
        token_ids = [i.get("id") for i in records]
        tags = await TTokenTag.filter(token_id__in =token_ids,is_deleted=0,type='auto_rotation')
        orders = await TIpOrders.filter(user_id=self.uid,token_id__in = token_ids,created_on__gt=**********, status__gte = 1,payway__in=["refresh_a_dynamic","buy_a_dynamic"])\
            .annotate(rotation_times=RawSQL("count(1)"),proxy_cost=RawSQL("sum(amount)")).group_by("token_id").values("rotation_times","proxy_cost","token_id")
        _ = {i.token_id:True for i in tags}
        o = {i.get("token_id"):(i.get("proxy_cost"),i.get("rotation_times")) for i in orders}

        for record in records:
            record['is_auto_rotation'] = _.get(record.get("id"),False)
            record['can_auto_rotation'] = record['proxy_area'] == "United States(US)"
            record.pop("life_time")
            record["status"] = self.func(record.get("proxy__health"), record.get("expired_on"), record.get("proxy__status"),
                                    record.get("created_on", 0),record.get("expired"))
            record['cost'] = str((o.get(record.get("id"),(0,0))[0]+150) /1000)
            record['rotation_times'] = o.get(record.get("id"),(0,0))[1]+1
            # record.pop("expired_on")
            # record.pop("expired")
            record.pop("traffic_usage")
            # record.pop("cost")
            record.pop("proxy_source")
            record.pop("proxy_type")
        return records


class StaticTrafficTokens(Tokens):
    is_static = True
    la_id_is_null = False

    async def get(self):
        records = await super().get()
        for record in records:
            if not  record.get("data_center"):
                if record.get("la_id") != 99:
                    record['s'] = "1"
                else:
                    record['s'] = "2"
            record.pop("life_time")
            record.pop("expired_on")
            record.pop("proxy_source")
            record.pop("expired")
        return records


class StaticIpTokens(Tokens):
    is_static = True
    la_id_is_null = True

    async def get(self):
        records = await super().get()
        ids = [record.get("id") for record in records]
        renews = await TStaticIpRenew.filter(token_id__in=ids, created_by=-1).order_by("-id")
        renews_dict = {renew.token_id: renew.is_auto_renew for renew in renews}
        for record in records:

            # is_auto_renew = await TStaticIpRenew.filter(token_id=record.get("id")).order_by("-id").first().values("is_auto_renew")
            auto_renew = renews_dict.get(record.get("id"), False)
            record['is_auto_renew'] = auto_renew
            record.pop("traffic_usage")
            record.pop("cost")
            record.pop("proxy_source")
        return records


@cache(CacheSaveWhere.redis)
async def get_payway_list(payway_name: str):
    """
    获取支付类型
    """
    name = PayWayParams(payway_name).name
    records = await TPayway.filter(payway=name, deleted_on=0).all().order_by("days", "pay_value")
    # records = await Tools.mysql.fetch_all(table.select().where(table.c.payway == name))
    res = [{"id": record.id, "name": record.note, "en_name": record.en_note, "days": record.days,"vm_level_id": record.vm_level_id,
             "pay_value": record.pay_value / 1000} for record in records]

    if name ==PayWayParams.create_vm.name:
        conf = await TVmLevel.all()
        confs = []
        for c in conf:
            confs.append({"id": c.id, "name": f"{c.name}/{c.cpu}/{c.ram}/{c.storage} 存储", "en_name": f"{c.en_name}/{c.cpu}/{c.ram}/{c.storage} storage"})
        res = {"days":sorted(set([i.get('days') for i in res])),"pay_value":{f"{r.get('vm_level_id')}:{r.get('days')}":r.get('pay_value') for r in res},"vm_conf":confs}
    return res

async def get_paway_id(days:int,vm_conf_id:int):
    """
    获取支付类型
    """
    record = await TPayway.filter(days=days, vm_level_id=vm_conf_id,payway='create_vm', deleted_on=0).first()
    return record.id


async def static_ip_verify(is_data_center=0, payway_id=0, protocol="http", **kwargs):
    """ 校验参数 """
    payway_list = await get_payway_list(PayWayParams.buy_residential_private_static_ip.value)
    if payway_id not in [payway["id"] for payway in payway_list]:
        raise_execption("payway_id is not valid")




# 记录代理线路
async def save_dynamic_proxy_line(token,s):
    """
    记录代理线路
    """
    await TProxyLine.create(token_id=token,s=s)




async def rec_celery_res(task_name, data, **kwargs):
    """
    接受celery的返回值

    """
    func = kwargs.get("func")
    if func:
        is_data_center = data.get("is_data_center")
        payway_id = data.get("payway_id")
        protocol = data.get("protocol")
        await func(is_data_center=is_data_center, payway_id=payway_id, protocol=protocol)
    celery = kwargs.get("celery",Tools.celery)
    res = await celery.send_task(task_name, kwargs=data)
    if res.get("code") == 0:
        data = suc_data(data=res)
    else:
        code = res.get("code",-1)
        if code in (500, 4002):
            code = 4004
        data = fail_data(msg=res.get("msg",""), code=code)
    return data


async def get_charges_records(uid: int, page: int = 1, page_size: int = 10,is_gpt=0):
    payways = await TPayway.filter(deleted_on=0).all().values("payway", "note", "en_note","jp_note","ru_note")
    payway = {payway["payway"]: payway for payway in payways}
    query = TIpOrders.filter(deleted_on=0, user_id=uid, status__gt=0, valid=1, type="+").all()
    if is_gpt:
        query = query.filter(text='from gpt302')
        f = 'ai'
    else:
        query = query.filter(text__not='from gpt302')
        f = 'proxy302'
    count = await query.count()
    records = await query.order_by("-created_on").offset((page - 1) * page_size).limit(page_size).limit(
        page_size).values("created_on", "orderid", "value", "extra_value", "amount", "type", "payway", "currency_type",'token_id',
                          "currency", "pay_order")
    for i in records:
        i['value'] = to_ptc(i['value'])
        i['extra_value'] = to_ptc(i['extra_value'])
        i['amount'] = to_ptc(i['amount'])
        i["note"] = payway.get(i["payway"],{}).get("note","") + (f"(ID: {i['token_id']})" if i['token_id'] else '')
        i["orderid"] = str(i['orderid'])
        i["zh_note"] = payway.get(i["payway"],{}).get("note","")+ (f"(ID: {i['token_id']})" if i['token_id'] else '')
        i["en_note"] = payway.get(i["payway"],{}).get("en_note","")+ (f"(ID: {i['token_id']})" if i['token_id'] else '')
        i["jp_note"] = payway.get(i["payway"],{}).get("jp_note","")+ (f"(ID: {i['token_id']})" if i['token_id'] else '')
        i["ru_note"] = payway.get(i["payway"],{}).get("ru_note","")+ (f"(ID: {i['token_id']})" if i['token_id'] else '')
        order_id = base64.b64encode(i["orderid"].encode("utf-8")).decode()
        i["en_bill_download"] = f"https://{Tools.config.service_host}/static/bill/en/{order_id}?f={f}"
        i["bill_download"] = f"https://{Tools.config.service_host}/static/bill/zh/{order_id}?f={f}"
        # i["pay_order"] = i["pay_order"] + f"({i['token_id']})" if i['token_id'] else i["pay_order"]
        if i['payway'].startswith("charge_") or i['payway'].startswith("auto_"):
            i['pay_order'] = i['pay_order']
        else:
            i['pay_order'] = ''
    return {"total": count, "records": records,"page":page,"page_size":page_size}


@cache(CacheSaveWhere.redis)
async def get_datacenter_area_filter():

    conn = Tortoise.get_connection("default")
    sql = f"select distinct country_id,state_id,city_id from t_traffic_ips left join t_luminati_accounts lt on lt.id= la_id  where is_data_center=1"
    res = await conn.execute_query_dict(sql)
    country_list = set()
    state_list = set()
    city_list = set()
    for i in  res:
        country_list.add(i.get("country_id"))
        state_list.add(i.get("state_id"))
        city_list.add(i.get("city_id"))
    return country_list,state_list,city_list

@cache(CacheSaveWhere.redis)
async def get_residential_area_filter():
    conn = Tortoise.get_connection("default")
    sql = f"select distinct country_id,state_id,city_id from t_traffic_ips left join t_luminati_accounts lt on lt.id= la_id  where is_data_center=0"
    res = await conn.execute_query_dict(sql)
    country_list = set()
    state_list = set()
    city_list = set()
    for i in  res:
        country_list.add(i.get("country_id"))
        state_list.add(i.get("state_id"))
        city_list.add(i.get("city_id"))
    return country_list,state_list,city_list



@cache(CacheSaveWhere.redis)
async def get_countries(name, use_in,**kwargs):
    """
    获取国家
    """
    query_set = TGeoCountry.filter(deleted_on=0).all()
    if use_in == UseIn.vm:
        query_set = query_set.filter(id__in=Subquery(TVmLocations.all().values("country_id")) )
    elif use_in == UseIn.static_residential_ip:
        query_set = query_set.filter(id__in=Subquery(TCountryFilter.filter(type=UseIn.static_residential_ip.value).values("country_id")))
    elif use_in == UseIn.static_datacenter_ip:
        query_set = query_set.filter(id__in=Subquery(TCountryFilter.filter(type=UseIn.static_datacenter_ip.value).values("country_id")))
    elif use_in == UseIn.static_residential_traffic:
        country_list,state_list,city_list = await get_residential_area_filter()
        query_set = query_set.filter(id__in=country_list)
    elif use_in == UseIn.static_datacenter_traffic:
        country_list,state_list,city_list = await get_datacenter_area_filter()
        query_set = query_set.filter(id__in=country_list)
    


    page = kwargs.get("page", 1)
    page_size = kwargs.get("page_size", 10)

    total = await query_set.count()
    if name:
        query_set = query_set.filter(Q(name__icontains=name) | Q(code=name.upper())).all()

    records = await query_set.order_by("id").offset(page_size * (page - 1)).limit(page_size).values("id", "code", "name")
    for record in records:
        record["name"] = f"{record['name']} ({record['code']})"
    return {"total": total, "records": records}



@cache(CacheSaveWhere.redis)
async def get_states(country_id, state_name, **kwargs):
    """
    获取洲
    """
    query_set = TGeoState.filter(country_id=country_id)
    use_in = kwargs.get("use_in")
    if use_in == UseIn.static_residential_traffic:
        country_list,state_list,city_list = await get_residential_area_filter()
        query_set = query_set.filter(id__in=state_list)
    elif use_in == UseIn.static_datacenter_traffic:
        country_list,state_list,city_list = await get_datacenter_area_filter()
        query_set = query_set.filter(id__in=state_list)
    page = kwargs.get("page", 1)
    page_size = kwargs.get("page_size", 10)
    
    total = await query_set.count()
    if state_name:
        query_set = query_set.filter(Q(name__icontains=state_name)|Q(code__icontains=state_name))
    records = await query_set.offset(page_size * (page - 1)).limit(page_size).values("id", "code", "name")
    for record in records:
        record["name"] = f"{record['name']} ({record['code']})"
    return {"total": total, "records": records}

@cache(CacheSaveWhere.redis)
async def get_cities(state_id, city_name, **kwargs):
    """
    获取城市
    """

    query_set = TGeoCity.filter(state_id=state_id)
    use_in = kwargs.get("use_in")
    if use_in == UseIn.static_residential_traffic:
        country_list,state_list,city_list = await get_residential_area_filter()
        query_set = query_set.filter(id__in=city_list)
    elif use_in == UseIn.static_datacenter_traffic:
        country_list,state_list,city_list = await get_datacenter_area_filter()
        query_set = query_set.filter(id__in=city_list)

    page = kwargs.get("page", 1)
    page_size = kwargs.get("page_size", 10)
    
    total = await query_set.count()
    if city_name:
        query_set = query_set.filter(name__icontains=city_name)
    records = await query_set.offset(page_size * (page - 1)).limit(page_size).values("id", "name")
    return {"total": total, "records": records}

# @cache(CacheSaveWhere.redis)
async def get_charts_list(user_id:int,is_gpt=False,payway_id=0) -> list:
    """
    获取代理列表
    """

    @cache(CacheSaveWhere.redis,ttl=60*10)
    async def get_records():
        contains = "charge_"
        others = {}
        if payway_id:
            others = {"id": payway_id}
        records = await TPayway.filter(payway__contains=contains,en_note__contains="top up", deleted_on=0, id__lte=35,**others).all().order_by(
            "price").values("id", "payway", "note", "en_note", "price", "service_fee", 'extra_value')
        return records

    records = await get_records()
    if user_id not in (18388,2):
        records = [i for i in records if i.get("id")>0]
        times = await PaymentApiHandler().has_change(user_id)
        # 掉新用户专属5刀充值
        if not payway_id and times and not is_gpt:
            records.pop(0)
    for i in records:
        i["tag"] = ""
        i["en_tag"] = ""
        i['extra_value'] = i['extra_value'] / 1000 if i['extra_value'] else 0
        i['note'] = " ".join(i['note'].split(" ")[-2:])
        i['en_note'] = " ".join(i['en_note'].split(" ")[-2:])
        if i['price'] == 50:
            i['en_tag'] = 'Recommend'
            i['tag'] = '热卖'
        if i['price'] == 200:
            i['en_tag'] = 'Most Popular'
            i['tag'] = "特别推荐"
    return records


@cache(60*10)
async def get_statistics(uid: int, type: StatisticsType, proxy_type: ProxyType) -> dict:
    payway = (
        'buy_1g_traffic', 'buy_1g_dynamic_traffic', 'buy_1g_static_traffic', 'buy_1g_static_data_center_traffic',
        "buy_1g_luna_dynamic_traffic","buy_1g_oracle_static_data_center_traffic"
    )
    days ,hour, now = 30,24, datetime.datetime.now() - relativedelta()
    if proxy_type == ProxyType.cost:
        value_name = "cost"
    elif proxy_type == ProxyType.traffic:
        value_name = "count"
    else:
        value_name = ""

    async def get_24h_summary() -> dict:
        """
        获取过去24小时创建的token的ip
        """

        # hour, now = 24, datetime.datetime.now(pytz.timezone('Etc/GMT-8')) - relativedelta()
        before_24h_dict = {(now + relativedelta(hours=-i)).strftime('%Y-%m-%d %H'): 0 for i in range(hour + 1)}
        # 时间戳转日期

        group = await TUserToken.filter(user_id=uid, deleted_on=0, la_id=0, created_on__gt=(now - relativedelta(hours=24)) .timestamp()).annotate(
            count=Count("id"), tmp_date=RawSQL(" DATE_FORMAT( CONVERT_TZ(FROM_UNIXTIME(created_on),'-08:00','+00:00') ,  '%Y-%m-%d %H')")).group_by(
            "tmp_date").values("tmp_date", "count")
        Tools.log.debug(group)
        Tools.log.debug(before_24h_dict)
        for g in group:
            before_24h_dict[g['tmp_date']] = g['count']
        Tools.log.debug(before_24h_dict)

        return before_24h_dict

    async def get_30d_summary() -> dict:
        """
        获取过去24小时创建的token的ip
        """
        before_30_dict = {(now + relativedelta(days=-i)).strftime('%Y-%m-%d'): 0 for i in range(days + 1)}
        # 时间戳转日期
        group = await TUserToken.filter(user_id=uid, deleted_on=0, la_id=0, created_on__gt=(now -relativedelta(days=30)).timestamp()).annotate(
            count=Count("id"), tmp_date=RawSQL(" DATE_FORMAT( CONVERT_TZ(FROM_UNIXTIME(created_on),'-08:00','+00:00') ,  '%Y-%m-%d')")).group_by(
            "tmp_date").values("tmp_date", "count")

        Tools.log.debug(group)
        for g in group:
            before_30_dict[g['tmp_date']] = g['count']
        return before_30_dict

    async def get_24h_traffic_summary() -> dict:
        """
        获取过去24小时创建的token的流量
        """
        "WHERE uid = %s AND deleted_on = 0 AND payway IN ('buy_1g_traffic',  'buy_1g_dynamic_traffic', 'buy_1g_static_traffic', 'buy_1g_static_data_center_traffic')"

        # hour, now = 24, datetime.datetime.now(pytz.timezone('Etc/GMT-8')) - relativedelta()
        before_24h_dict = {(now + relativedelta(hours=-i)).strftime('%Y-%m-%d %H'): 0 for i in range(hour + 1)}
        group = await TIpOrders.filter(user_id=uid, deleted_on=0, created_on__gt=(now - relativedelta(hours=24)).timestamp(),
                                       payway__in=payway).annotate(count=Sum("traffic_usage"),cost=Sum(F("value")),
                                                                   tmp_date=RawSQL(
                                                                       " DATE_FORMAT( CONVERT_TZ(FROM_UNIXTIME(created_on),'-08:00','+00:00') ,  '%Y-%m-%d %H')"))\
            .group_by("tmp_date").values("tmp_date", "count","cost")

        Tools.log.debug(group)

        for g in group:
            value = g[value_name]
            if value_name == "cost":
                value /= 1000
            before_24h_dict[g['tmp_date']] = value
        return before_24h_dict

    async def get_30d_traffic_summary() -> dict:

        before_30_dict = {(now + relativedelta(days=-i)).strftime('%Y-%m-%d'): 0 for i in range(days + 1)}
        # 时间戳转日期
        group = await TIpOrders.filter(user_id=uid, deleted_on=0, created_on__gt=(now -relativedelta(days=30)) .timestamp(),
                                       payway__in=payway).annotate(count=Sum("traffic_usage"),cost=Sum(F("value")),
                                                                   tmp_date=RawSQL("DATE_FORMAT( CONVERT_TZ(FROM_UNIXTIME(created_on),'-08:00','+00:00') ,  '%Y-%m-%d')"))\
            .group_by("tmp_date").values("tmp_date", "count","cost")
        Tools.log.debug(group)
        for g in group:
            value = g[value_name]
            if value_name == "cost":
                value /= 1000
            before_30_dict[g['tmp_date']] = value
        return before_30_dict

    if proxy_type == ProxyType.ip:
        if type == StatisticsType.by_hour:
            res = await get_24h_summary()
        else:
            res = await get_30d_summary()
    else:
        if type == StatisticsType.by_hour:
            res = await get_24h_traffic_summary()
        else:
            res = await get_30d_traffic_summary()

    data = {
        "keys": list(map(lambda x: x.split(" ")[-1], res.keys()))[::-1],
        "values": list(res.values())[::-1]
    }
    return data


@cache(60*10)
async def get_cost(uid: int, type: StatisticsType,start=0,end=0):
    end = time.time()
    if type == StatisticsType.by_day:
        start = time.mktime((datetime.datetime.now() - relativedelta(hours=24)).timetuple())
    else:
        start = time.mktime((datetime.datetime.now() - relativedelta(days=30)).timetuple())

    def get_query(format="%Y-%m-%d %H"):
        query = TIpOrders.filter(user_id=uid, deleted_on=0, created_on__gt=start,created_on__lt=end,
                                 value__gt=0, type='-',payway__not="gpt_cost").annotate(
            payways=Case(
                When(payway__in=['buy_1g_dynamic_traffic', 'buy_1g_luna_dynamic_traffic','buy_1g_oxylabs_dynamic_traffic','buy_1g_luminati_dynamic_traffic','buy_1g_smart_dynamic_traffic','buy_1g_netnut_dynamic_traffic'],
                     then="buy_1g_dynamic_traffic"),
                When(payway__in=['buy_1g_oracle_static_data_center_traffic', 'buy_1g_static_data_center_traffic'],
                     then="buy_1g_static_data_center_traffic"),
                When(payway__in=['buy_1g_static_traffic_from_iproyal', 'buy_1g_static_traffic'],
                     then="buy_1g_static_traffic"),
                default=F("payway")),
            cost=Sum(F("value")),
            tmp_date=RawSQL(
                f" DATE_FORMAT( CONVERT_TZ(FROM_UNIXTIME(created_on),'-08:00','+00:00') ,  '{format}')")) \
            .group_by("tmp_date","payways").values("tmp_date", "payways", "cost")
        return query

    async def get_days_traffic_summary(format="%Y-%m-%d",is_days=True) -> dict:
        """
        获取过去n天创建的消耗
        """
        start_time = datetime.datetime.fromtimestamp(start +3600*8)
        end_time = datetime.datetime.fromtimestamp(end +3600*8)

        # Tools.log.debug(start_time, end_time)
        _tmp={}
        while start_time < end_time:
            _tmp[start_time.strftime(format)] = PayWays().model_dump()
            if is_days:
                start_time += datetime.timedelta(hours=1)
            else:
                start_time += relativedelta(days=1)
        # before_24h_dict = {(now + relativedelta(hours=-i)).strftime('%Y-%m-%d'): 0 for i in range(hour + 1)}
        group = await get_query(format=format)

        Tools.log.debug(group)
        for i in group:
            _tmp[i.get("tmp_date")][i.get("payways")] = i.get("cost")/1000
            _tmp[i.get("tmp_date")]['total'] += i.get("cost")/1000
        return _tmp

    _ = {
        StatisticsType.by_day:("%Y-%m-%d %H",True),
        StatisticsType.by_month:("%Y-%m-%d",False)
    }

    res = await get_days_traffic_summary(is_days=_[type][1],format=_[type][0])
    keys = res.keys()
    values = res.values()
    tmp = {}
    for key in PayWays.model_fields:
        tmp[key] = []
        for value in values:
            tmp[key].append(round(value.get(key, 0),3))
    data = {
        "keys": list(keys) if type == StatisticsType.by_month else list([i[-2:] for i in keys]),
        "values": tmp,
        "summary": {k:round(sum(v),3) for k,v in tmp.items()}

    }
    return data


async def change_remark(uid, token_id, remark):
    await TUserToken.filter(id=token_id, user_id=uid).update(remark=remark)


async def on_off_token(uid, token_id):
    record = await TUserToken.filter(id=token_id, user_id=uid). \
        select_related(
        "proxy"
    ).first().values("proxy__id", "proxy__health")
    if record:
        p_id = record.get("proxy__id")
        proxy__online = record.get("proxy__health", 0)
        proxy__online = 0 if proxy__online else 1
        await TProxyIp.filter(id=p_id).update(health=proxy__online)

async def on_off_tokens(uid,switch="on",token_ids=[]):
    tokens = await TUserToken.filter(user_id=uid,deleted_on=0,id__in=token_ids).values("proxy_id")
    query = [i.get("proxy_id") for i in tokens]
    if switch == "on":
        await TProxyIp.filter(id__in=query).update(health=1)
    else:
        await TProxyIp.filter(id__in=query).update(health=0)


async def del_token(uid, token_id):
    await TUserToken.filter(id=token_id, user_id=uid).update(deleted_on=time.time(),is_deleted=True)
    # await TUserTrafficIp.filter(pi_id__in=Subquery(TUserToken.filter(id=token_id, user_id=uid).values("pi_id")))\
    #     .update(deleted_on=current_timestamp())

# 批量删除
async def del_token_batch(uid:int,token_ids:list):
    await TUserToken.filter(id__in=token_ids, user_id=uid).update(deleted_on=time.time(),is_deleted=True)
    # await TUserTrafficIp.filter(pi_id__in=Subquery(TUserToken.filter(id__in=token_ids, user_id=uid).values("pi_id")))\
    #     .update(deleted_on=current_timestamp())



async def change_auto_renew_status(token_id: int, is_auto_renew: bool = True, lang: str = "zh", payway_id=0, uid=0):
    record = await TStaticIpRenew.filter(token_id=token_id, created_by=-1, uid=uid).order_by("-id").first()

    is_auto_renew = bool(is_auto_renew)

    if record:
        if not is_auto_renew:
            is_auto_renew = record.is_auto_renew
        payway_id = record.pay_way_id
        return await TStaticIpRenew.filter(token_id=token_id, created_by=-1, uid=uid).update(
            **{"is_auto_renew": not is_auto_renew, "pay_way_id": payway_id})
    else:
        return await TStaticIpRenew.create(token_id=token_id, is_auto_renew=not is_auto_renew, created_by=-1,
                                           created_on=int(time.time()), pay_way_id=payway_id, uid=uid, order_id=0,
                                           t_ip_order_id=0)


async def create_key(uid, key_name):
    count = await TApiToken.filter(user_id=uid,deleted_on=0).count()
    if count >= 10:
        res = fail_data(msg="key limit 10", code=StateCode.ApiKeyLimit.value)
    else:
        await TApiToken.create(user_id=uid, name=key_name,password=random_string(18))
        res = suc_data()
    return res

async def del_key(uid, key_id):
    await TApiToken.filter(id=key_id, user_id=uid).update(deleted_on=time.time())
    return suc_data()

async def get_user_keys(uid):
    query = TApiToken.filter(user_id=uid,deleted_on=0)
    count = await query.count()
    records =  await query.values("id", "name", "password","created_on")
    return suc_data(data={"count":count,"records":records})



async def download_times_add_1(record_id):
    conn = Tortoise.get_connection("default")
    await conn.execute_query("UPDATE t_accessory SET download_times=download_times+1 WHERE id=%s", record_id)
    # await TAccessories.filter(download__contains=file_name).update(download_times=RawSQL("download_times+1"))


async def get_extension(token_id:int):
    def get_type(is_static,is_datacenter):
        return ("Static" if is_static else "Dynamic") + (
            "DataCenter" if is_datacenter else "Residential")

    token_data = await TUserToken.filter(id=token_id).\
        select_related("proxy").\
        select_related("proxy__ip").\
        select_related("proxy__country").\
        select_related("proxy__state").\
        select_related("proxy__city"). \
        first().\
        values(*"""id,is_api,url,username,passwd,proxy__country__id,data_center,is_static,proxy__country__code,proxy__country__name,proxy__state__id,proxy__state__code,proxy__state__name,proxy__city__id,proxy__city__name,proxy__health,proxy__status,proxy__source,network,proxy__ip__ip_address,proxy__ip__ip_addr,proxy__ip_id,remark,la_id""".split(","))
    
    name = ""
    name_en = ""
    if token_data.get("is_static"):
        name += "静态"
        name_en += "Static Proxy by "
    else:
        name += "动态"
        name_en += "Rotating Proxy by "
    if token_data.get("la_id"):
        name += "流量"
        name_en += "Traffic"
    else:
        name += "IP"
        name_en += "IP"
    
    name += "代理"

    res_data = {
        'ip_address': int2ip(token_data.get('proxy__ip__ip_addr')),
        'ip': int2ip(token_data.get('proxy__ip__ip_addr')),
        'network': token_data.get('network',"").replace('socket','socks5'),
        'token_id': token_data['id'], 
        'proxy_type': name,
        'proxy_en_type': name_en,
        'username': token_data['username'], 'password': token_data['passwd'],
        'host': token_data.get('url', '') , 'port': 2222 if token_data.get('network',"") == 'http' else 3333,
        'country': {'id': token_data['proxy__country__id'], 'code': token_data['proxy__country__code'], 'name': token_data['proxy__country__name']},
        'state': {'id': token_data['proxy__state__id'], 'code': token_data['proxy__state__code'], 'name': token_data['proxy__state__name']},
        'city': {'id': token_data['proxy__city__id'], 'name': token_data['proxy__city__name']},
        'is_api':(2 if token_data.get("proxy__city__id")==-2 else 1) if token_data.get("is_api") else 0,
        'remark': token_data['remark'],
        'plugin_remark' : token_data['remark'] if token_data['remark'] else get_type(token_data['is_static'],token_data['data_center']) + f"-{token_data['proxy__country__code']}"

    }
    if res_data['is_api']:
        res_data['proxy_en_type'] += " (General Proxy)"
        res_data['proxy_type'] += " (通用代理)"
    Tools.log.debug(res_data)
    return res_data


async def cache_area():
    async def func(records,key="country"):
        dict_list = {}
        for c in records:
            dict_list[c.get("id")] = json.dumps(c)
        await Tools.redis.hset(key, mapping=dict_list)

    country = await TGeoCountry.all().values("id","name","code")
    state = await TGeoState.all().values("id","name","code")
    city = await TGeoCity.all().values("id","name")
    await func(country)
    await func(state,'state')
    await func(city,'city')


# @cache(where=CacheSaveWhere.redis,ttl=3600)
async def get_announcements(is_gpt=False):
    conn = Tortoise.get_connection("default")
    t = int(time.time())
    platform = 'proxy302'
    if is_gpt:
        platform = '302AI'
    res = await conn.execute_query("SELECT id, modified_on, title, en_title,ru_title,jp_title, content, en_content,ru_content,jp_content, expired_on "
                                   "FROM t_announcements WHERE (expired_on = 0 OR expired_on >= %s) AND modified_on <= %s "
                                   "AND deleted_on = 0 AND status = 1 and platform=%s order by id desc limit 1", [t,t,platform])
    if res[0]>=1:
        return res[1][0]
    else:
        return {}


async def build_gift_url(uid,lang,is_gpt=False):
    product_id = Tools.config.partner_share_gpt.product_id if is_gpt else Tools.config.partner_share.product_id
    user_integration_key = Tools.config.partner_share_gpt.user_integration_key if is_gpt else Tools.config.partner_share.user_integration_key
    url = constants.PartnerShareConfig.login_base_url.format(product_id=product_id)
    user = await TUser.filter(uid=uid).first()
    # if res := await test_user_action(user,limit=True):
    #     return res

    if not user:
        return None
    email = user.email.replace("adswave.", "proxy302.") if user.email else user.phone[:5]+'*'*8
    data = {
        'user_id': user.uid,
        'name': user.name,
        'timestamp': str(int(time.time())),
        'lang': lang,
        'notification_email': email,
    }
    secret_str = "{uid}{product_id}{secret_key}{ts}".format(
        uid=data['user_id'], product_id=product_id,
        secret_key=user_integration_key, ts=data['timestamp'])
    data['secret'] = hashlib.sha256(secret_str.encode('utf-8')).hexdigest()
    params = '&'.join([f"{k}={v}" for k, v in data.items()])
    url = f"{url}?{params}"
    return url


async def get_info_from_request(request:Request):
    """
    :param request:
    :return:
    """
    from controllers.user import get_client_ip

    ip = await get_client_ip(request)
    agent = request.headers.get("user-agent")
    return {"ip":ip, "agent": agent}


async def cache_or_get_email_info(code,code_md5:str=None,request:Request=None):
    cache_key = f"Captcha_Cache_{code}"
    info = await Tools.redis.get(cache_key)
    if info:
        return pickle.loads(info)
    if not code_md5 or not request:
        return
    info = await get_info_from_request(request)
    info['code_md5'] = code_md5
    await Tools.redis.setex(cache_key,60*5,pickle.dumps(info))


async def _post(session, host, port,username="",pw=""):
    try:
        if username and pw:
            username_pw = f"{username}:{pw}@"
        else:
            username_pw = ""
        resp = await session.get("https://www.google.com/", proxy=f"http://{username_pw}{host}:{port}", timeout=5)
        Tools.log.debug(f"http://{username_pw}{host}:{port} {resp.status}")
        if resp.status == 200:
            return True
        Tools.log.debug(await resp.text())
    except:
        return False

async def check_host_port():
    sem = asyncio.Semaphore(50)




    suc_num = 0
    expired_num = 0
    async def check_proxy_health(session,ip_pool_record:TDynamicIpPool):
        nonlocal suc_num
        nonlocal expired_num
        async with sem:
            res = await _post(session,ip_pool_record.host,ip_pool_record.port)
            now_time = time.time()
            if res:
                ip_pool_record.modified_on = now_time
                ip_pool_record.checked_on = now_time
                ip_pool_record.status = 1
                suc_num += 1
                if now_time - ip_pool_record.created_on >= 60*60*12:
                    ip_pool_record.status = -1
                    ip_pool_record.deleted_on = now_time
            else:
                if ip_pool_record.pi_id:
                    await TProxyIp.filter(id=ip_pool_record.pi_id).update(health=0, online=0, status=0, deleted_on=now_time,
                                                                          modified_on=now_time)
                    tokens = await TUserToken.filter(proxy_id=ip_pool_record.pi_id).all().values("id","user_id","created_on")
                    for token in tokens:
                        Tools.log.debug(f"tokenId: {token.get('id') } time ：{now_time - token.get('created_on')}" )
                        if now_time - token.get("created_on") <= 60*3:
                            await add_ready_ip_order(update_user_info=True,token_id=token.get("id"),
                                                     user_id=token.get("user_id"), type="+", currency=0, currency_type="USD",
                                                     receive_currency=0, payway="refund_a_dynamic", pay_order="", is_inner=True,
                                                     value=0, extra_value=150,
                                                     status=1, checksum=True, valid=True)

                ip_pool_record.deleted_on = now_time
                ip_pool_record.modified_on = now_time
                ip_pool_record.checked_on = now_time
                ip_pool_record.status = -1
                expired_num += 1
            await ip_pool_record.save()

    # 逻辑开始
    session = aiohttp.ClientSession(trust_env=True)
    now =  int(time.time())-60*5
    all_records =await TDynamicIpPool.filter(deleted_on=0,created_on__lte=now,status__gte=0 ).all()
    jobs = [check_proxy_health(session,record) for record in all_records]
    await asyncio.gather(*jobs)
    await session.close()
    return {"suc_num":suc_num,"expired_num":expired_num}



async def create_dynamic_pool_ip(host:str,code:str="US",host_port=8080):
    session = aiohttp.ClientSession()



    async def band_ip2port(code,port,country_code='',state_name=''):
        await asyncio.sleep(0.1)
        data = {
            "freePort": 1,
            "geo": code,
            "port": port,
            "taskId": 0
        }
        if country_code:
            data['geo']=country_code
        if state_name:
            data["state"]=state_name
        resp = await session.post(f"http://{host}:{host_port}/v1/proxy", data=data)
        try:
            json_resp = await resp.json()  # type:dict
        except:
            return
        res = json.loads(json_resp.get("data").get("result"))
        data = res.get("data")[0]
        ip = data.get("out_ip")
        country_code = data.get("country")
        country_id, state_id, city_id = 0, 0, 0
        country = await TGeoCountry.filter(code=country_code).first()
        if country:
            country_id = country.id
            state = data.get("state")
            if state == 'newyork':
                state = 'New York'

            state = await TGeoState.filter(name=state, country_id=country.id).first()
            if state:
                state_id = state.id
                city = data.get("city")
                city = await TGeoCity.filter(name=city, state_id=state.id).first()
                city_id = city.id if city else 0
        today_str = datetime.date.today().strftime("%Y%m%d")
        await Tools.redis.incr(f"Dynamic_Pool_Statistics_{ip}_{today_str}")
        ip_obj = await TIps.update_or_create(ip_addr=ip2int(ip),ip_address=ip2hex(ip),ip=ip)

        if await TDynamicIpPool.filter(ip_id=ip_obj[0].id,status__gte=0 ).exists():
            state = await TGeoState.filter(country_id=country_id).annotate(ra = RawSQL("rand()")).order_by("ra").first().values("name")
            name = state.get("name").replace(" ",'').lower()
            return await band_ip2port(code,port,state_name=name)

        return await TDynamicIpPool.create(host=host, port=port, country_id=country_id, created_on=time.time(),pi_id=0
                                    , state_id=state_id, city_id=city_id, ip=ip,pm_id=pm.id,ip_id=ip_obj[0].id)



    ip_addr = ip2int(host)
    pm = await TProxyMachines.get(ip_id=Subquery(TIps.filter(ip_addr=ip_addr).first().values("id")))
    update_num = 0
    ports = [i.get("port") for i in await TDynamicIpPool.filter(host=host,status__gte=0).values("port")]
    for port in range(5000,5251) :
        if port in ports:
            continue
        data = await band_ip2port(code,port)
        if data:
            update_num+=1
        # break

    await session.close()
    return {"updated":update_num}




async def create_quick_access(uid,data:QuickAccessIn):

    record = await TUserToken.filter(id=data.token_id).first()
    if not record:
        return fail_data()
    port = Tools.config.service_conf.http_port if record.network == NetworkType.http else Tools.config.service_conf.socks5_port
    ip_record = await TIps.filter(id = Subquery(TProxyIp.filter(id=record.proxy_id).values("ip_id"))).first()
    if not ip_record:
        ip = ""
    else:
        ip = int2ip(ip_record.ip_addr)
    ads_url = f"{record.url}:{port}:{record.username}:{record.passwd}"
    update_files = {"token_id": data.token_id, "url": data.url, "ads_url": ads_url, "ip": ip, "remark": data.remark,
                    "group": data.group, 'proxy_area': data.proxy_area, 'username': record.username,"uid":uid,
                    'proxy_created_on': record.created_on}
    await TProxyUrlGroup.create(**update_files)
                                # ,is_traffic=1 if record.la_id==0 else 0,is_static=record.is_static)
    return suc_data()


async def update_quick_access(id,uid,data:QuickAccessIn):
    update_files = {}
    if data.url:
        update_files["url"] = data.url
    if data.token_id:
        record = await TUserToken.filter(id=data.token_id).first()
        if not record:
            return fail_data()
        port = Tools.config.service_conf.http_port if record.network == NetworkType.http else Tools.config.service_conf.socks5_port
        ip_record = await TIps.filter(id = Subquery(TProxyIp.filter(id=record.proxy_id).values("ip_id"))).first()
        if not ip_record:
            ip = ""
        else:
            ip = int2ip(ip_record.ip_addr)
        ads_url = f"{record.url}:{port}:{record.username}:{record.passwd}"
        update_files["token_id"] = data.token_id
        update_files["ads_url"] = ads_url
        update_files["ip"] = ip

        update_files['proxy_area'] = data.proxy_area
        update_files['username'] = record.username
        update_files['proxy_created_on'] = record.created_on
    if data.remark:
        update_files["remark"] = data.remark
    if data.group:
        update_files["group"] = data.group
    await TProxyUrlGroup.filter(id=id,uid=uid).update(**update_files)
                                # ,is_traffic=1 if record.la_id==0 else 0,is_static=record.is_static)
    return suc_data()



async def get_quick_access(uid,page,page_size):
    def tmp(x,y):
        _ = dict(x)
        _.update(y)
        _['proxy_type'] = ("Static" if x.token.is_static else "Dynamic") + " "+(
            "data center" if x.token.data_center else "Residential")
        _["proxy_type_zh"] = ("静态" if x.token.is_static else "动态") + ("数据中心" if x.token.data_center else "住宅")
        _['proxy_type_en'] = ("Static" if x.token.is_static else "Dynamic") +" "+ ("data center" if x.token.data_center else "residential")
        _['proxy_type_jp'] = ("静的" if x.token.is_static else "動的") + ("データセンター" if x.token.data_center else "住宅")
        _['proxy_type_ru'] = ("Статический" if x.token.is_static else "Динамический") +" "+ ("Центральный" if x.token.data_center else "Жилье") 
        return _
    total = await TProxyUrlGroup.filter(uid=uid,deleted_on=0).count()
    records = await TProxyUrlGroup.filter(uid=uid,deleted_on=0).select_related("token").order_by("-id").offset((page-1)*page_size).limit(page_size)

    data = {
        "total":total,
        "records":[tmp(i,{"proxy_remark":i.token.remark,'network':"http" if i.token.network == 'http' else 'socks5'}) for i in records],
    }
    return suc_data(data=data)

async def delete_quick_access(uid,id):
    await TProxyUrlGroup.filter(uid=uid,id=id).update(deleted_on=int(time.time()))
    return suc_data()



# 替换go转发服务用的缓存token
async def del_token_cache(token:TUserToken):
    "key name CACHE_USER_TOKEN_R3252FwB_rTENnBcJKOuUYQ8d"
    Tools.log.debug(f"CACHE_USER_TOKEN_{token.username}_{token.passwd}")
    await Tools.redis.delete(f"CACHE_USER_TOKEN_{token.username}_{token.passwd}")
    # _ = json.loads(token_str.decode("utf-8"))


async def search_all_auto_rotation_tokens():
    records = await TTokenTag.filter(type="auto_rotation",is_deleted=0)
    return [i.token_id for i in records]

async def deal_token(sem,session,token):
    async with sem:
        now = int(time.time())
        filter_dict = {
            "country_id": token.proxy.country_id,
            "status": 1,
            "pi__user_tokens__user_id__not":token.user_id
        }
        info = await UserInfo.filter(uid=token.user_id).first()
        if info.balance< 150:
            return
        is_ok = await _post(session, token.url,Tools.config.service_conf.http_port if token.network==NetworkType.http else Tools.config.service_conf.socks5_port,username=token.username,pw=token.passwd)
        if is_ok:
            return
        proxy_created_on = token.proxy.created_on
        if token.proxy.state_id:
            filter_dict["state_id"] = token.proxy.state_id
        if token.proxy.city_id:
            filter_dict["city_id"] = token.proxy.city_id
        records = await TDynamicIpPool.filter(**filter_dict).select_related("pi__user_tokens").annotate(rand=RawSQL("Rand()")).order_by("rand")\
            .limit(10).values("host","port","pi_id","created_on","pm_id","ip_id","id","status")
        for record in records:
            is_ok = await _post(session, record.get("host"), record.get("port"))
            if is_ok:
                if record.get("pi_id",0) is not None and record.get("pi_id",0) > 0:
                    proxy = await TProxyIp.filter(id=record.get("pi_id")).first()
                else:
                    proxy = await TProxyIp.create(
                        pm_id=record.get("pm_id"),
                        forward_port=record.get("port"),
                        port=record.get("port"),
                        ip_id=record.get("ip_id"),
                        online=1,
                        status=1,
                        health=1,
                        country_id=token.proxy.country_id,
                        state_id=0,
                        city_id=0,
                    )
                    await TDynamicIpPool.filter(id=record.get("id")).update(pi_id=proxy.id)
                # 替换token缓存

                # 构建proxy缓存
                # 构建pm缓存
                # 构建ip缓存
                await TUserToken.filter(id=token.id).update(pi_id=proxy.id,cost=token.cost+150)
                await del_token_cache(token)
                # 扣费
                if now - proxy_created_on > 60*3:
                    await add_ready_ip_order(update_user_info=True, token_id=token.id,
                                             user_id=token.user_id, type="-", currency=0, currency_type="USD",
                                             receive_currency=0, payway="buy_a_dynamic", pay_order="", is_inner=True,
                                             value=150, extra_value=0,
                                             status=1, checksum=True, valid=True)

                break
            else:
                await TDynamicIpPool.filter(id=record.get("id")).update(status=-1)
        else:
            # 退款
            ...
async def rotation_token():
    session = aiohttp.ClientSession()

    token_ids = await search_all_auto_rotation_tokens()
    r = Tools.redis_14.pipeline()
    for token in token_ids:
        r = r.get(f"TOKEN_IS_USING_{token}")
    results = await r.execute()
    await r.close()
    token_ids = [v for i,v in zip(results,token_ids) if i is not None]
    token_records = await TUserToken.filter(id__in=token_ids,is_static=0,la_id=0).select_related("proxy")
    # # 判断token是否能连接，不能的情况下再执行
    # if now - token.created_on > 24*60*60:
    #     return
    sem = asyncio.Semaphore(50)


    # for token in token_records:
    #     # 只有动态ip能执行
    #     await deal_token(sem,session,token)
    jobs = [deal_token(sem,session,token)  for token in token_records]
    await asyncio.gather(*jobs)
    await session.close()


async def set_auto_rotation(token_id,uid):
    now = int(time.time())
    auto_rotation_record = await TTokenTag.filter(token_id=token_id, type='auto_rotation',uid=uid).first()
    if auto_rotation_record:
        auto_rotation_record.is_deleted = int(not bool(auto_rotation_record.is_deleted))
        auto_rotation_record.deleted_on = now if ( bool(auto_rotation_record.is_deleted)) else 0
        await auto_rotation_record.save()
    else:
        auto_rotation_record = await TTokenTag.create(token_id=token_id, type='auto_rotation',uid=uid)
    return auto_rotation_record.id


async def get_secret(network="",protocol=""):
    conn = Tortoise.get_connection("default")
    if network == "HTTP":
        value = random.randrange(4100, 4111)
    else:
        value = random.randrange(4200, 4211)
    if protocol == "socks":
        value = 5001
    q = await conn.execute_query_dict("select * from t_proxy_secret where port=%s limit 1",[value])
    return {"value":q[0].get("value"),"domain":q[0].get("domain"),"port":q[0].get("port")}


async def proxy_auto_renew():
    # 代理自动续费

    conn =Tortoise.get_connection("default")
    records = await conn.execute_query_dict("""
    select token_id,pay_way_id,t_static_ip_renew.uid as uid from t_static_ip_renew left join t_user_tokens on token_id= t_user_tokens.id where  created_by=-1 and is_auto_renew=1 and   t_user_tokens.created_on + life_time*3600*24 -UNIX_TIMESTAMP() <12*3600 and expired=0
    """)

    datas = []
    for record in records:
        uid = record.get('uid')
        token_id = record.get('token_id')
        payway_id = record.get('pay_way_id')

        data = {'uid': uid, 'token_id': token_id, 'payway_id': payway_id, 'lang': 'cn',
                'is_auto_renew': True, "execute_user": 0}
        data = await Tools.celery.send_task('tasks.static_ip_proxy.renew_private_static_ip', kwargs=data)
        datas.append(data)
    return datas

def get_id_from_text(text:str=""):
    def extract_ids(text):

        # 使用正则表达式匹配 # 后跟数字的模式
        pattern = r'#(\d+)'
        
        # 查找所有匹配项
        matches = re.findall(pattern, text)
        
        # 返回匹配到的ID列表
        return matches

    # 示例文本
    text = """
    Subnet Changed
Hello!

You've made an order with IPRoyal proxies and your proxies were changed to new ones in another subnet.

 
We changed subnets:

#45894731

************** -> *************


#47589523

************** -> *************


#32386887

************* -> ************


#34855809

************** -> ************


#46137935

************ -> *************


#46388060

************* -> *************


#44593360

************** -> *************


#40961283

************* -> *************


#45875261

************** -> *************


#45898954

************** -> ************


#36841243

************* -> ************


#39246895

************** -> *************


#47334602

************** -> *************


#39258450

************** -> *************


#29451363

************** -> ************


#17720231

************** -> *************


#39327668

************** -> *************


#36798196

************** -> *************


#45663744

************** -> *************


#46743396

************** -> ************


#38791949

************* -> ************


#47338544

************* -> *************

 
We decided to do this because we encountered some problems with the original subnet containing your proxies. There's nothing to worry about - we guarantee that the new proxies will work even better!
    """  # 这里是完整文本

    # 提取ID
    ids = extract_ids(text)
    return ids

async def change_iproyal_ip(order_id:int):

    async def deal_proxy(pi_id,ip_obj):
        pi_obj = await TProxyIp.filter(id=pi_id).first()
        if not pi_obj:
            return
        pi_obj.username = username
        pi_obj.password = password
        pi_obj.ip_id = ip_obj.id
        pi_obj.health = 1
        pi_obj.status = 1
        pi_obj.online = 1

        # 更新pm
        pm_obj = await TProxyMachines.filter(id=pi_obj.pm_id).first()
        pm_obj.ip_id = ip_obj.id
        pm_obj.domain = ""

        await pm_obj.save()
        Tools.log.debug(f"更新pm缓存: {pm_obj.id} ip_id:{pm_obj.ip_id} domain:{pm_obj.domain}")
        await pi_obj.save()
        Tools.log.debug(f"更新ip缓存: {pi_obj.id} ip_id:{pi_obj.ip_id} username:{pi_obj.username} password:{pi_obj.password}")
        return True

    async def deal_traffic(traffic_id,ip_obj):
        traffic_obj = await TTrafficIps.filter(id=traffic_id).first()
        if not traffic_obj:
            return
        traffic_obj.username = username
        traffic_obj.password = password
        traffic_obj.ip_id = ip_obj.id

        await Tools.redis_14.delete(f"CACHE_USER_TOKEN_{traffic_obj.username}_{traffic_obj.password}")
        await Tools.redis_14.delete(f"CACHE_USER_TOKEN_{traffic_obj.username}_{traffic_obj.password}")
        traffic_obj.host = ip_obj.ip
        traffic_obj.ip = ip_obj.ip
        traffic_obj.ip_int = ip_obj.ip_addr
        traffic_obj.ip_hex = ip_obj.ip_address
        traffic_obj.is_health = True
        traffic_obj.last_check_time = time.time()
        traffic_obj.check_proxy = f"{traffic_obj.network_type}://{traffic_obj.username}:{traffic_obj.password}@{traffic_obj.ip}:{traffic_obj.port}"
        
        # 更新ip
        for i in await TUserTrafficIp.filter(ti_id=traffic_id).all().values("pi_id"):
            await deal_proxy(i.get("pi_id"),ip_obj)

        try:
            await traffic_obj.save()
            Tools.log.debug(f"更新traffic缓存: {traffic_obj.id} host:{traffic_obj.host} ip:{traffic_obj.ip} ip_int:{traffic_obj.ip_int} ip_hex:{traffic_obj.ip_hex} username:{traffic_obj.username} password:{traffic_obj.password}")
        except Exception as e:
            Tools.log.error(f"更新traffic缓存失败: {e}")

        return True

    # 获取iproyal的ip
    try:
        order_info = await get_iproyal_order_info(order_id)
    except Exception as e:
        Tools.log.error(f"获取iproyal的ip失败: {e}")
        return fail_data(msg="获取iproyal的ip失败")
    if not order_info:
        return fail_data(msg="订单不存在")

    """
    {
  "id": 36210880,
  "note": null,
  "product_name": "ISP (Static Residential)",
  "expire_date": "2025-03-14 03:44:01",
  "plan_name": "30 Days",
  "status": "expired",
  "location": "United States",
  "quantity": 1,
  "questions_answers": [],
  "proxy_data": {
    "ports": {
      "socks5": 12324,
      "http|https": 12323
    },
    "proxies": [
      {
        "username": "14a35d0d9dcee",
        "password": "1a839b8f47",
        "ip": "*************"
      }
    ]
  },
  "auto_extend_settings": null,
  "extended_history": [
    {
      "created_at": "2024-11-14T03:44:00.000000Z",
      "expired_at": "2024-12-14T03:44:01.000000Z"
    },
    {
      "created_at": "2024-12-11T08:10:04.000000Z",
      "expired_at": "2025-01-13T03:44:01.000000Z"
    },
    {
      "created_at": "2025-01-12T00:08:17.000000Z",
      "expired_at": "2025-02-12T03:44:01.000000Z"
    }
  ]
}
    """    
    proxies = order_info.get("proxy_data").get("proxies")
    username = proxies[0].get("username")
    password = proxies[0].get("password")
    ip = proxies[0].get("ip")

    ip_obj = await TIps.filter(ip=ip).first()
    Tools.log.debug(f"ip_obj: {ip}")
    if not ip_obj:
        try:
            ip_obj = await TIps.create(ip=ip,ip_addr=ip2int(ip),ip_address=ip2hex(ip))
        except Exception as e:
            Tools.log.error(f"创建ip失败: {e}")
            return fail_data(msg="创建ip失败")

    for i in await TIproyalOrder.filter(order_id=order_id).all().values("pi_id","traffic_id"):
        if i.get("pi_id"):
            await deal_proxy(i.get("pi_id"),ip_obj)
        if i.get("traffic_id"):
            await deal_traffic(i.get("traffic_id"),ip_obj)

    return suc_data()



# 流量池

async def get_traffic_payway_list(type:str):
    """
    流量池购买套餐列表
    """
    if type == '1':
        name='优质线路'
    elif type == '2':
        name='低价线路'
    payways = await TPayway.filter(deleted_on=0,payway="traffic_pool",traffic_pool_config__name=name).select_related("traffic_pool_config").order_by("order_num").annotate(GB=F("days")).all().values("id","price","GB","traffic_pool_config__name","traffic_pool_config__origin_price",'tag')
    for i in payways:
        i["origin_price"] = i.pop("traffic_pool_config__origin_price")
        i["name"] = i.pop("traffic_pool_config__name")
        tag = i.pop("tag")
        if tag != '0':
            i["tag"] = tag
    return payways


async def get_traffic_pool_remaining(uid:int):
    """
    统计流量包状态
    """
    # traffic_pool = await TUserTrafficPool.filter(uid=uid,status=1).select_related("pay_way").annotate(
    #     total_traffic=Sum("total_traffic"),
    #     current_cost_traffic=Sum("current_cost_traffic"),
    #     price=Sum(F("pay_way__price")*F("pay_way__days"))
    # ).first().values("total_traffic","current_cost_traffic","price")
    conn = tortoise.Tortoise.get_connection("default")
    data = await conn.execute_query_dict(f"SELECT sum(total_traffic) total_traffic,sum(current_cost_traffic) as current_cost_traffic,sum(price*days) as price FROM t_user_traffic_pool left join t_payways on t_payways.id=pay_way_id WHERE  uid={uid} and status = 1")
    traffic_pool = {}
    if data:
        traffic_pool = data[0]
    if not traffic_pool:
        traffic_pool = {
            "total_traffic":0,
            "current_cost_traffic":0,
            "price":0
        }
    total_traffic = traffic_pool.get("total_traffic",0) or 0
    current_cost_traffic = traffic_pool.get("current_cost_traffic",0) or 0
    price = traffic_pool.get("price",0) or 0
    return {
        "total_traffic":total_traffic,
        "current_cost_traffic":current_cost_traffic,
        # 剩余流量
        "remaining_traffic":total_traffic-current_cost_traffic,
        "price":price
    }
    

async def get_traffic_pool_summary(uid:int,start_time:int,end_time:int):
    """
    流量池流量消耗统计
    """
    sql = f"""
     select if(s is null,case MAX(payway)  
	when 'buy_1g_dynamic_traffic' then 1 
	when 'buy_1g_oxylabs_dynamic_traffic' then 1 
	when 'buy_1g_luminati_dynamic_traffic' then 2 
	when 'buy_1g_luna_dynamic_traffic' then 3 
	when 'buy_1g_netnut_dynamic_traffic' then 3 
	when 'buy_1g_smart_dynamic_traffic' then 4 end,s) as s,date(FROM_UNIXTIME(t_traffic_pool_log.created_on)) s_date,sum(t_traffic_pool_log.traffic) traffic from t_traffic_pool_log left join t_proxy_line using(token_id) 
 left join t_user_tokens on token_id=t_user_tokens.id
 left join t_luminati_accounts on t_luminati_accounts.id=la_id
  where t_traffic_pool_log.created_on between %s and %s
  and t_traffic_pool_log.uid = %s
	group by s_date,s,payway
    """
    data = await tortoise.Tortoise.get_connection("default").execute_query_dict(sql,[start_time,end_time,uid])
    """
    data数据：
    [
        {"s_date":"2025-03-21","s":1,"traffic":*********},
        {"s_date":"2025-03-21","s":2,"traffic":*********},
    ]
    """
    df = pd.DataFrame(data,columns=["s_date","s","traffic"])
    df = df.groupby(["s_date","s"]).sum().reset_index()
    df = df.pivot(index='s_date', columns='s', values='traffic')
    summary = df.sum().to_dict()
    df = df.fillna(0)
    values = df.to_dict('list')
    keys = df.index.tolist()
    return {
        "keys":keys,
        "summary":summary,
        "values":values
    }

async def get_traffic_pool(uid:int,page:int=1,page_size:int=10):
    """
    流量池列表
    """
    query = TUserTrafficPool.filter(uid=uid)
    total = await query.count()
    data = await query.order_by("-id").limit(page_size).offset((page-1)*page_size).select_related("pay_way").select_related("pay_way__traffic_pool_config").all().values("id","pay_way_id","current_cost_traffic","total_traffic","status","pay_way__traffic_pool_config__name","pay_way__traffic_pool_config__origin_price","pay_way__price",'pay_way__days',"pay_way__traffic_pool_config__en_name")
    for i in data:
        i["origin_price"] = i.pop("pay_way__traffic_pool_config__origin_price")
        i["name"] = i.pop("pay_way__traffic_pool_config__name")
        i["en_name"] = i.get("pay_way__traffic_pool_config__en_name","")
        i["pre_price"] = i.pop("pay_way__price")
        i["GB"] = i.pop("pay_way__days")

    return {
        "total":total,
        "page":page,
        "page_size":page_size,
        "data":data
    }


# @tortoise.transactions.atomic(connection_name="default")
async def buy_traffic(uid:int,payway_id:int):
    """
    流量池购买
    """

    payway = await TPayway.filter(id=payway_id).first()
    if not payway:
        return fail_data(msg="套餐不存在")
    # 查看用户余额
    user_info = await UserInfo.filter(uid=uid).first()
    if not user_info:
        return fail_data(msg="用户不存在")
    if user_info.balance < (payway.pay_value *payway.days):
        return fail_data(code=3002,msg="余额不足")

    await add_ready_ip_order(
        user_id=uid,
        type="-",
        currency_type="",
        currency=0,
        receive_currency=0,
        is_inner=True,
        payway=payway.payway,
        value=payway.pay_value*payway.days,
        extra_value=0,
        status=1,
        valid=True,
        update_user_info=True,
        traffic_usage=0,
        
    )
    # 创建流量池    
    traffic_pool_config = await TPayway.filter(id=payway_id).first().values("traffic_pool_config_id")
    await Tools.redis.delete(f"TRAFFIC_ALARM_{uid}")
    await TUserTrafficPool.create(uid=uid,pay_way_id=payway_id,current_cost_traffic=0,total_traffic=payway.days*1024*1024*1024,status=1,traffic_pool_config_id=traffic_pool_config.get("traffic_pool_config_id"))
    return suc_data()



async def get_traffic_consume_log(uid:int,id:int=0,page:int=1,page_size:int=10):
    """
    消耗日志
    """
    query = TTrafficPoolLog.filter(uid=uid)
    if id:
        query = query.filter(traffic_pool_id=id)

    total = await query.count()
    data = await query.order_by("-id").limit(page_size).offset((page-1)*page_size).all().values("id","created_on","ip_order_id","traffic")
    return {
        "total":total,
        "page":page,
        "page_size":page_size,
        "data":data
    }


async def get_traffic_consume_log_summary(uid:int,start_time:int,end_time:int,id:int=0):
    """
    消耗日志汇总
    """

    sql = f"""
SELECT 
    DATE_FORMAT(FROM_UNIXTIME(t_traffic_pool_log.created_on), '%Y-%m-%d') AS date_key,
    t_traffic_pool_config.en_name,
    SUM(traffic) AS traffic
FROM t_traffic_pool_log 
LEFT JOIN t_user_traffic_pool 
    ON t_traffic_pool_log.traffic_pool_id = t_user_traffic_pool.id
LEFT JOIN t_traffic_pool_config 
    ON t_user_traffic_pool.traffic_pool_config_id = t_traffic_pool_config.id
WHERE t_user_traffic_pool.uid = {uid}
    """
    if start_time:
        sql += f" AND t_traffic_pool_log.created_on >= {start_time}"
    if end_time:
        sql += f" AND t_traffic_pool_log.created_on <= {end_time}"
    sql += f" GROUP BY t_traffic_pool_config.en_name, date_key"

    data = await tortoise.Tortoise.get_connection("default").execute_query_dict(sql)
    """
    data数据：
    	优质流量	336909390
2025-03-21	优质流量	*********
    """
    [
        {"date_key":"2025-03-21","en_name":"优质流量","traffic":*********},
        {"date_key":"","en_name":"优质流2","traffic":*********},
    ]
    Tools.log.debug(f"data: {data}")
    df = pd.DataFrame(data,columns=["date_key","en_name","traffic"]).fillna(0)
    Tools.log.debug(f"df: {df}")
    df = df.pivot(index='date_key', columns='en_name', values='traffic')
    summary = df.sum().to_dict()
    df = df.fillna(0)
    values = df.to_dict('list')
    keys = df.index.tolist()
    return {
        "keys":keys,
        "summary":summary,
        "values":values
    }



async def update_traffic_alarm(uid:int,enable:bool,traffic:int,type:str,alarm_value:str):
    """
    更新流量报警
    """
    alarm = await TTrafficAlarm.filter(uid=uid).first()
    if not alarm:
        await TTrafficAlarm.create(uid=uid,traffic=traffic,type=type,alarm_value=alarm_value)
    else:
        
        await TTrafficAlarm.filter(uid=uid).update(enable=enable,traffic=traffic,type=type,alarm_value=alarm_value)
    return True


async def get_traffic_alarm(uid:int):
    """
    获取流量报警
    """
    alarm = await TTrafficAlarm.filter(uid=uid).first()
    if not alarm:
        return suc_data()
    return suc_data(data={
        "enable":alarm.enable,
        "traffic":alarm.traffic,
        "type":alarm.type,
        "alarm_value":alarm.alarm_value
    })
