# -*- coding: utf-8 -*-
# @Time    : 2023/10/23 10:38
# <AUTHOR> hzx1994
# @File    : middleware.py
# @Software: PyCharm
# @description:
import asyncio
from time import time
from starlette.requests import Request
from fastapi.responses import RedirectResponse
from fastapi import FastAPI
from starlette.responses import JSONResponse

from controllers.user import get_client_ip
from models.db.proxy import TUserLog, UserInfo
from models.response import fail_data, StateCode
from models.user import UserOut
from utils import Tools, get_region, get_path


def add_middleware(app:FastAPI):

    # 用户region缓存，避免每次请求都查询数据库
    from utils import cache

    @cache(ttl=300)  # 缓存5分钟
    async def get_user_region(user_id: int):
        """获取用户region信息，带缓存"""
        user_info = await UserInfo.filter(uid=user_id).first().values("region")
        return user_info.get("region") if user_info else None

    #重定向到指定的路径
    async def redirect_to_dashboard(request: Request):

        refer_host = request.headers.get("referer") or request.headers.get("origin") or ''
        # if "/docs" in refer_host:
        #     return
        region = get_region(refer_host)
        user_id = request.scope.get('user', {}).get('uid', 0)
        if not user_id:
            return

        # 使用缓存获取用户region，避免每次请求都查询数据库
        user_region = await get_user_region(user_id)
        if user_region is None:
            return

        if user_region and user_region != region:
            url = str(request.url)
            domain = Tools.config.gpt_dashboard_cn_host if region == 0 else Tools.config.gpt_dashboard_host
            path_query = get_path(url)
            url = f"https://{domain}{path_query}"
            return RedirectResponse(url)

    async def add_log_to_db(request:Request,other_msg=""):
        if not request.scope.get("to_mysql",True):
            return
        user_id = request.scope.get('user',{}).get('uid',0)
        if not user_id:
            return
        # 钩子，记录全局日志
        await TUserLog.create(
            client_ip = await get_client_ip(request),
            ip_id = 0,
            uid = user_id,
            log_type_id = 12,
            msg = request.url.path+" :"+request.method+f" {other_msg}",
        )




    @app.middleware("http")
    async def add_process_time_header(request: Request, call_next):
        # 中间件
        st = time()*1000
        headers = request.headers
        if "X-Forwarded-For" in headers or 'x-forwarded-for' in headers:
            host = headers["X-Forwarded-For"].split(",")[0].strip()
        elif "x-real-ip" in headers:
            host = headers["x-real-ip"]
        else:
            host = request.scope['client'][0]
        request.scope['client'] = (host,0)

        response = await call_next(request)
        ed = time()*1000

        Tools.log.debug(f"process_time:{ed-st}")
        # 异步记录日志
        asyncio.ensure_future(add_log_to_db(request,f"process_time: {round(ed-st,3)} ms"))

        return response
