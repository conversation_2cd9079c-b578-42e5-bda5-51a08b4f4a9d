<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Proxy302 - {{账单}}</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <style>
    .font-regular {
      font-family: "Poppins", sans-serif;
      font-weight: 400;
      font-style: normal;
    }

    .font-medium {
      font-family: "Poppins", sans-serif;
      font-weight: 500;
      font-style: normal;
    }

    .font-semibold {
      font-family: "Poppins", sans-serif;
      font-weight: 600;
      font-style: normal;
    }

    .font-bold {
      font-family: "Poppins", sans-serif;
      font-weight: 700;
      font-style: normal;
    }
  </style>
  <style>
    body {
      font-family: "Poppins", sans-serif;
      margin: 0;
      font-weight: 400;
      font-style: normal;
      padding: 0px;
      --theme-color: #4560E4;
      --black-100: #3C445E;
      --black-200: #041F48;
      --text-base: 14px;
      --text-lg: 16px;
      --text-xl: 18px;
      --text-2xl: 26px;
      --text-3xl: 36px;
      font-size: var(--text-base);
      color: var(--black-100);
    }

    .invoice-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
    }

    .logo-section {
      display: flex;
      flex-direction: column;
    }

    .logo {
      margin-right: 10px;
    }

    .logo img {
      width: 156px;
    }

    .company-address {
      font-size: var(--text-base);
    }

    .invoice-title {
      font-size: var(--text-3xl);
      color: var(--black-100);
      text-align: right;
      letter-spacing: 2px;
    }

    .invoice-details {
      text-align: right;
      color: var(--theme-color);
      font-size: var(--text-lg);
    }

    .invoice-details span {
      font-weight: normal;
      font-size: var(--text-base);
      color: #333;
    }

    .bill-info {
      margin-bottom: 30px;
    }

    .bill-info h3 {
      color: var(--theme-color);
      margin-bottom: 2px;
      font-size: var(--text-lg);
    }

    .bill-section {
      margin-bottom: 20px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 50px 0 100px 0;
      font-size: var(--text-xl);
    }

    th {
      text-align: left;
      padding: 10px 0;
      border-bottom: 1px solid #ddd;
      color: var(--theme-color);
    }

    td {
      padding: 15px 0;
      border-bottom: 1px solid #ddd;
    }

    .amount {
      color: var(--theme-color);
    }

    .fees {
      font-size: 14px;
      /* font-style: italic; */
    }

    .total-amount {
      font-size: var(--text-2xl);
    }

    .text-right {
      text-align: right;
    }

    .bank-info {
      position: relative;
      margin-top: 50px;
      margin-bottom: 50px;
    }

    .bank-info p {
      margin: 5px 0;
      font-size: var(--text-base);
      font-weight: 500;
      max-width: 50%;
    }

    .bank-info span {
      margin-left: 3px;
      font-weight: 400;
    }

    .bank-download {
      position: absolute;
      right: 10%;
      bottom: 10%;
      border: 1px solid var(--theme-color);
      border-radius: 5px;
      padding: 10px 20px;
      color: var(--theme-color);
      cursor: pointer;
    }

    .support {
      text-align: center;
      color: #666;
      margin-top: 50px;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }

    @media (max-width: 768px) {
      body {
        --text-base: 12px;
        --text-lg: 14px;
        --text-xl: 16px;
        --text-2xl: 24px;
        --text-3xl: 32px;
      }

      .logo img {
        width: 130px;
      }

      table {
        margin: 20px 0 50px 0;
      }

      .bank-info p {
        max-width: 100%;
      }

      .bank-download {
        right: 3%;
        bottom: 0;
        transform: translateY(30px);
      }
    }
  </style>
</head>

<body>
  <div class="invoice-container">
    <div class="header">
      <div class="logo-section">
        <div class="logo">
          <img src="data:image/png;base64,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" alt="">
        </div>
        <div>
          <div class="company-address">Univerads Technology Limited, HONG KONG</div>
        </div>
      </div>
      <div>
        <div class="invoice-title font-regular">{{账单}}</div>
        <div class="invoice-details font-bold">
          <div>{{账单编号}}: <span>{{order_id}}</span></div>
          <div>{{账单日期}}: <span>{{order_created_on}}</span></div>
        </div>
      </div>
    </div>

    <div class="bill-info">
      <div class="bill-section">
        <h3>{{开票给}}:</h3>
        <div>{{pay_email}}</div>
      </div>

      <div class="bill-section">
        <h3>{{支付给}}:</h3>
        <div>Univerads Technology Limited</div>
        <div>FLAT A112, 1/F, LEE KA INDUSTRIAL BUILDING, 8 NG FONG STREET, SAN PO KONG, KL</div>
      </div>
    </div>

    <table>
      <thead>
        <tr>
          <th style="width: 60%;">{{描述}}</th>
          <th style="width: 20%;">{{金额}}</th>
          <th style="width: 20%;"><!-- 占位符 --></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{order_note}}</td>
          <td>${{order_price}}</td>
          <td><!-- 占位符 --></td>
        </tr>
        <tr>
          <td><!-- 占位符 --></td>
          <td class="font-bold amount">{{总计}}</td>
          <td class="total-amount font-bold text-right">${{order_total}}</td>
        </tr>
        <tr>
          <td><!-- 占位符 --></td>
          <td><!-- 占位符 --></td>
          <td class="fees text-right">{{手续费}}: {{order_service_fee}}</td>
        </tr>
      </tbody>
    </table>

    <div class="bank-info">
      <p>Account no: <span>*********</span></p>
      <p>Bank Name: <span>DBS Bank (Hong Kong) Limited</span></p>
      <p>Bank Address: <span>G/F, The Center,99 Queen's Road Central, Central, Hong Kong</span></p>
      <p>Swift: <span>DHBKHKHHXXX</span></p>

      <div id="downloadBtn" class="bank-download" onclick="downloadPDF()">
        DOWNLOAD
      </div>
    </div>

    <div class="support">
      <EMAIL>
    </div>
  </div>

  <script>
    function downloadPDF() {
      const downloadBtn = document.getElementById('downloadBtn');
      downloadBtn.style.display = 'none';  // 隐藏按钮

      const element = document.querySelector('.invoice-container');
      const opt = {
        margin: 10,
        filename: 'proxy302-bill.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      html2pdf().set(opt).from(element).save().then(() => {
        downloadBtn.style.display = 'block';  // 生成完成后显示按钮
      });
    }
  </script>
</body>

</html>