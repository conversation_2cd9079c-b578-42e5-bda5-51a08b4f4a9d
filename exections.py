# -*- coding: utf-8 -*-
# @Time    : 2023/8/21 16:34
# <AUTHOR> hzx1994
# @File    : exections.py
# @Software: PyCharm
# @description:
import json

from celery.bin.control import status
from fastapi import  FastAPI,status
from fastapi.exceptions import RequestValidationError
from starlette.responses import JSONResponse
from starlette.exceptions import HTTPException
from models.response import fail_data, StateCode


class AuthException(Exception):
    def __init__(self, detail: str = "Incorrect username or password"):
        self.detail = detail

class ComstomException(Exception):
    def __init__(self, detail: str = "pid is needed",code: int = -1):
        self.detail = detail
        self.code = code

class ShareCodeExistsErr(Exception):
    def __init__(self, detail: str = "share code exists"):
        self.detail = detail


def raise_execption(detail="pid is needed", status_code=400):
    raise HTTPException(status_code=status_code, detail=detail)


async def unicorn_exception_handler(request, exc: AuthException):
    data = fail_data(code=StateCode.AuthFail.value, msg=exc.detail)
    data.code = StateCode.AuthFail
    if exc.detail == 'user is exits':
        data.code = StateCode.UserExistError
    data.msg = exc.detail
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )


async def user_exception_handler(request, exc: HTTPException):
    data = fail_data(code=StateCode.AuthFail.value, msg=exc.detail)
    data.code = StateCode.AuthFail
    data.msg = exc.detail
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )

async def share_core_exists(request, exc: ShareCodeExistsErr):
    data = fail_data(code=StateCode.ShareCodeExists.value, msg=exc.detail)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )


# 自定义错误
async def custom_exception_handler(request, exc: ComstomException):
    data = fail_data(code=exc.code, msg=exc.detail)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )


async def http_exception_handler(request, exc: HTTPException):
    data = fail_data(code= StateCode.FAIL.value, msg=exc.detail)
    data.msg = exc.detail

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )

async def http_422_error_handler(request, exc: RequestValidationError):
    err = str([{"field":i.get("loc")[-1],"err": i.get("msg")} for i in exc.errors()])
    data = fail_data(code=StateCode.FAIL.value,msg=err)
    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=data.dict(),
    )

def add_exections(app:FastAPI):
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(AuthException, unicorn_exception_handler)
    app.add_exception_handler(ShareCodeExistsErr, share_core_exists)
    app.add_exception_handler(RequestValidationError, http_422_error_handler)
    app.add_exception_handler(ComstomException, custom_exception_handler)
