# -*- coding: utf-8 -*-
# @Time    : 2024/3/26 16:48
# <AUTHOR> hzx1994
# @File    : Epay.py
# @Software: PyCharm
# @description:
import copy
import json
import hashlib
from collections import OrderedDict
import aiohttp


def create_sign(parameters, key):
    replace_empty_with_null(parameters)
    sorted_params = json.dumps(parameters, sort_keys=True)
    sorted_dict = json.loads(sorted_params, object_pairs_hook=OrderedDict)
    q_string = query_string(sorted_dict)
    sbkey = f"{q_string}&key={key}"
    sign = hashlib.sha256(sbkey.encode('utf-8')).hexdigest().upper()
    return sign

def check_sign(parameters, key, check_sign=""):
    sign = create_sign(parameters, key)
    return sign == check_sign

def replace_empty_with_null(parameters):
    keys = copy.copy(list(parameters.keys()))
    for key in keys:
        if not parameters[key]:
            parameters.pop(key)


def query_string(j_obj):
    q_string = ""
    if j_obj is not None:
        for key, value in j_obj.items():
            if isinstance(value, dict):
                nested_qs = query_string(value)
                q_string += f"{key}={{{nested_qs}}}&"
            else:
                q_string += f"{key}={value}&"
        q_string = q_string.rstrip("&")
    return q_string


class Epay:
    # 收银台
    # dev

    def __init__(self, epay_account: str, api_key:str,call_back_url:str=None,host='https://api.epay.com/capi/openapi'):
        self.host = host

        # prod
        # host = "https://api.epay.com/capi/openapi"
        self.send_transaction_url = f"{self.host}/gateway/sendTransaction"
        self.check_order_url = f"{self.host}/payinApi/queryTransaction"
        self.headers = {
            'Content-Type': 'application/json'
        }
        self.epay_account = epay_account
        self.api_key = api_key
        self.call_back_url = call_back_url

    def get_sign(self, parameters: dict):
        return create_sign(parameters, self.api_key)

    async def create_payment(self,order_id:str,amount:str,lang='CN',
                             from_name='GPT302',
                             suc_url='',fail_url='',remark='')->str:
        """
        创建订单，返回支付地址
        :param order_id: 订单号
        :param amount: 金额
        :param lang: 语言
        :param from_name: 发件人
        :param call_back_url: 回调地址
        return:
        url: 支付ui
        """
        suc_url = suc_url
        fail_url = fail_url

        params = {
                "epayAccount": self.epay_account,
                "merchantName": from_name,
                "amount": amount,
                "currency": "USD",
                "paymentCurrency":"USDT",
                "paymentCountry":"US",
                # "senderEpayAccount":"",
                "merchantOrderNo": order_id,
                "notifyUrl": self.call_back_url,
                "successUrl": suc_url,
                "failUrl": fail_url,
                "remark": remark,
                "language": lang.upper(),
                "version": "V2.0.0",

              }

        sign = self.get_sign(params)
        payload = json.dumps({
            "param": params,
            "sign": sign
        })

        async with aiohttp.ClientSession() as session:
            resp = await session.post(self.send_transaction_url, data=payload, headers=self.headers)
            json_detail = await resp.json()

        url = json_detail.get('data',{}).get("epayUrl")

        # print(url)
        return url


    async def check_order(self,order_id,uid=0):
        """
        查询订单状态是否完成交易
        """
        param =  {
            "epayAccount": self.epay_account,
            "version": "V2.0.0",
            "merchantOrderNo":order_id
        }
        payload = json.dumps({
            "param": param,
            "sign": self.get_sign(param)
        })

        async with aiohttp.ClientSession() as session:
            resp = await session.post(self.check_order_url, data=payload, headers=self.headers)
            r = json.loads(await resp.text())
            if r.get("code") !=1:
                return False
            data = await resp.json()
            print(data)
            status = data.get("data",{}).get("status")
            # 订单支付完成
            if status == "7" :
                return status == "7",data.get("data",{}).get("amount")

            if status == '11':
                try:
                    from utils import Tools
                    key = f"epay_lock_{order_id}"
                    if await Tools.redis.exists(key):
                        return False
                    await Tools.redis.set("epay_order_"+order_id,data.get('data').get('epayOrderNo'))
                    if float(data.get('data').get('paymentAmount'))>= float(data.get('data').get('amount')) * 0.9:
                        msg = f"epay订单异常 用户uid:{uid} \nmerchantOrderNo: {order_id} \nepayOrderNo：{data.get('data').get('epayOrderNo')} \n状态异常,已完成收款，请确认订单状态 \n应收{data.get('data').get('amount')} ,实收{data.get('data').get('paymentAmount')}"
                        Tools.feishu3.sendText(msg)
                        return True,data.get('data').get('paymentAmount')

                    msg = f"epay订单异常 用户uid:{uid} \nmerchantOrderNo: {order_id} \nepayOrderNo：{data.get('data').get('epayOrderNo')} \n状态异常,需手动处理\n应收{data.get('data').get('amount')} ,实收{data.get('data').get('paymentAmount')}"
                    Tools.feishu3.sendText(msg)
                    await Tools.redis.set(key,1,ex=60*60*24)
                except :
                    pass


if __name__ == "__main__":
    import requests
    import json
    import asyncio

    epay_account = "<EMAIL>"
    api_key = "0fc99c4041b277de496a16a3797a8ada"
    params = {'amount': '22.00', 'code': '1', 'fee': '0.07', 'sign': 'C81E9D321951004189F60531B683DAF78D34D2665A1A4B248E14D399AC3F8AA0', 'remark': '', 'message': 'success', 'merchantOrderNo': 'epay_20240430vmnlweDC4AFx', 'paymentAmount': '22', 'version': 'V2.0.0', 'epayAccount': '<EMAIL>', 'payerAccount': '', 'epayOrderNo': '201141785120261876666368', 'receiveAmount': '21.**********', 'receiveCurrency': 'USD', 'rate': '1.000000', 'accountNo': '', 'extendFields': '', 'currency': 'USD', 'paymentCurrency': 'USDT', 'status': '7', 'timestamp': '2024-04-30 01:47:16'}
    # x = check_sign(params,api_key,params.pop('sign'))
    # epay_account = "<EMAIL>"
    # api_key = "2d00b386231806ec7e18e2d96dc043aa"
    ep = Epay(epay_account, api_key,"https://api.epay.com/capi/openapi")
    # x = asyncio.run(ep.create_payment("sadsafg2dfsdasa",'100',suc_url="https://www.baidu.com",fail_url="https://www.baidu.com"))
    x = asyncio.run(ep.check_order('epay_20240708LuMbithkE8bd'))
    print(x)
