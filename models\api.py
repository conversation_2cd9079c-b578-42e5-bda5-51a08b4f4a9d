# -*- coding: utf-8 -*-
# @Time    : 2024/7/5 14:38
# <AUTHOR> hzx1994
# @File    : api.py
# @Software: PyCharm
# @description:
from pydantic import BaseModel, Field
from typing import List, Dict, Optional


class ApiInfo(BaseModel):
    method: str = Field(..., example="POST")
    url: str = Field(..., example="https://api.302.ai/v1/chat/completions")
    link: Dict[str, str] = Field(..., example={
        "en": "https://302ai-jp.apifox.cn/api-207705102",
        "jp": "https://302ai-jp.apifox.cn/api-207706456",
        "zh": "https://302ai.apifox.cn/api-147522039"
    })
    title: Dict[str, str] = Field(..., example={
        "en": "Chat（Talk）",
        "jp": "Chat（会話）",
        "zh": "Chat（聊天）"
    })
    stability: Optional[str] = Field(None, example="green")

class ServiceRequest(BaseModel):
    sort_order: int = Field(..., example=427)
    service_type: str = Field(..., example="API超市")
    titles: Dict[str, str] = Field(..., example={"en": "LLM", "jp": "大規模言語モデル", "zh": "语言大模型"})
    tags: Dict[str, str] = Field(..., example={"en": "OpenAI Model", "jp": "OpenAIモデル", "zh": "OpenAI模型"})
    service_names: Dict[str, str] = Field(..., example={"en": "gpt-3.5-turbo", "jp": "gpt-3.5-turbo", "zh": "gpt-3.5-turbo"})
    price_prefix: Optional[str] = Field(..., example="")
    input_token: Optional[int] = Field(..., example=4000)
    suffix: Optional[Dict[str, str]] = Field(..., example={"en": "", "jp": "", "zh": ""})
    links: Optional[Dict[str, str]] = Field(..., example={"en": "https://302ai-en.apifox.cn/doc-5030894", "jp": "https://302ai-jp.apifox.cn/doc-5030913", "zh": "https://302ai.apifox.cn/doc-3704971"})
    input_price: Optional[float] = Field(..., example=0.75)
    output_price: Optional[float] = Field(..., example=1)
    old_input_price: Optional[float] = Field(..., example=1.5)
    old_output_price: Optional[float] = Field(..., example=2)
    description: Optional[Dict[str, str]] = Field(..., example={"en": "GPT3.5", "jp": "GPT3.5", "zh": "GPT3.5"})
    model_id: Optional[int] = Field(..., example=5)
    output_size: Optional[int] = Field(..., example=4000)
    is_multimodal: Optional[bool] = Field(..., example=False)

class CategoryRequest(BaseModel):
    title: Optional[Dict[str, str]] = Field(None, example={"zh": "语言大模型", "en": "Language Model", "jp": "大規模言語モデル"})
    tags: Optional[Dict[str, str]] = Field(None, example={"zh": "文本,多模态,工具", "en": "Text,Multimodal,Tool Call", "jp": "テキスト,マルチモーダル,ツールの使用"})
    description: Optional[Dict[str, str]] = Field(None, example={"zh": "模型描述", "en": "Model Description", "jp": "モデルの説明"})
    link: Optional[Dict[str, str]] = Field(None, example={"zh": "https://302ai.apifox.cn/doc-3704971", "en": "https://302ai-en.apifox.cn/doc-5030894", "jp": "https://302ai-jp.apifox.cn/doc-5030913"})

class CategoryResponse(CategoryRequest):
    id: int

class BrandRequest(BaseModel):
    category_id: int = Field(None, example=1)
    title: Optional[Dict[str, str]] = Field(None, example={"en": "OpenAI Model", "jp": "OpenAIモデル", "zh": "OpenAI模型"})
    description: Optional[Dict[str, str]] = Field(None, example={"en": "OpenAI Model", "jp": "OpenAIモデル", "zh": "OpenAI模型"})
    search_description: Optional[Dict[str, str]] = Field(None, example={"en": "OpenAI Model", "jp": "OpenAIモデル", "zh": "OpenAI模型"})
    tags: Optional[Dict[str, str]] = Field(None, example={"en": "Text,Multimodal,Tool Call", "jp": "テキスト,マルチモーダル,ツールの使用", "zh": "文本,多模态,工具"})
    link: Optional[Dict[str, str]] = Field(None, example={"en": "https://302ai.apifox.cn/doc-3704971", "jp": "https://302ai.apifox.cn/doc-3704971", "zh": "https://302ai.apifox.cn/doc-3704971"})
    img: Optional[Dict[str, str]] = Field(None, example={"en": "https://file.302.ai/gpt/imgs/b104d38b7e648575430010cd9499c068.png", "jp": "https://file.302.ai/gpt/imgs/b104d38b7e648575430010cd9499c068.png", "zh": "https://file.302.ai/gpt/imgs/b104d38b7e648575430010cd9499c068.png"})
    show_model: Optional[bool] = Field(None, example=False)
    api_info: Optional[List[ApiInfo]] = Field(None, example=[{
        "url": "/chat/completions",
        "link": {
            "en": "https://302ai-jp.apifox.cn/api-207705102",
            "jp": "https://302ai-jp.apifox.cn/api-207706456",
            "zh": "https://302ai.apifox.cn/api-147522039"
        },
        "title": {
            "en": "Chat（Talk）",
            "jp": "Chat（会話）",
            "zh": "Chat（聊天）"
        },
        "method": "POST",
        "stability": "green"
    },
    {
        "url": "/chat/completions",
        "link": {
            "en": "https://302ai-jp.apifox.cn/api-207705107",
            "jp": "https://302ai-jp.apifox.cn/api-207706461",
            "zh": "https://302ai.apifox.cn/api-147522044"
        },
        "title": {
            "en": "Chat (gpt-4o Image Analysis)",
            "jp": "Chat（gpt-4o 画像分析）",
            "zh": "Chat（gpt-4o 分析图片）"
        },
        "method": "POST",
        "stability": "green"
    }])

class BrandResponse(BrandRequest):
    id: int


class ModelParamBase(BaseModel):
    model_name: Optional[str] = None
    support_system_role: Optional[bool] = None
    support_stop: Optional[bool] = None
    support_stream: Optional[bool] = None
    max_output_tokens: Optional[int] = None
    max_context_tokens: Optional[int] = None
    min_temperature: Optional[float] = None
    max_temperature: Optional[float] = None
    min_top_p: Optional[float] = None
    max_top_p: Optional[float] = None
    min_frequency_penalty: Optional[float] = None
    max_frequency_penalty: Optional[float] = None
    min_presence_penalty: Optional[float] = None
    max_presence_penalty: Optional[float] = None
    support_attachment_type: Optional[list] = None


class ToolBase(BaseModel):
    """工具基础信息模型
    
    Attributes:
        id (Optional[int]): 工具ID
        name (Optional[str]): 工具名称(中文)
        en_name (Optional[str]): 工具名称(英文) 
        jp_name (Optional[str]): 工具名称(日文)
        description (Optional[str]): 工具描述(中文)
        en_description (Optional[str]): 工具描述(英文)
        jp_description (Optional[str]): 工具描述(日文)
        search_description (Optional[str]): 搜索描述
        domain (Optional[str]): 域名
        cn_domain (Optional[str]): 中文域名
        logo_url (Optional[str]): Logo URL(中文)
        en_logo_url (Optional[str]): Logo URL(英文)
        jp_logo_url (Optional[str]): Logo URL(日文)
        zh_tool_logo_url (Optional[str]): 工具Logo URL(中文)
        en_tool_logo_url (Optional[str]): 工具Logo URL(英文) 
        jp_tool_logo_url (Optional[str]): 工具Logo URL(日文)
        tool_logo_video_url (Optional[str]): 工具Logo视频URL(中文)
        en_tool_logo_video_url (Optional[str]): 工具Logo视频URL(英文)
        jp_tool_logo_video_url (Optional[str]): 工具Logo视频URL(日文)
        prefix (Optional[str]): 前缀
        cate_id (Optional[int]): 分类ID
        extra (Optional[Dict]): 额外信息字典
        ord (Optional[int]): 排序号
        created_time (Optional[str]): 创建时间，前端显示用
        open_source_url (Optional[str]): 开源地址
        open_source (Optional[bool]): 是否开源
    """
    id: Optional[int] = None
    name: Optional[str] = None  
    en_name: Optional[str] = None
    jp_name: Optional[str] = None
    description: Optional[str] = None
    en_description: Optional[str] = None
    jp_description: Optional[str] = None
    search_description: Optional[str] = None
    domain: Optional[str] = None
    cn_domain: Optional[str] = None
    logo_url: Optional[str] = None
    en_logo_url: Optional[str] = None
    jp_logo_url: Optional[str] = None
    zh_tool_logo_url: Optional[str] = None
    en_tool_logo_url: Optional[str] = None
    jp_tool_logo_url: Optional[str] = None
    tool_logo_video_url: Optional[str] = None
    en_tool_logo_video_url: Optional[str] = None
    jp_tool_logo_video_url: Optional[str] = None
    prefix: Optional[str] = None
    cate_id: Optional[int] = None
    extra: Optional[Dict] = None
    ord: Optional[int] = None
    created_time: Optional[str] = None
    open_source_url: Optional[str] = None
    open_source: Optional[bool] = None


class ToolCategoryCreate(BaseModel):
    cn_cate_name: str
    en_cate_name: str
    jp_cate_name: str

class ToolCategoryUpdate(BaseModel):
    cn_cate_name: Optional[str] = None
    en_cate_name: Optional[str] = None
    jp_cate_name: Optional[str] = None

class ToolCategoryResponse(BaseModel):
    id: int
    cn_cate_name: str
    en_cate_name: str
    jp_cate_name: str


class CustomModelBase(BaseModel):
    show_name: str
    api_key: str
    base_url: str = ''
    model: str = ''
    description: Optional[str] = None
    max_tokens: int = 1
    temperature: float = 0
    top_p: float = 0
    frequency_penalty: float = 0
    presence_penalty: float = 0
    allow_upload: bool = False
    allow_functions: bool = False
    allow_deep_thinking: bool = False

class CustomModelCreate(CustomModelBase):
    pass

class CustomModelUpdate(CustomModelBase):
    show_name: Optional[str] = None
    api_key: Optional[str] = None
    token_mapping_id: Optional[int] = None
    base_url: Optional[str] = None
    model: Optional[str] = None

class CustomModelResponse(CustomModelBase):
    id: int
    uid: int
    created_on: int
    updated_on: int