# -*- coding: utf-8 -*-
# @Time    : 2024/9/3 15:01
# <AUTHOR> hzx1994
# @File    : RagNano.py
# @Software: PyCharm
# @description:
import json
from hashlib import md5
from nano_graphrag.base import BaseVectorStorage
import aiohttp
import asyncio
from nano_graphrag._utils import logger
import numpy as np
from nano_graphrag._utils import compute_args_hash, wrap_embedding_func_with_attrs
from nano_graphrag.prompt import PROMPTS
from openai import AsyncOpenAI
import time
import functools
fail_response = "Sorry, I'm not able to provide an answer to that question."

PROMPTS['local_rag_response'] = """
---Role---

You are a helpful assistant responding to questions about data in the tables provided.


---Goal---

Generate a response of the target length and format that responds to the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.

If you don't know the answer, just say so. Do not make anything up.No need to explain the data source.
Output the result using natural semantics. 

---Target response length and format---

{response_type}


---Data tables---

{context_data}


---Goal---

Generate a response of the target length and format that responds to the user's question, summarizing all information in the input data tables appropriate for the response length and format, and incorporating any relevant general knowledge.

If you don't know the answer, just say so. Do not make anything up.No need to explain the data source.
Output the result using natural semantics. 

---Target response length and format---

{response_type}

Add sections and commentary to the response as appropriate for the length and format. Style the response in markdown.
"""
async def get_llm_func(api_key, model, base_url,retry=3):
    async def llm_model_if_cache(
            prompt, system_prompt=None, history_messages=[],retry=retry, **kwargs
    ) -> str:
        from utils import Tools
        Tools.log.debug(f"api_key:{api_key}, model:{model}, base_url:{base_url}")
        openai_async_client = AsyncOpenAI(
            api_key=api_key, base_url=base_url
        )
        messages = []

        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # Get the cached response if having-------------------
        hashing_kv = kwargs.pop("hashing_kv", None)
        messages.extend(history_messages)
        messages.append({"role": "user", "content": prompt})
        if hashing_kv is not None:
            args_hash = compute_args_hash(model, messages)
            if_cache_return = await hashing_kv.get_by_id(args_hash)
            if if_cache_return is not None:
                return if_cache_return["return"]
        # -----------------------------------------------------

        response = await openai_async_client.chat.completions.create(
            model=model, messages=messages, **kwargs
        )

        # Cache the response if having-------------------
        if hashing_kv is not None:
            await hashing_kv.upsert(
                {"args_hash": {"return": response.choices[0].message.content, "model": model}}
            )
        # -----------------------------------------------------

        Tools.log.debug(prompt)
        Tools.log.debug(f"system_prompt---{system_prompt}")
        msg = response.choices[0].message.content
        if msg.startswith("{"):
            try:
                json.loads(msg)
            except:
                Tools.log.debug("llm_model_if_cache err: not json")
                if retry>0:
                    msg = await llm_model_if_cache(
                        prompt, system_prompt=system_prompt, history_messages=[],retry=retry-1, **kwargs
                    )
                    return msg
                else:
                    return None
        return msg

    return llm_model_if_cache


async def get_emb_func(api_key, base_url, model='text-embedding-ada-002',retry=2, **kwargs):
    import utils
    dimension = kwargs.get("dimension",0)
    @wrap_embedding_func_with_attrs(embedding_dim=dimension, max_token_size=8192)
    async def emb_func(texts: list[str], retry=retry,**kwargs) -> np.ndarray:
        utils.Tools.log.debug(f"dimension:{dimension}")
        if not model.startswith("jina"):
            openai_async_client = AsyncOpenAI(api_key=api_key, base_url=base_url, **kwargs)
            response = await openai_async_client.embeddings.create(
                model=model, input=texts, encoding_format="float"
            )
            emb =  np.array([dp.embedding for dp in response.data])
            utils.Tools.log.info(f"emb:{emb.shape}")
            return emb

        url = f"{base_url.replace('/v1', '')}/jina/v1/embeddings"
        data = {
            "model": model,
            "embedding_type": "float",
            "input": texts,
            "dimensions": 768 if model == "jina-embeddings-v3" else None,
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        try:
            async with aiohttp.ClientSession() as s:
                async with s.post(url, headers=headers, json=data) as resp:
                    resp = await resp.json()
        except Exception as e:
            # print(e)
            utils.Tools.log.error(e)
            if retry <= 0:
                raise e
            return await emb_func(texts,retry-1, **kwargs)
        embs = [i["embedding"] for i in resp["data"]]
        emb = np.array(embs)

        # print(f"emb:{padded_data.shape}")
        utils.Tools.log.info(f"emb:{emb.shape}")
        return emb

    return emb_func
from dataclasses import dataclass

client = None
@dataclass
class MilvusStorge(BaseVectorStorage):
    @staticmethod
    def create_collection_if_not_exist(client, collection_name: str, **kwargs):
        if client.has_collection(collection_name):
            return
        # TODO add constants for ID max length to 32
        client.create_collection(
            collection_name, max_length=64, id_type="string", **kwargs
        )
    @staticmethod
    def create_partition_if_not_exist(client, collection_name: str, partition_name: str,**kwargs):
        if client.has_partition(collection_name,partition_name):
            return
        client.create_partition(collection_name, partition_name,**kwargs)
    def __post_init__(self):
        from pymilvus import MilvusClient
        import utils
        # self._client_file_name = os.path.join(
        #     self.global_config["working_dir"], "milvus_lite.db"
        # )
        # client = MilvusClient(
        #     uri="http://localhost:19530"
        # )
        #
        # # 2. Create a collection in quick setup mode
        # client.create_collection(
        #     collection_name="quick_setup",
        #     dimension=5
        # )
        global client
        self.loop = asyncio.get_running_loop()
        if client is None:
            client = MilvusClient("http://*********:19530")
        self._client = client
        self._client.using_database('rag_nano')
        self._max_batch_size = self.global_config["embedding_batch_num"]
        utils.Tools.log.info(f"max_batch_size:{self._max_batch_size}")
        namespace = "c_"+md5(str(self.global_config["working_dir"]).encode()).hexdigest()
        utils.Tools.log.info(f"work_dir:{self.global_config['working_dir']}")
        self.namespace = namespace
        utils.Tools.log.info(f"namespace:{namespace}")
        # 使用时间戳生成唯一分区名称
        timestamp = int(time.time() * 1000)  # Milliseconds since epoch
        self.partition_name = f"p_{timestamp}"
        MilvusStorge.create_collection_if_not_exist(
            self._client,
            self.namespace,
            dimension=self.embedding_func.embedding_dim,
        )

    async def upsert(self, data: dict[str, dict]):
        logger.info(f"Inserting {len(data)} vectors to {self.namespace}")
        list_data = [
            {
                "id": k,
                **{k1: v1 for k1, v1 in v.items() if k1 in self.meta_fields},
            }
            for k, v in data.items()
        ]
        contents = [v["content"] for v in data.values()]
        batches = [
            contents[i : i + self._max_batch_size]
            for i in range(0, len(contents), self._max_batch_size)
        ]
        embeddings_list = await asyncio.gather(
            *[self.embedding_func(batch) for batch in batches]
        )
        embeddings = np.concatenate(embeddings_list)
        for i, d in enumerate(list_data):
            d["vector"] = embeddings[i]
        
        MilvusStorge.create_partition_if_not_exist(
            self._client, 
            self.namespace, 
            self.partition_name
        )
        #使用自定义分区名存储新入库的数据
        results = await self.loop.run_in_executor(None,self._client.upsert,self.namespace,list_data,None,self.partition_name)
        logger.info(f"Upsert operation completed successfully in partition {self.partition_name}")
        return results 
    
    async def del_collection(self):
        await self.loop.run_in_executor(None,self._client.drop_collection,self.namespace)

    #返回集合名称
    async def get_collection_name(self):
        return self.namespace
    
    #判断集合是否具有某个分区
    async def has_partitions(self,partition_name):
        return await self.loop.run_in_executor(None,self._client.has_partition,self.namespace,partition_name)
    #返回集合内的分区名字
    async def get_partitions_name(self):
        return self.partition_name
    #获取集合内所有的id
    async def get_file_ids(self):
        results = await self.loop.run_in_executor(None, self._client.query, self.namespace, {}, ["id"])
        return [result["id"] for result in results]
    
    #获取集合内某个分区的全部数据
    async def get_partition_data(self,partition_name):
        query_with_limit = functools.partial(self._client.query,collection_name=self.namespace,partition_names=[partition_name],limit=1000)
        results = await self.loop.run_in_executor(None,query_with_limit)
        return results
    #删除集合内所有的相关ids数据
    async def delete_file_ids(self,file_ids):
        await self.loop.run_in_executor(None,self._client.delete,self.namespace,file_ids)

    #释放分区数据
    async def release_partition(self,partition_name):
        await self.loop.run_in_executor(None,self._client.release_partitions,self.namespace,partition_name)

    #删除分区
    async def delete_partition(self,partition_name):
        await self.loop.run_in_executor(None,self._client.drop_partition,self.namespace,partition_name)
    
    async def rename(self,new_path):
        self.loop.run_in_executor(None,self._client.rename_collection,self.namespace, "c_"+md5(str(new_path).encode()).hexdigest())

    async def query(self, query, top_k=5):
        embedding = await self.embedding_func([query])
        results = self._client.search(
            collection_name=self.namespace,
            data=embedding,
            limit=top_k,
            output_fields=list(self.meta_fields),
            search_params={"metric_type": "COSINE", "params": {"radius": 0.2}},
        )
        return [
            {**dp["entity"], "id": dp["id"], "distance": dp["distance"]}
            for dp in results[0]
        ]

if __name__ == '__main__':
    import asyncio


    async def main():
        job = await get_emb_func("sk-6ChMnqntd44O44MNkqChNKv0SKFtStlnWSMwoHOj3wqmg9Qb","https://api.302.ai/v1")
        res =  await job(['1','2'] )
        print(res)

    asyncio.run(main())