# -*- coding: utf-8 -*-
# @Time    : 2024/3/20 19:02
# <AUTHOR> hzx1994
# @File    : register.py
# @Software: PyCharm
# @description:
from abc import abstractmethod

from controllers.pay import gift_for_ptc
from controllers.pubilc import ip_to_db, get_question_status_by_id
from controllers.user import create_user, reset_pw, get_user_without_pwd, MyAPIKeyHeader, change_user_pwd, test_ip, \
    save_user_phone
from exections import AuthException, ComstomException
from libs.Github import Github
from models.db.proxy import TUser, TIpOrders
from models.response import StateCode
from models.user import UserIn, UserIn_Body
from utils import get_user_info_from_fire, random_string, Tools, get_hash_password, sms


class Auth():
    send_email=True
    register_from=''

    def __init__(self,register_ip='',ref='',is_gpt=False,zh=True,**kwargs):
        self.user:UserIn_Body =  UserIn_Body()
        self.register_ip:str = register_ip
        self.ref:str = ref
        self.ip_id:int = 0
        self.is_gpt:bool = is_gpt
        self.zh:bool = zh
        self.phone:str=kwargs.get("phone_number",'')
        self.name=kwargs.get("name",'')
        self.password=kwargs.get("password",'')
        self.sms_code = kwargs.get("sms_code",'')
        self.phone_number = kwargs.get("phone_number",'')
        self.region=kwargs.get("region",0)
        self.user.region = self.region
        self.user_id = kwargs.get("user_id","")
        self.lang = kwargs.get("lang")
        self.email = kwargs.get("email")


    async def phone_exists(self):
        if self.phone_number and await TUser.filter(phone=self.phone_number.replace(" ","")).exists():
            raise ComstomException(code=StateCode.PHONE_IS_BAND,detail="phone is exists")

    async def bind_or_change_phone(self,phone_number:str):
        Tools.log.debug(f"绑定手机获取的手机号为  {self.phone or phone_number}")
        if await TUser.filter(phone=self.phone or phone_number).exists():
            raise ComstomException(code=StateCode.PHONE_IS_BAND,detail="phone is exists")
        await save_user_phone(self.phone or phone_number,self.user_id)

        had_answered = not await get_question_status_by_id(self.user_id, is_gpt=self.is_gpt)
        had_gift = await TIpOrders.filter(user_id=self.user_id, status__gt=0, type='+', pay_order__gt="",payway='gift').exists()
        Tools.log.debug(f"had_answered:{had_answered} had_gift:{had_gift}")
        if had_answered and not had_gift:
            await gift_for_ptc('',self.user_id,band_phone=True,ip=self.register_ip)
        # password = get_hash_password(self.password,user.salt,user.created_on)
        # await change_user_pwd(user.uid,password)
        return True

    async def reset_password(self):
        user = await get_user_without_pwd(email=self.user.email,phone=self.phone,user_id = self.user_id)
        if not user:
            raise ComstomException(code=StateCode.USER_NOT_EXIST)
        password = get_hash_password(self.password,user.salt,user.created_on)
        await change_user_pwd(user.uid,password)
        return True
        # await user.save()

    async def user_is_exits(self) -> bool:
        user = await get_user_without_pwd(email=self.user.email,phone=self.phone.replace(" ",""),user_id=self.user_id)
        return bool(user)

    async def send_email_to_reset_pwd(self):
        credential = await reset_pw(self.user.email)

        host = Tools.config.gpt_dashboard_host if self.is_gpt else Tools.config.dashboard_host
        if self.region == 0:
            host = Tools.config.gpt_dashboard_cn_host
        url = f"https://{host}/reset-password/{credential}"
        # url = Tools.config.reset_url.format(credential=credential)
        if self.is_gpt:
            email_obj = Tools.email_for_gpt
        else:
            email_obj = Tools.email


        email_obj.sendResetPasswordEmail(to_user_name=self.user.email, to=self.user.email, token_url=url,
                                         zh=self.zh, is_gpt=self.is_gpt)

    @abstractmethod
    async def get_user_info(self,*args,**kwargs):
        ...

    async def register(self):
        if await self.user_is_exits():
            raise AuthException("user is exits")
        # data = await test_ip(ip=self.register_ip if self.register_ip != "127.0.0.1" else "*************")
        # if data.get("country") == "CN":
        if self.phone_number:
            value = await sms(mobile=self.phone_number,is_gpt=self.is_gpt)
            if not value or self.sms_code != value:
                raise ComstomException("sms code error",code=900)

        ip_id = await ip_to_db(self.register_ip)
        self.user.password = self.password or random_string(10)
        self.user.register_from = self.register_from
        self.user.phone = self.phone.replace(' ','')
        user = await create_user(self.user,self.register_ip,self.ref,ip_id,is_gpt=self.is_gpt,lang=self.lang)
        return user

        # await user.save()


    async def login(self):
        user = await get_user_without_pwd(email=self.user.email,phone=self.phone,user_id = self.user_id)
        if not user:
            return ''
        token = await MyAPIKeyHeader.cache_key(dict(user))
        return token


    async def start(self,*args,**kwargs):
        await self.get_user_info(*args,**kwargs)
        # 判断用户是否注册过，注册过直接充当登录接口
        # 帮用户注册账号（share也一起做了 create_user
        # 异步任务 随机密码邮件通知用户-告知重置密码
        if kwargs.get("login",True) and await self.user_is_exits():
            return await self.login()
        else:
            await self.register()
            if self.send_email and self.user.email:
                await self.send_email_to_reset_pwd()
            return await self.login()



class Google(Auth):
    register_from = 'Google'
    async def get_user_info(self,*args,**kwargs):
        id_token = kwargs.get("id_token",'')
        res = await get_user_info_from_fire(id_token,self.is_gpt)
        Tools.log.debug(res)
        email = res.get("email","")
        self.user_id = user_id = res.get("uid","")
        name = res.get("name") or random_string(8)
        pwd = random_string(10)
        Tools.log.debug(f"pwd:{pwd}")
        self.user = UserIn_Body(email=email,user_id=user_id,password=pwd,name=name,from_invite_code=self.ref,is_gpt=self.is_gpt,region=self.region)

class GooglePwd(Google):
    async def get_user_info(self,*args,**kwargs):
        self.user = UserIn_Body(email=self.email,user_id=self.user_id,password=self.user_id,name=random_string(8),from_invite_code=self.ref,is_gpt=self.is_gpt,region=self.region)


class GithubAuth(Auth):
    register_from = 'Github'
    async def get_user_info(self,*args,**kwargs):
        state = kwargs.get("state",'')
        request_url = kwargs.get("request_url",'')

        conf = Tools.config.ai302_github_conf if self.is_gpt else Tools.config.proxy_github_conf
        github = Github(conf.get("client_id"), conf.get("client_secret"))
        name,user_id,email = github.call_back(state,request_url)
        # res = await get_user_info_from_fire(id_token,self.is_gpt)
        # Tools.log.debug(res)
        # email = res.get("email","")
        self.user_id = user_id
        # name = res.get("name") or random_string(8)
        pwd = random_string(10)
        Tools.log.debug(f"pwd:{pwd}")
        self.user = UserIn_Body(email=email,user_id=user_id,password=pwd,name=name,from_invite_code=self.ref,is_gpt=self.is_gpt,region=self.region)





class Phone(Auth):
    register_from = 'Phone'
    async def get_user_info(self,*args,**kwargs):
        id_token = kwargs.get("id_token",'')
        if id_token:
            res = await get_user_info_from_fire(id_token,self.is_gpt)
            number = res.get("phone_number",'')
            self.phone = number
        if self.phone_number:
            user = await get_user_without_pwd(email=self.user.email, phone=self.phone, user_id=self.user_id)
            # if not user:
            #     raise ComstomException("user is not exits",code=StateCode.USER_NOT_EXIST)
            value = await sms(mobile=self.phone_number,is_gpt=self.is_gpt)
            if not value or self.sms_code != value:
                raise ComstomException("sms code error",code=900)
            self.phone = self.phone_number.replace(" ","")
        name = self.name

        self.user = UserIn_Body(email='',name=name,from_invite_code=self.ref,is_gpt=self.is_gpt,region=self.region)
        # Tools.log.info(self.user.to_dict())
        return True