# -*- coding: utf-8 -*-
# @Time    : 2023/8/25 14:18
# <AUTHOR> hzx1994
# @File    : proxy.py
# @Software: PyCharm
# @description:
from enum import Enum
from typing import List

from fastapi.params import Form, Query, Path
from pydantic import BaseModel

from models.user import Base


# 生成方式
class GenerateType(str, Enum):
    """
    生成类型
    """
    dynamic = "dynamic"
    static = "static"

class ToolType(str, Enum):
    """
    工具类型
    """
    browser = "browser"
    cmd = "cmd"

class Platform(str, Enum):
    """
    平台
    """
    windows = "windows"
    linux = "linux"

    chrome = "chrome"
    _360 = "360"

class ProxyStatus(int, Enum):
    """
    if health == 0:
    return "unhealthy"
if expired_on < time.time():
    # 过期
    return "expired"
if status == 0:
    # 失效
    return "invalid"
return "normal"
    """
    all = 0
    normal = 1
    invalid = 2
    expired = 3
    unhealthy = 4
    on = 5
    off = 6


class UseIn(str, Enum):
    """
    国家过滤方式
    """
    vm ="vm"
    dynamic_traffic = "dynamic_traffic"
    dynamic_ip = "dynamic_ip"


    static_datacenter_traffic = "static_datacenter_traffic"
    static_residential_traffic = "static_residential_traffic"

    static_datacenter_ip = "static_datacenter_ip"
    static_residential_ip = "static_residential_ip"



class Protocol(str, Enum):
    http = "http"
    socks5 = "socks5"

class ProxyType(str, Enum):
    """
    代理类型
    """
    traffic = "traffic"
    ip = "ip"
    cost = "cost"

class VmType(str, Enum):
    """
    虚拟机操作类型
    """
    start = "start"
    stop = "stop"
    restart="restart"
    cancel = "cancelAutoRenew"



class StatisticsType(str, Enum):
    by_hour = "hour"
    by_day = "day"
    by_month = "month"


class IsDataCenter(int, Enum):
    yes = 1
    no = 0


class StatisticsKey(int, Enum):
    """
    统计类型id
    """
    new_user_guide = 21


class Region(int, Enum):
    """
    统计类型id
    """
    cn = 0
    n_cn = 1



class NetworkType(str, Enum):
    """
    代理类型
    """
    http = "http"
    socks5 = "socks5"
    socket = "socket"


class BaseParamsForm(Base):
    """
    分页基础参数
    """

    def __init__(self,  protocol: Protocol = Form(Protocol.http, description="协议，可选"),
                 is_data_center: bool = Form(True, description="是否数据中心,可选")):
        # self.lang = lang
        self.protocol = protocol
        self.is_data_center = is_data_center
        # self.is_static = is_static

class BaseParamsQuery(Base):
    """
    分页基础参数
    """

    def __init__(self, page: int = Query(1, description="page,可选，post请求不用"), page_size: int = Query(10, description="page_size,可选，post请求不用"),
                 protocol: Protocol = Query(None, description="协议，可选"),
                 is_data_center: IsDataCenter = Query(IsDataCenter.yes, description="是否数据中心,可选"),is_static: IsDataCenter = Query(IsDataCenter.yes, description="是否静态IP,可选")):
        self.page = page
        self.page_size = page_size
        # self.lang = lang
        self.protocol = protocol
        self.is_data_center = is_data_center
        self.is_static = is_static


class PayWayParams(str, Enum):
    """支付参数"""
    buy_data_center_private_static_ip = "static_data_center"
    buy_residential_private_static_ip = "static_residential"
    create_vm = "vm"


class Task(str, Enum):
    """
    任务名
    buy_private_static_ip:购买私有静态IP
    """
    # 静态流量-住宅 -- 流量生成
    static_traffic_proxy = "tasks.static_traffic_proxy.make_proxy_by_area"

    # 静态流量-数据中心 -- 流量生成
    static_data_center_traffic_proxy = "tasks.static_data_center_traffic_proxy.make_proxy_by_area"

    # 静态流量-购买私有静态IP-住宅和数据中心统一使用  -- ip生成
    buy_private_static_ip = "tasks.static_ip_proxy.buy_private_static_ip"

    # 动态流量 -- 流量生成
    dynamic_traffic_proxy = "tasks.dynamic_traffic_proxy.make_proxy_by_area"
    # 动态流量-模糊生成-通用代理生成
    dynamic_traffic_blur_proxy = "tasks.dynamic_traffic_proxy.make_proxy_api_proxy"

    # 动态流量 -- IP生成-地区
    dynamic_traffic_proxy_ip_by_area = "tasks.dynamic_ip_proxy.make_proxy_by_area"
    dynamic_traffic_proxy_ip_by_ip = "tasks.dynamic_ip_proxy.make_proxy_by_ip"

    # vm
    create_vm = "tasks.virtual_machine.new_vm"
    vm_list="tasks.virtual_machine.list_vm"
    vm_start = "tasks.virtual_machine.control_vm"
    vm_auto_renew = "tasks.virtual_machine.set_vm_auto_renew"
    vm_renew = "tasks.virtual_machine.renew_vm"



class StaticParams(Base):
    """静态参数"""

    def __init__(self, country_id: int = Form(0, description="国家id"),
                 state_id: int = Form(0, description="洲id"),
                 city_id: int = Form(0, description="城市id"),
                 protocol: str = Form("http", description="协议"),
                 number:int = Form(1, description="生成次数"),
                 s:int = Form(1)
                 ):
        super().__init__()
        self.country_id = country_id
        self.state_id = state_id
        self.city_id = city_id
        self.generate_times = number
        self.protocol = protocol
        self.s = s

class DynamicByIpForm(Base):
    def __init__(self,ip=Form(...,title="ip"),result_same_location:bool=Form(...,title="是否返回相同ip"),protocol=Form("http",title="协议")):
        self.ip=ip
        self.result_same_location=result_same_location
        self.protocol=protocol


class TokenIn(BaseModel):
    tokens: List[int]



class PayWays(BaseModel):
    buy_1g_dynamic_traffic: int=0
    buy_1g_static_traffic: int=0
    buy_1g_static_data_center_traffic: int=0
    create_vm: int=0
    # buy_1_day_private_proxy: str="buy_1_day_private_proxy"
    buy_residential_private_static_ip: int=0
    buy_data_center_private_static_ip: int=0
    buy_a_dynamic: int=0
    # buy_a_static: int=0
    total: int=0


# body
class QuickAccessIn(BaseModel):
    url:str
    token_id:int
    remark:str=""
    group:str=""
    proxy_area:str = ""
