# -*- coding: utf-8 -*-
# @Time    : 2023/8/15 14:47
# <AUTHOR> hzx1994
# @File    : MyCelery.py
# @Software: PyCharm
# @description:
import asyncio
import functools
from celery import Celery
from loguru import logger

class MyCelery(Celery):

    @logger.catch(reraise=False,default={})
    async def send_task(self, name, **options):
        loop = asyncio.get_event_loop()
        rec = await loop.run_in_executor(None, functools.partial(super().send_task, name, **options))
        return await loop.run_in_executor(None, rec.get)
        await asyncio.sleep(2)
        return {"code":0}

        # return loop.run_until_complete(super().send_task(name, **options))
