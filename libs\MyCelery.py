# -*- coding: utf-8 -*-
# @Time    : 2023/8/15 14:47
# <AUTHOR> hzx1994
# @File    : MyCelery.py
# @Software: PyCharm
# @description:
import asyncio
import functools
from celery import Celery
from loguru import logger

class MyCelery(Celery):

    @logger.catch(reraise=False,default={})
    async def send_task(self, name, timeout=30, **options):
        loop = asyncio.get_event_loop()
        try:
            rec = await asyncio.wait_for(
                loop.run_in_executor(None, functools.partial(super().send_task, name, **options)),
                timeout=timeout
            )
           
        except asyncio.TimeoutError:
            logger.error(f"Task {name} timed out after {timeout} seconds")
            return {"code": -1, "error": "Task execution timeout"}
        
        return await asyncio.wait_for(
            loop.run_in_executor(None, rec.get),
            timeout=timeout
        )