# -*- coding: utf-8 -*-
# @Time    : 2023/8/15 14:47
# <AUTHOR> hzx1994
# @File    : MyCelery.py
# @Software: PyCharm
# @description:
import asyncio
import functools
import time
from celery import Celery
from celery.exceptions import ConnectionError as CeleryConnectionError
from kombu.exceptions import ConnectionError as KombuConnectionError
from loguru import logger
from concurrent.futures import ThreadPoolExecutor

pool = ThreadPoolExecutor(30)

class MyCelery(Celery):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._connection_retries = 0
        self._max_retries = 3
        self._retry_delay = 5  # seconds

    def check_connection(self):
        """检查celery连接状态"""
        try:
            # 尝试获取连接
            with self.connection_or_acquire() as conn:
                conn.ensure_connection(max_retries=1)
            return True
        except (CeleryConnectionError, KombuConnectionError, Exception) as e:
            logger.error(f"Celery connection check failed: {e}")
            return False

    def reconnect(self):
        """重连celery broker"""
        logger.info("Attempting to reconnect to Celery broker...")

        try:
            # 关闭现有连接池
            if hasattr(self, '_pool') and self._pool:
                self._pool.force_close_all()
                self._pool = None

            # 重新建立连接
            with self.connection_or_acquire() as conn:
                conn.ensure_connection(max_retries=3)

            logger.info("Successfully reconnected to Celery broker")
            self._connection_retries = 0
            return True

        except (CeleryConnectionError, KombuConnectionError, Exception) as e:
            self._connection_retries += 1
            logger.error(f"Reconnection attempt {self._connection_retries} failed: {e}")

            if self._connection_retries < self._max_retries:
                logger.info(f"Retrying in {self._retry_delay} seconds...")
                time.sleep(self._retry_delay)
                return self.reconnect()
            else:
                logger.error(f"Max reconnection attempts ({self._max_retries}) reached")
                return False

    @logger.catch(reraise=False,default={})
    async def send_task(self, name, timeout=30, **options):
        loop = asyncio.get_event_loop()

        # 尝试发送任务，如果连接失败则重连
        for attempt in range(self._max_retries + 1):
            try:
                rec = await asyncio.wait_for(
                    loop.run_in_executor(pool, functools.partial(super().send_task, name, **options)),
                    timeout=timeout
                )
                break

            except (CeleryConnectionError, KombuConnectionError) as e:
                logger.warning(f"Connection error on attempt {attempt + 1}: {e}")
                if attempt < self._max_retries:
                    logger.info("Attempting to reconnect...")
                    reconnect_success = await loop.run_in_executor(None, self.reconnect)
                    if not reconnect_success:
                        return {"code": -1, "error": "Failed to reconnect to Celery broker"}
                else:
                    logger.error("Max connection attempts reached")
                    return {"code": -1, "error": "Celery connection failed after retries"}

            except asyncio.TimeoutError:
                logger.error(f"Task {name} timed out after {timeout} seconds")
                return {"code": -1, "error": "Task execution timeout"}

            except Exception as e:
                logger.error(f"Unexpected error sending task {name}: {e}")
                return {"code": -1, "error": f"Task send failed: {str(e)}"}

        # 获取任务结果
        try:
            return await asyncio.wait_for(
                loop.run_in_executor(None, rec.get),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.error(f"Task {name} result timeout after {timeout} seconds")
            return {"code": -1, "error": "Task result timeout"}
        except Exception as e:
            logger.error(f"Error getting task {name} result: {e}")
            return {"code": -1, "error": f"Task result failed: {str(e)}"}