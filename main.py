# -*- coding: utf-8 -*-
import logging

import uvicorn
from fastapi import FastAP<PERSON>
from starlette.staticfiles import StaticFiles
from tortoise.contrib.fastapi import register_tortoise

from conf.config import load_yaml_config, add_cors
from conf.db_config import get_config
from exections import  add_exections
from middleware import add_middleware
from utils import load_redis, create_celery, init_loger, load_stripe_key, load_feishu, close_redis, load_mongo, close_celery

from pathlib import Path
from views import router






def init_app():
    app = FastAPI(title='302',version='2.0')


    here = Path(__file__).parent
    conf = load_yaml_config(here / "conf"/"setting.yaml")

    @app.get("/health")
    def health():
        return {"status": "ok"}

    # 注册数据库
    register_tortoise(
        app,
        config=get_config(conf.db),
        # generate_schemas=True,
        add_exception_handlers=True,
    )
    app.include_router(router.router)
    # 加载静态资源
    app.mount("/static", StaticFiles(directory="static"), name="static")
    add_exections(app)
    add_middleware(app)
    add_cors(app)
    init_loger( here/"log"/"info.log",here/"log"/"err.log",level=logging.DEBUG if conf.debug else logging.INFO)
    return app


app = init_app()

@app.on_event("startup")
async def startup_event():
    await load_redis()
    await load_mongo()
    await create_celery()
    load_stripe_key()
    load_feishu()

    import asyncio,utils
    utils.create_images()
    await utils.cache_img(Path('static','imgs'))
    from start_up_jobs import load_and_start_rag
    # asyncio.ensure_future(load_and_start_rag.start_job())


@app.on_event("shutdown")
async def shutdown_event():
    await close_redis()
    await close_celery()





if __name__ == '__main__':
    import sys
    args = sys.argv
    if len(args)==1:
        port = 8000
    else:
        port = args[1]
    config = uvicorn.Config("main:app", host="0.0.0.0", port=int(port), access_log=False, workers=4)
    server = uvicorn.Server(config)

    server.run()
    # uvicorn.run("main:app", host="localhost", port=8000,log_level=level)