FROM python:3.9.18

# SHELL ["/bin/bash", "-c"]
#
# RUN curl https://sh.rustup.rs -sSf | sh -s -- -y
#
# RUN source "$HOME/.cargo/env" && cargo --version
# RUN source "$HOME/.cargo/env" && rustc --version


# 2. 设置工作目录（在容器中创建一个目录用于存放代码）
WORKDIR /app

# 3. 将当前目录中的内容复制到容器的 /app 目录
COPY . /app

# 4. 安装项目所需的依赖包（假设你有一个 requirements.txt 文件）
# RUN source "$HOME/.cargo/env" && pip3 install -r requirement.txt
RUN pip install --upgrade pip==24.0 && pip install --no-cache-dir -r requirement.txt

# 6. 暴露服务端口（假设 Flask 在 5000 端口运行）
EXPOSE 8000

# 7. 定义容器启动时运行的命令
CMD ["python", "main.py"]
