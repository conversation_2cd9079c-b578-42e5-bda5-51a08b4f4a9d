# -*- coding: utf-8 -*-
# @Time    : 2023/9/5 11:25
# <AUTHOR> hzx1994
# @File    : pubilc.py
# @Software: PyCharm
# @description:
import asyncio
from time import time

import aiohttp
import requests
from ronglian_sms_sdk import SmsSDK
from tortoise import Tortoise

from exections import ComstomException
from models.db.gpt import TTokenMapping, TTokenInfo, TToken
from models.db.proxy import TIps, TIpOrders, TTrafficAlarm, TUserTrafficPool, UserInfo, TEmailBlacklists, TUser, TUserLog, TBalanceAlarm
from models.response import fail_data, StateCode
from utils import int2ip, ip2int, ip2hex, Tools, current_timestamp
import re
import datetime


async def test_user_action(user, limit=False):
    if user.email == "<EMAIL>":
        return fail_data(code=StateCode.TEST_USER.value,msg="tourist user has not permission,register and login please!!!")
    # if not limit and await Tools.redis.get(f"NO_MONEY_UID_{user.uid}"):
    #     return fail_data(code=StateCode.CallFrequently.value, msg="调用过频繁")
    # if await UserInfo.filter(uid=user.uid,balance=0).exists():
    #     await Tools.redis.setex(f"NO_MONEY_UID_{user.uid}",60,1)
async def ip_to_db(ip: str):
    ip_addr = ip2int(ip)
    ip_hex = ip2hex(ip)
    ip_str = ip

    ip = await TIps.filter(ip_addr=ip_addr).limit(1)
    if ip:
        return ip[0].id
    ip = await TIps.create(ip_addr=ip_addr, ip_address=ip_hex,ip=ip_str)
    return ip.id


async def sub_ready_ip_order(user_id, **kwargs):
    obj = await TIpOrders.filter(user_id=user_id, status=1).order_by('-orderid').limit(1).first().values(
        "total_money", 'balance')
    if not obj:
        obj = {}
    total_money = obj.get('total_money', 0)
    balance = obj.get('balance', 0)
async def add_ready_ip_order(update_user_info=False, **kwargs):
    now = datetime.datetime.now()
    order_id = int(re.sub(r"-|:|\.| ", '', str(now))) // 100
    t = int(now.timestamp())
    kwargs["orderid"] = order_id
    kwargs["created_on"] = t
    amount = (kwargs['value'] + kwargs.get("extra_value", 0)) *(-1 if kwargs.get("type","+") == "-" else 1)
    kwargs['amount'] = (kwargs['value'] + kwargs.get("extra_value", 0))
    obj = await TIpOrders.filter(user_id=kwargs['user_id'], status=1).order_by('-orderid').limit(1).first().values(
        "total_money", 'balance')
    Tools.log.debug(f"obj: {obj}")
    if not obj:
        obj = {}
    total_money = obj.get('total_money',0)
    balance = obj.get('balance',0)
    kwargs['balance'] = balance + amount
    kwargs['total_money'] = total_money + amount
    kwargs['token_id'] = kwargs.get("token_id",0)
    await TIpOrders.create(**kwargs)
    if update_user_info:
        if kwargs["status"] == 1:
            balance = kwargs['balance']
        await UserInfo.filter(uid=kwargs['user_id']).update(balance=balance, total_balance=total_money,
                                                            ex_balance=total_money - balance, modified_on=t,)
        try:
            # 放后台执行删除缓存
            asyncio.create_task(Tools.redis_14.delete(f"CACHE_USER_{kwargs['user_id']}"))
            asyncio.create_task(Tools.redis_us.delete(f"CACHE_USER_{kwargs['user_id']}"))
        except:
            pass
        if balance == 0:
            is_indebted = 1 
        else :
            is_indebted = 0
        await TUser.filter(uid=kwargs['user_id']).update(is_indebted = is_indebted  )
        

    Tools.log.debug(f"生成了一个记录: {kwargs}")
    return order_id


async def add_email_blacklist(email, delete_remark="", delete_remark_en="", **kwargs) -> int:
    """
    拉黑用户
    """
    now = int(time())
    eb_obj = await TEmailBlacklists.filter(email=email).first()
    is_first = False
    if eb_obj:

        await TEmailBlacklists.filter(id=eb_obj.id).update(modified_on=now, note=delete_remark,deleted_on=0,
                                                           en_note=delete_remark_en)

    else:
        await TEmailBlacklists.create(email=email, created_on=now, modified_on=now, note=delete_remark,deleted_on=0,
                                      en_note=delete_remark_en)
        is_first = True

    uid = kwargs.get('uid', 0)
    if not uid:
        await TUser.filter(email=email).update(is_use=False)
    else:
        await TUser.filter(uid=int(uid)).update(is_use=False)
    return is_first


async def get_new_questionnaire(is_gpt = False,lang='zh'):
    conn = Tortoise.get_connection("default")
    platform = 'ai302' if is_gpt else 'proxy302'
    if lang.startswith('zh'):
        lang = 'zh'
    elif lang.startswith('ja') or lang.startswith('jp'):
        lang = 'ja'
    else:
        lang = 'en'
    record = await conn.execute_query_dict(
        f"select * from myAdmin_wj where status = 1 and platform='{platform}' and lang='{lang}'  order by id desc  limit 1")
    return record

async def get_new_questionnaire_each_lang(is_gpt = False):
    conn = Tortoise.get_connection("default")
    platform = 'ai302' if is_gpt else 'proxy302'
    zh_record = await conn.execute_query_dict(
        f"select * from myAdmin_wj where status = 1 and platform='{platform}'  and (lang='zh' or lang='') order by id desc  limit 1")
    en_record = await conn.execute_query_dict(
        f"select * from myAdmin_wj where status = 1 and platform='{platform}'  and lang='en' order by id desc  limit 1")
    ja_record = await conn.execute_query_dict(
        f"select * from myAdmin_wj where status = 1 and platform='{platform}'  and lang='ja' order by id desc  limit 1")
    ru_record = await conn.execute_query_dict(
        f"select * from myAdmin_wj where status = 1 and platform='{platform}'  and lang='ru' order by id desc  limit 1")
    ids = [0,0,0,0]
    if zh_record:
        ids[0] = zh_record[0].get("id")
    if en_record:
        ids[1] = en_record[0].get("id")
    if ja_record:
        ids[2] = ja_record[0].get("id")
    if ru_record:
        ids[3] = ru_record[0].get("id")
    return ids




async def get_question_status_by_id(uid,is_gpt=False):
    record = await get_new_questionnaire(is_gpt=is_gpt)
    status = True
    if record:
        conn = Tortoise.get_connection("default")
        res = await conn.execute_query_dict("select 1 from myAdmin_submit where uid=%s and status = 0 and wjId=%s",
                                            [uid, record[0].get("id")])
        if res:
            status = False
    return status




async def get_questionnaire_by_id(uid,is_test=False,is_gpt=False):
    """
    获取问卷链接
    """

    record = await get_new_questionnaire_each_lang(is_gpt=is_gpt)
    if is_test:
        url = "https://consult-api.proxy302.com/"
    else:
        url = f"https://consult.proxy302.com/"
    data = {}
    if record:
        for index,value in enumerate(record):
            if not value: continue
            if index == 0:
                data["url"] = url + f"display/{value}/{uid}"
                data["url_cn"] = url + f"display/{value}/{uid}"
            if index == 1:
                data["url_en"] = url + f"display/{value}/{uid}"
            if index == 2:
                data["url_ja"] = url + f"display/{value}/{uid}"
            if index == 3:
                data["url_ru"] = url + f"display/{value}/{uid}"
    
    return data

async def check_user_not_gift(uid):
    """
    检查用户是否是赠送的
    """

    return not await TIpOrders.filter(user_id=uid, status__gt=0,payway='gift').exists()

async def check_user_is_submit(uid, set_status=False,is_gpt=False,lang='zh'):
    try:
        conn = Tortoise.get_connection("default")
        record = await get_new_questionnaire(is_gpt=is_gpt,lang=lang)
        if not record:
            return False
        wj_id = record[0].get("id")
        key = f"check_user_is_submit_{uid}_{wj_id}"
        data = await Tools.redis.get(key)
        if data in (b"1", "1"):
            return False
        if set_status:
            await Tools.redis.set(key, b"1")
        # res = await conn.execute_query_dict(
        #     "select 1 from myAdmin_submit where uid=%s and status = 0 and wjid=%s limit 1",
        #     [uid, wj_id])
        res = await conn.execute_query_dict(
            "select 1 from myAdmin_submit where uid=%s  limit 1",
            [uid])
        if record[0].get("show_windows") == 1:
            if res:
                return False
            else:
                query = record[0].get('query')
                if not query:
                    return True
                _ = await conn.execute_query_dict(query,[uid])
                Tools.log.debug(_)
                if not _:
                    return False
                return True

    except:
        return False



async def log_event(value,log_type_id,uid):
    await TUserLog.create(log_type_id=log_type_id,uid=uid,msg=value)



async def  get_forward_domain_func():
    conn = Tortoise.get_connection("default")
    record = await conn.execute_query_dict("select domain from t_proxy_domain where status = 1 order by RAND() LIMIT 10")
    if record:
        return {"domain_list":[i.get("domain") for i in record]}
    return {"domain_list":[]}


async def send_traffic_alarm(lang="zh",is_gpt=False):
    async def send_email(lang,is_gpt,email,traffic_used):
        Tools.email.send_traffic_alarm_email(email,is_gpt,lang,traffic_used=traffic_used)

    async def send_sms(mobile,is_gpt=False):
        accId = Tools.config.rly_conf.get("acc_id")
        accToken = Tools.config.rly_conf.get("acc_token")
        app_name = "app_id" if not is_gpt else 'gpt_app_id'
        appId = Tools.config.rly_conf.get(app_name)
        tid = 3015405

        sdk = SmsSDK(accId, accToken, appId)
        resp = sdk.sendMessage(tid, mobile, ())
        Tools.log.info(f"send sms to {mobile},resp:{resp}")
    from tortoise.functions import Sum
    from tortoise.expressions import F
    now = current_timestamp()
    for record in await TTrafficAlarm.filter(enable=1).all():
        key = f"TRAFFIC_ALARM_{record.uid}"
        if value := await Tools.redis.get(key):
            if int(value) >= 3:
                continue
        await Tools.redis.incr(key)
        
        total_traffic = await TUserTrafficPool.filter(uid=record.uid).annotate(total_traffic=Sum(F("total_traffic")),cost = Sum(F("current_cost_traffic"))).group_by("uid").first().values("uid","total_traffic","cost")
        cost = total_traffic.get("cost",0)
        total_traffic = total_traffic.get("total_traffic",0)
        traffic_used = (total_traffic-cost)/1024/1024/1024
        if traffic_used <= record.traffic:
            if record.type == "email":
                await send_email(lang,is_gpt,record.alarm_value,traffic_used)
            else:
                await send_sms(record.alarm_value.replace(" ",""),is_gpt)

async def send_balance_msg():
    async def send_sms(mobile,is_gpt):
        accId = Tools.config.rly_conf.get("acc_id")
        accToken = Tools.config.rly_conf.get("acc_token")
        app_name = "app_id" if not is_gpt else 'gpt_app_id'
        tid_name = "proxy_balance_tid" if not is_gpt else 'gpt_balance_tid'
        appId = Tools.config.rly_conf.get(app_name)
        tid = Tools.config.rly_conf.get(tid_name)

        sdk = SmsSDK(accId, accToken, appId)
        resp = sdk.sendMessage(tid, mobile, ())
        Tools.log.info(f"send sms to {mobile},resp:{resp}")

    now = current_timestamp()
    for record in await TBalanceAlarm.filter(next_time__lte=now,enable_alarm=True).all():
        phone_or_email = record.alarm_value
        if record.alarm_balance <=0:
            continue
        is_gpt = record.is_gpt
        lang = record.lang
        _ = await UserInfo.filter(uid=record.uid).first().values("balance")
        if not _:continue
        balance = _.get("balance")
        key = f"BALANCE_ALARM_{record.uid}_{is_gpt}"
        if await TIpOrders.filter(created_on__gt =record.next_time-3600*2, created_on__lt=current_timestamp(), user_id=record.uid,payway__startswith='charge_',status__gte=1).exists():
            await Tools.redis.delete(key)
        if balance/1000 > record.alarm_balance:
            record.next_time = now + 3600*2
            await record.save()
            continue
        try:
            value = await Tools.redis.get(key)
            if value and (int(value.decode()))>=3:
                continue
            if record.alarm_type == 'email':
                Tools.email.send_alarm_email(phone_or_email,is_gpt,lang,balance=balance)
            else:
                await send_sms(phone_or_email.replace(" ",""),is_gpt)
            record.next_time = now +3600*2
            await record.save()
            await Tools.redis.incr(key)

        except Exception as e:
            Tools.log.error(e)

# 转义字符映射表
replacements = {
    '\\n': '\n',  # 换行
    '\\t': '\t',  # 制表符
    '\\r': '\r',  # 回车
}

# 正则表达式匹配单引号和双引号内的内容，以及外部内容
def replace_outside_quotes(input_string):
    def replace_function(match):
        if match.group(1) or match.group(2):  # 捕获单引号或双引号内的内容
            return match.group(0)  # 保持原样
        else:
            # 对字符串外的部分，按映射表替换转义字符
            result = match.group(0)
            for key, value in replacements.items():
                result = result.replace(key, value)
            return result
    
    # 正则表达式同时处理单引号和双引号
    # "([^"\\]*(?:\\.[^"\\]*)*)" 捕获双引号内的字符串
    # '([^'\\]*(?:\\.[^'\\]*)*)' 捕获单引号内的字符串
    # ([^"'\\]+) 捕获不在引号内的其他部分
    pattern = re.compile(r'"([^"\\]*(?:\\.[^"\\]*)*)"|\'([^\'\\]*(?:\\.[^\'\\]*)*)\'|([^"\'\\]+)')
    return pattern.sub(replace_function, input_string)


async def run_code_func(code,lang,token_value=""):
    code = replace_outside_quotes(code)
    token = await TToken.filter(value=token_value).first()
    if not token:
        raise ComstomException(detail="token is not found")
    json_data = {
        "language": lang,
        "code": code,
        "preload": "",
        "enable_network": True
    }
    headers = {"X-Api-Key": "dify-sandbox"}
    async with aiohttp.ClientSession() as session:
        async with session.post("http://10.0.1.61:8194/v1/sandbox/run", json=json_data, headers=headers) as response:
            json_data = await response.json()
            print(json_data)
            return json_data.get("data",{})
    # res = requests.post("http://127.0.0.1:8193/v1/sandbox/run", data=json_data, headers=headers)


# 验证域名格式
def is_valid_domain(domain: str) -> bool:
    pattern = r"^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$"
    return re.match(pattern, domain) is not None

# 检查域名可访问性
def check_domain_accessibility(domain: str) -> bool:
    try:
        response = requests.get(f"https://{domain}", verify=False, timeout=5)
        return response.status_code == 200
    except requests.RequestException:
        return False