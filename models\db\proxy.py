import time
from typing import Optional

from httpx._transports import default
from tortoise.contrib.pydantic import pydantic_model_creator
from tortoise.fields import NO_ACTION
from tortoise.models import Model
from tortoise import fields, BaseDBAsyncClient
from tortoise import functions

"""
"""


class Base(Model):
    class Meta:
        abstract = True

    created_on = fields.IntField(null=True, description="创建时间", default=time.time)
    modified_on = fields.IntField(null=True, description="修改时间", default=time.time)
    deleted_on: int = fields.IntField(null=True, description="删除时间", default=0)

    def to_dict(self):
        return self.__dict__


"""
CREATE TABLE `proxy`.`t_balance_alarm`  (
  `id` int NOT NULL,
  `uid` int NULL,
  `deleted_on` int NULL,
  `created_on` int NULL,
  `alarm_type` int NULL COMMENT '0 邮箱 1 电话',
  `alarm_value` text NULL,
  `modified_on` int NULL,
  `status` int NULL COMMENT '0 还没开始 1进行中 2 完成',
  `next_time` int NULL,
  `threshold` float NULL,
  PRIMARY KEY (`id`)
);

"""
class TBalanceAlarm(Base):
    class Meta:
        table="t_balance_alarm"
        indexes = [
            {"fields": ["uid"]},
        ]
    uid: int = fields.IntField(description="用户ID")
    alarm_type: str = fields.CharField(max_length=512,default="email")
    enable_alarm: int = fields.IntField(description="是否启用",default=0)
    is_gpt: bool = fields.BooleanField(description="生成记录的平台是gpt",default=False)
    alarm_value: str = fields.CharField(max_length=128, description="报警值")
    status: int = fields.IntField(description="0 还没开始 1进行中 2 完成")
    next_time: int = fields.IntField(description="下次执行时间")
    alarm_balance: float = fields.FloatField(description="阈值")
    lang:str = fields.CharField(max_length=8, description="语言",default="zh")

class TOrderInfo(Base):
    class Meta:
        table= "t_orders_info"
    id:int = fields.IntField(pk=True, description="订单ID")
    pay_order: str = fields.CharField(max_length=128, description="订单号",default="")
    amount: float = fields.FloatField(description="金额",default=0.0)
    currency_type: str = fields.CharField(max_length=8, description="货币类型",default="")
    funding: str = fields.CharField(max_length=32, description="支付方式",default="")
    name: str = fields.CharField(max_length=128, description="收件人",default="")
    email : str = fields.CharField(max_length=128, description="收件人邮箱",default="")
    address1: str = fields.CharField(max_length=255, description="收件人地址",default="")
    address2: str = fields.CharField(max_length=255, description="收件人地址",default="")
    city: str = fields.CharField(max_length=64, description="收件人城市",default="")
    state: str = fields.CharField(max_length=64, description="收件人州",default="")
    country: str = fields.CharField(max_length=64, description="收件人国家",default="")


class TPayways(Base):
    class Meta:
        table="t_payways"

    id:int = fields.IntField(pk=True, description="订单ID   ！这个表设计上有点缺陷，orderid用的是bigint类型  输出后可能和原来的值不一样")
    payway: str = fields.CharField(max_length=128, description="订单类型  t_ip_orders.payway")
    order_num: int = fields.IntField(description="订单号")
    type: str = fields.CharField(max_length=11, description="类型 [+充值 -扣费]", default="")
    pay_value: int = fields.IntField(description="对应值(除以1000=PTC)")
    extra_value: int = fields.IntField(description="赠送值(除以1000=PTC)")
    note: str = fields.CharField(max_length=32, description="中文内容")
    en_note: str = fields.CharField(max_length=128, description="英文内容")
    price: float = fields.FloatField(description="价格")
    currency: str = fields.CharField(max_length=128, description="货币类型 ")
    tries_limit: int = fields.IntField(description="每个用户可使用此订单次数     -1:不限制")
    service_fee: float = fields.FloatField(description="服务费金额")
    vm_level_id: int = fields.IntField(description=" t_vm_levels.id 远程服务对应的虚拟机配置ID")
    days: int = fields.IntField(description="按天购买的代理 套餐对应的天数")

"""
CREATE TABLE `t_ref_blacklists` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_on` int(11) DEFAULT NULL,
  `deleted_on` int(11) DEFAULT NULL,
  `ref` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""

class TRefBlacklists(Model):
    class Meta:
        table="t_ref_blacklists"
    id:int = fields.IntField(pk=True, description="ID")
    created_on: int = fields.IntField(description="创建时间",default=time.time)
    deleted_on: int = fields.IntField(description="删除时间",default=0)
    ref: str = fields.CharField(max_length=255, description="来源")

class TAccessories(Model):
    class Meta:
        table="t_accessory"
    id:int = fields.IntField(pk=True, description="附件ID")
    create_time: int = fields.IntField(description="创建时间",default=time.time)
    update_time: int = fields.IntField(description="更新时间",default=time.time)
    title: str = fields.CharField(max_length=255, description="标题")
    en_title: str = fields.CharField(max_length=255, description="标题")
    support: str = fields.CharField(max_length=255, description="支持")
    detail: str = fields.CharField(max_length=255, description="详情")
    en_detail: str = fields.CharField(max_length=255, description="详情")
    download: str = fields.CharField(max_length=255, description="下载")
    download_times: int = fields.IntField(description="下载次数")
    label: str = fields.CharField(max_length=255, description="标签")
    en_label: str = fields.CharField(max_length=255, description="标签")
    type: str = fields.CharField(max_length=255, description="类型")
    version: str = fields.CharField(max_length=255, description="版本号")
    deleted_on: int = fields.IntField(description="删除时间",default=0)



class TVmLevel(Base):
    class Meta:
        table="t_vm_levels"
    id:int = fields.IntField(pk=True, description="虚拟机配置ID")
    name: str = fields.CharField(max_length=64, description="虚拟机配置名称")
    en_name: str = fields.CharField(max_length=64, description="虚拟机配置名称")
    size: str = fields.CharField(max_length=32, description="虚拟机配置大小")
    cpu: str = fields.CharField(max_length=16, description="虚拟机配置cpu")
    ram: str = fields.CharField(max_length=16, description="虚拟机配置ram")
    storage: str = fields.CharField(max_length=16, description="虚拟机配置storage")
    ext_disk_gb: int = fields.IntField(description="虚拟机配置ext_disk_gb")



class TApiToken(Base):
    class Meta:
        table="t_api_tokens"
    id:int = fields.IntField(pk=True, description="账号ID")
    user: int = fields.ForeignKeyField("models.TUser", source_field="uid", related_name="api_tokens",
                                       description="t_users.id 用户ID", db_constraint=False, on_delete=NO_ACTION)
    name: str = fields.CharField(max_length=64, description="用户名")
    password: str = fields.CharField(max_length=128, description="密码")

class TEmailBlacklists(Model):
    class Meta:
        table="t_email_blacklists"
    id = fields.IntField(pk=True, description="黑名单ID")
    email: str = fields.CharField(max_length=64, description="邮箱")
    created_on: int = fields.IntField(description="创建时间",default=time.time)
    modified_on: int = fields.IntField(description="修改时间",default=time.time)
    deleted_on: int = fields.IntField(description="删除时间",default=0)
    note: str = fields.CharField(max_length=255, description="备注",default="")
    en_note: str = fields.CharField(max_length=255, description="备注",default="")


class TStripe(Model):
    class Meta:
        table="t_stripe_data"
    id = fields.IntField(pk=True, description="stripeID")
    orderid: int = fields.IntField(description="账单ID",default=0)
    status :int = fields.IntField(description="状态 0 发起支付 1 回调支付",default=0)
    payment_id :str= fields.CharField(description="支付ID",max_length=255,default="")
    data:dict= fields.JSONField(description="支付数据",default='{}')

class TProxyLine(Model):
    class Meta:
        table = "t_proxy_line"
    id = fields.IntField(pk=True, description="代理线路ID")
    token_id: int = fields.IntField(description="t_user_tokens.id")
    s: str = fields.CharField(max_length=255, description="代理线路")

class TIpOrders(Base):
    class Meta:
        table = "t_ip_orders"

    orderid: int = fields.IntField(pk=True,
                                   description="账单ID   ！这个表设计上有点缺陷，orderid用的是bigint类型  输出后可能和原来的值不一样")
    user: int = fields.ForeignKeyField("models.TUser", source_field="uid", related_name="ip_orders",
                                       description="t_users.id 用户ID", db_constraint=False, on_delete=NO_ACTION)
    type: str = fields.CharField(max_length=11, description="类型 [+充值 -扣费]", default="")
    currency_type: str = fields.CharField(max_length=11, description="充值使用的货币类型   内部充值：IT", default="")
    currency: int = fields.IntField(description="充值金额 ",default=0)
    receive_currency: int = fields.IntField(description="加上手续费充值金额",default=0)
    payway: str = fields.CharField(max_length=128, description="t_payways.payway 账单类型",default='')
    pay_order: str = fields.CharField(max_length=32, description="第三方平台的订单号",default='')
    value: int = fields.IntField(description="充值值 (除以1000=PTC)",default=0)
    extra_value: int = fields.IntField(description="赠送充值值 (除以1000=PTC)",default=0)
    amount: int = fields.IntField(description="实际充值值 = value + extra_value  (除以1000=PTC)",default=0)
    balance: int = fields.IntField(description="此时用户的余额 (除以1000=PTC)",default=0)
    total_money: int = fields.IntField(description="总的增加的值",default=0)
    status: int = fields.IntField(description="状态(0无效或者尚未生效  1成功  2成功但是隐藏不给用户看到)",default=0)
    checksum: int = fields.IntField(description="是否自校验（基本用不到",default=0)
    valid: int = fields.IntField(description="是否有效（0无效 1有效   用于充值回调后回来设置成1）",default=0)
    is_inner: int = fields.IntField(description="是否内部操作",default=0)
    token = fields.ForeignKeyField("models.TUserToken", source_field="ut_id", related_name="ip_orders",
                                   description="对应退款时候的订单和token", db_constraint=False, on_delete=NO_ACTION)
    # proxy = fields.ForeignKeyField("models.TProxyIp", source_field="pi_id", related_name="user_tokens",
    #                                description="代理信息", db_constraint=False, on_delete=NO_ACTION)
    text: str = fields.CharField(max_length=128, description="备注",default="")
    traffic_usage: int = fields.IntField(description="流量代理扣费对应的流量",default=0)
    pay_method: int = fields.IntField(description="充值方式  对应 constants.py -> PAY_METHODS",default=0)


"""
CREATE TABLE `t_user_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_on` int(11) NOT NULL DEFAULT '0',
  `modified_on` int(11) NOT NULL DEFAULT '0',
  `deleted_on` int(11) NOT NULL DEFAULT '0',
  `uid` int(11) NOT NULL COMMENT 't_users.id   用户id',
  `log_type_id` int(11) NOT NULL COMMENT 't_log_types.id  操作类型',
  `msg` varchar(2550) NOT NULL COMMENT '操作信息',
  `ip_id` int(11) NOT NULL DEFAULT '0' COMMENT 't_ips.id  操作时客户端IP',
  `client_ip` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '操作时客户端IP  字符串类型IP',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_log_type_id` (`log_type_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1811246 DEFAULT CHARSET=latin1 COMMENT='用户操作记录表';
"""

class TUserLog(Base):
    class Meta:
        table="t_user_logs"
    id:int = fields.IntField(pk=True, description="日志ID")
    uid: int = fields.IntField(description="用户ID",default=0)
    log_type_id: int = fields.IntField(description="日志类型",default=0)
    msg: str = fields.CharField(max_length=2550, description="日志信息",default='')
    ip_id: int = fields.IntField(description="客户端IP",default=0)
    client_ip: str = fields.CharField(max_length=128, description="客户端IP",default='')


class TInviteOrders(Base):
    class Meta:
        table="t_invite_orders"
    id:int = fields.IntField(pk=True, description="订单ID")
    order_id: str = fields.CharField(max_length=128, description="订单号")
    order_num: int = fields.IntField(description="订单号")
    charge_value: float = fields.FloatField(description="充值金额")
    inv_uid: int = fields.IntField(description="邀请人")
    uid: int = fields.IntField(description="用户ID")
    charge_num: int = fields.IntField(description="充值次数")
    charge_order_id: int = fields.IntField(description="充值订单号")
    payway: str = fields.CharField(max_length=128, description="支付方式")
    status: int = fields.IntField(description="状态")
    value: float = fields.FloatField(description="充值金额")
    balance: float = fields.FloatField(description="余额")
    amount: float = fields.FloatField(description="实际充值金额")
    other: str = fields.CharField(max_length=512, description="备注")

"""
CREATE TABLE `t_country_filter` (
  `id` int(11) NOT NULL,
  `country_id` int(11) DEFAULT NULL COMMENT '国家id',
  `type` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""


class TCountryFilter(Model):
    class Meta:
        table="t_country_filter"
    id: int = fields.IntField(pk=True, description="ID")
    country_id: int = fields.IntField(description="国家ID")
    type: str = fields.CharField(max_length=255, description="类型")

class TVmLocations(Base):
    class Meta:
        table="t_vm_locations"
    id: int = fields.IntField(pk=True, description="ID")
    country_id: int = fields.IntField(description="国家ID")
    location: str = fields.CharField(max_length=128, description="地区")
    quota: int = fields.IntField(description="配额")


class TInviteUserSettings(Base):
    class Meta:
        table ="t_invite_user_settings"
    id: int = fields.IntField(pk=True, description="ID")
    inv_uid: int = fields.IntField(description="邀请人")
    inviter_rebate: float = fields.FloatField(description="邀请人返利")
    invitee_max_num: int = fields.IntField(description="最大邀请人数")
    invitee_max_days: int = fields.IntField(description="最大邀请天数")
    withdrawal_min_acc: float = fields.FloatField(description="提现最小金额")
    withdrawal_months_num: int = fields.IntField(description="提现月数")

"""
CREATE TABLE `t_invite_ref` (
  `id` int(11) NOT NULL,
  `ref` varchar(255) DEFAULT NULL,
  `create_on` int(11) DEFAULT NULL,
  `uid` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`),
  KEY `ref` (`ref`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='ref 来源日志';
"""
class TInviteRef(Model):
    class Meta:
        table="t_invite_ref"
    id: int = fields.IntField(pk=True, description="ID")
    ref: str = fields.CharField(max_length=255, description="来源")
    create_on: int = fields.IntField(description="创建时间",default=time.time)
    uid: int = fields.IntField(description="用户ID")

class UserInfo(Base):
    class Meta:
        table = "t_users_info"

    id: int = fields.IntField(pk=True, description="用户ID")
    uid: int = fields.IntField(description="用户ID")
    balance: int = fields.IntField(description="仅供参考的用户余额  (除以1000=PTC)", default=0)
    ex_balance: float = fields.FloatField(description="使用的余额总额 (除以1000=PTC)", default=0)
    total_balance: float = fields.FloatField(description="充值的的余额总额 (除以1000=PTC)", default=0)
    invite_code: str = fields.CharField(max_length=32, description="邀请码 （接入第三方返现平台后只做旧版本兼容 ）",
                                        default="")
    invite_for: int = fields.IntField(description="邀请自t_users.id 用户id （接入第三方返现平台后只做旧版本兼容 ）",
                                      default=0)
    it_invite_for: int = fields.IntField(description="邀请自内部邀请码 t_internal_invitation_codes.id", default=0)
    reward_invite_for: int = fields.IntField(description="邀请制返现邀请码 (接入第三方返现平台后弃用)", default=0)
    ref: str = fields.CharField(max_length=255, description="来源", default="")
    apply_win_vm: bool = fields.BooleanField(default=False,
                                             description="是否允许使用远程机器（后不需要申请就可以使用后弃用）")
    apply_paypal: bool = fields.BooleanField(default=False, description="是否运行PayPal支付（PayPal收款账号被封后弃用）")
    from_ps_ref: str = fields.CharField(max_length=64, description="邀请自第三方邀请平台的邀请码", default="")
    is_new_user: bool = fields.BooleanField(max_length=64, description="是否新用户", default=True)
    gpt_request_times:int = fields.IntField(default=0,description="gpt请求次数")
    gpt_cost:int = fields.IntField(default=0,description="gpt花销")
    region:int = fields.IntField(default=1,description="地域 0 中国大陆  1 海外 ")


"""
CREATE TABLE `t_user_stripe` (
  `id` int(11) NOT NULL,
  `uid` int(11) DEFAULT NULL,
  `is_gpt` tinyint(4) DEFAULT NULL COMMENT '是否为gpt',
  `stripe_customer_id` varchar(255) DEFAULT NULL,
  `default_payment_method` varchar(255) DEFAULT NULL,
  `balance` float DEFAULT NULL COMMENT '低于这个值的情况下自动充值',
  `amount` float DEFAULT NULL COMMENT '充值的金额，默认20',
  PRIMARY KEY (`id`),
  KEY `uid` (`uid`,`is_gpt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用于绑定stripe的信息表，可以做自动充值';
"""

class TUserStripe(Model):
    class Meta:
        table="t_user_stripe"
    id: int = fields.IntField(pk=True, description="ID")
    uid: int = fields.IntField(description="用户ID",)
    is_gpt: int = fields.IntField(description="是否为gpt",default=0)
    stripe_customer_id: str = fields.CharField(max_length=255, description="stripe_customer_id",default='')
    default_payment_method: str = fields.CharField(max_length=255, description="default_payment_method",default='')
    balance: float = fields.FloatField(description="低于这个值的情况下自动充值",default=0)
    amount: float = fields.FloatField(description="充值的金额 默认20",default=5)
    enable: int = fields.IntField(description="是否启用",default=0)
    display_brand = fields.CharField(max_length=255, description="显示品牌",default="")
    last4:str = fields.CharField(max_length=255, description="最后4位",default="")



class TPayway(Base):
    class Meta:
        table = "t_payways"
    id = fields.IntField(pk=True, description="ID")
    payway = fields.CharField(max_length=128, default="")
    order_num = fields.IntField(default=0)
    type = fields.CharField(max_length=128, default='')
    pay_value = fields.IntField(default=0)
    extra_value = fields.IntField(default=0)
    note = fields.CharField(max_length=128, default="")
    en_note = fields.CharField(max_length=128, default="")
    jp_note = fields.CharField(max_length=128, default="")
    ru_note = fields.CharField(max_length=128, default="")
    price = fields.FloatField(default=0)
    currency = fields.CharField(128, default="")
    tries_limit = fields.IntField(default=0)
    service_fee = fields.FloatField(default=0)
    vm_level_id = fields.IntField(default=0)

    days = fields.IntField(default=0)
    traffic_pool_config =fields.ForeignKeyField("models.TTrafficPoolConfig",related_name="traffic_pool_config",db_constraint=False,on_delete=NO_ACTION)
    tag :str = fields.CharField(max_length=128, default="")
"""
CREATE TABLE `t_traffic_alarm` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_on` int(11) DEFAULT NULL,
  `modified_on` int(11) DEFAULT NULL,
  `deleted_on` int(11) DEFAULT NULL,
  `traffic_pool_id` int(11) DEFAULT NULL,
  `uid` int(11) DEFAULT NULL,
  `traffic` bigint(20) DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户流量包报警';
"""
class TTrafficAlarm(Base):
    class Meta:
        table="t_traffic_alarm"
    id: int = fields.IntField(pk=True, description="ID")
    uid: int = fields.IntField(description="用户ID")
    traffic: int = fields.IntField(description="流量")
    enable: int = fields.IntField(description="是否启用")
    type: str = fields.CharField(max_length=128, description="类型")
    alarm_value: str = fields.CharField(max_length=128, description="通知报警的具体值")

class TTrafficPoolLog(Base):
    class Meta:
        table="t_traffic_pool_log"
    id: int = fields.IntField(pk=True, description="ID")
    traffic_pool_id: int = fields.IntField(description="流量池ID")
    uid: int = fields.IntField(description="用户ID")
    ip_order_id: int = fields.IntField(description="IP订单ID")
    traffic: int = fields.BigIntField(description="流量")
    token_id: int = fields.IntField(description="tokenID")

class TUserTrafficPool(Base):
    class Meta:
        table="t_user_traffic_pool"
    id: int = fields.IntField(pk=True, description="ID")
    uid: int = fields.IntField(description="用户ID")
    status: int = fields.IntField(description="状态")
    current_cost_traffic: int = fields.IntField(description="当前消耗的流量")
    total_traffic: int = fields.IntField(description="总流量")
    pay_way: int = fields.ForeignKeyField("models.TPayway",related_name="pay_way",db_constraint=False,on_delete=NO_ACTION)
    traffic_pool_config_id: int = fields.IntField(description="流量池配置ID")

class TTrafficPoolConfig(Base):
    class Meta:
        table="t_traffic_pool_config"
    id: int = fields.IntField(pk=True, description="ID")
    name: str = fields.CharField(max_length=255, description="控制线路")
    en_name: str = fields.CharField(max_length=255, description="控制线路英文")
    origin_price: float = fields.FloatField(description="原价")


class TGeoCity(Base):
    class Meta:
        table = "t_geo_city"

    id: int = fields.IntField(pk=True, description="城市ID")
    state_id: int = fields.IntField(description="t_geo_state.id   城市对应的洲的id")
    name: str = fields.CharField(max_length=128, description="城市名")


class TGeoCountry(Base):
    class Meta:
        table = "t_geo_country"

    id: int = fields.IntField(pk=True, description="国家ID")
    name: str = fields.CharField(max_length=64, description="国家名")
    code: str = fields.CharField(max_length=8, description="国家缩写")


class TGeoState(Base):
    class Meta:
        table = "t_geo_state"

    id: int = fields.IntField(pk=True, description="洲ID")
    country_id: int = fields.IntField(description="t_geo_country.id  洲对应的国家的id")
    name: str = fields.CharField(max_length=128, description="洲名")
    code: str = fields.CharField(max_length=8, description="缩写")


class TIps(Base):
    class Meta:
        table = "t_ips"

    id: int = fields.IntField(pk=True, description="IP ID")
    ip_addr: int = fields.IntField(description="十进制存储")
    ip_address: str = fields.CharField(max_length=32, description="十六进制存储")
    ip :str =  fields.CharField(max_length=32, description="字符串")


class TProxyHost(Model):
    class Meta:
        table = "t_proxy_host"
    id: int = fields.IntField(pk=True, description="ID")
    host: str = fields.CharField(max_length=255, description="主机名")
    enable: bool = fields.BooleanField(default=True, description="是否启用")
    name: str = fields.CharField(max_length=255, description="主机名称")

class TUserToken(Base):
    class Meta:
        table = "t_user_tokens"

    id: int = fields.IntField(pk=True, description="用户token ID")
    user: int = fields.ForeignKeyField("models.TUser", source_field="uid",
                                       description="t_users.id 用户ID", related_name="user_tokens",
                                       db_constraint=False, on_delete=NO_ACTION)
    username: str = fields.CharField(max_length=32, description="token用户名")
    passwd: str = fields.CharField(max_length=32, description="token密码")
    pi_id: int = fields.IntField(null=True, description="t_proxy_ips.id 转发代理", )
    network: str = fields.CharField(max_length=16, description="协议类型 （http socks5=sockset）")
    url: str = fields.CharField(max_length=255,
                                description="代理host url（正式服务器的proxy.proxy302.com  测试服务器的test-proxy.proxy302.com）")
    # port: int = fields.IntField(null=True, description="代理端口")
    requests: int = fields.IntField(null=True, description="原打算记录911请求次数 但是没用")
    traffic: int = fields.IntField(null=True, description="原打算记录911流量 但没用")
    expired: bool = fields.BooleanField(default=False, description="是否已经过期（1已过期 0正常）")
    refund: bool = fields.BooleanField(default=False, description="是否已经退款（1已退款 0正常）")
    is_more_sell: bool = fields.BooleanField(default=False, description="是否多买 已经没用这个字段")
    is_static: bool = fields.BooleanField(default=False, description="是否静态代理")
    life_time: int = fields.IntField(null=True, description="ip代理？存活时间")
    req_usage_amount: int = fields.IntField(null=True, description="request流量 !真实流量没有乘以倍数")
    resp_usage_amount: int = fields.IntField(null=True, description="response流量 ！真实流量没有乘以倍数")
    traffic_usage: int = fields.IntField(null=True,
                                         description="记录使用的流量，！乘以倍数后的流量，用于给用户查看使用流量")
    cost: int = fields.IntField(null=True, description="对应消耗了余额值(除以1000=PTC)")
    data_center: int = fields.IntField(null=True, description="数据中心")
    remark: str = fields.CharField(max_length=168, description="用户备注")
    payway_id: int = fields.IntField(null=True, description="t_payways.id 用于购买远程机器自动续费检查时对应套餐")
    is_api: int = fields.IntField(default=0, description="是不是API代理(通用代理，不是开发者api)（2不知道  1是 0不是）")
    is_deleted: bool = fields.BooleanField(default=False, description="是否用户删除（1删除 0正常）")
    delete_remark: str = fields.CharField(max_length=255, description="记录删除信息")
    ps_id: int = fields.IntField(null=True, description="t_proxy_suppliers.id 用于快速对应供应商")
    proxy = fields.ForeignKeyField("models.TProxyIp", source_field="pi_id", related_name="user_tokens",
                                   description="代理信息", db_constraint=False, on_delete=NO_ACTION)
    la = fields.ForeignKeyField("models.TLuminatiAccount", source_field="la_id", related_name="user_tokens",
                                description="流量代理信息", db_constraint=False, on_delete=NO_ACTION)

    class Config:
        table_description = "用户代理token信息"


"""
CREATE TABLE `t_iproyal_renew_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `created_on` int(11) DEFAULT '0',
  `modified_on` int(11) DEFAULT '0',
  `deleted_on` int(11) DEFAULT '0',
  `order_id` int(11) DEFAULT NULL,
  `resp` json DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
"""

class TIpRoyalRenewLog(Base):
    class Meta:
        table = "t_iproyal_renew_log"
    id: int = fields.IntField(pk=True, description="ID")
    order_id: int = fields.IntField(description="订单ID")
    resp: dict = fields.JSONField(description="响应数据")


class TUserTrafficIp(Model):
    class Meta:
        table = "t_user_traffic_ips"
    uid:int=fields.IntField(null=True, description="用户id")
    ti_id:int=fields.IntField(null=True, description="t_traffic_ip 对应id")
    pi_id:int=fields.IntField(null=True, description="t_proxy_ips.id 转发代理")
    created_on:int=fields.IntField(null=True, description="创建时间",default=0)
    deleted_on:int=fields.IntField(null=True, description="删除时间",default=0)

class TProxyUrlGroup(Base):
    class Meta:
        table = "t_proxy_url_group"
    id = fields.IntField(pk=True, description="id")
    remark: str = fields.CharField(max_length=255, description="备注")
    url: str = fields.CharField(max_length=255, description="域名")
    group: str = fields.CharField(max_length=255, description="分组")
    token: int = fields.ForeignKeyField("models.TUserToken", related_name="proxy_url_groups",)
    uid: int = fields.IntField(null=True, description="用户id")
    ads_url: str = fields.CharField(max_length=255, null=True, description="广告域名")
    ip: str = fields.CharField(max_length=255, null=True, description="ip")
    proxy_area:str = fields.CharField(max_length=255,null=True,description="代理地区")
    proxy_created_on:int = fields.IntField(null=True, description="代理的创建时间",default=0)
    username:str = fields.CharField(max_length=255,null=True,description="用户名",default='')
    # is_static:int =fields.IntField(null=True, description="静态",default=1)
    # is_traffic:int =fields.IntField(null=True, description="流量",default=1)

class TTrafficIps(Model):
    class Meta:
        table = "t_traffic_ips"
    id = fields.IntField(pk=True, description="id")
    la_id: int = fields.IntField(description="对应的账号id")
    country_id: int = fields.IntField(description="国家id")
    state_id: int = fields.IntField(description="州id")
    city_id: int = fields.IntField(description="城市id")
    is_health: bool = fields.BooleanField(default=True, description="是否健康")
    last_check_time: int = fields.IntField(null=True, description="最后检查时间",default=time.time)
    check_proxy: str = fields.CharField(max_length=255, null=True, description="用来检查的url")
    username: str = fields.CharField(source_field="_username",max_length=255, null=True, description="用户名")
    password: str = fields.CharField(source_field="_password",max_length=255, null=True, description="密码")
    host: str = fields.CharField(source_field="_host",max_length=255, null=True, description="主机")
    port: int = fields.IntField(source_field="_port",null=True, description="端口")
    ip: str = fields.CharField(max_length=255, null=True, description="IP")
    ip_int: int = fields.IntField(null=True, description="IP int")
    ip_hex: str = fields.CharField(max_length=255, null=True, description="IP hex")
    created_on: int = fields.IntField(description="创建时间",default=time.time)
    deleted_on: int = fields.IntField(description="创建时间",default=0)
    network_type: str = fields.CharField(max_length=255, null=True, description="网络类型")
class TUserEvent(Model):
    class Meta:
        table = "t_user_events"
    id = fields.IntField(pk=True, description="id")
    uid: int = fields.IntField(description="用户id")
    name: str = fields.CharField(max_length=255, description="用户名")
    email: str = fields.CharField(max_length=255, description="邮箱")
    created_on: int = fields.IntField(description="创建时间")
    job: str = fields.CharField(max_length=255, description="职业")
    ref: str = fields.CharField(max_length=255, description="来源")


class TStaticIpRenew(Model):
    class Meta:
        table = "t_static_ip_renew"
    id = fields.IntField(pk=True, description="id")
    order_id: int = fields.IntField(description="外部订单id")
    uid: int = fields.IntField(description="用户id")
    token_id: int = fields.IntField(description="t_user_tokens.id")
    t_ip_order_id: int = fields.IntField(description="t_ip_orders对应的id")
    created_on: int = fields.IntField(description="创建时间")
    is_auto_renew: bool = fields.BooleanField(default=False, description="是否自动续费，表示这一次充值用户的自动续费状态")
    created_by: int = fields.IntField(description="谁创建的订单，区分是用户手动充值还是系统自动充值，系统为0, -1是用来转换状态的一个记录")
    pay_way_id: int = fields.IntField(description="支付订单套餐")

class TLuminatiAccount(Base):
    class Meta:
        table = "t_luminati_accounts"

    id: int = fields.IntField(pk=True, description="账号ID")
    username: str = fields.CharField(max_length=128, description="用户名")
    password: str = fields.CharField(max_length=128, description="密码")
    only_country: bool = fields.BooleanField(default=False, description="只支持国家 （0可以精确到城市  1只能精确到国家）")
    is_static: bool = fields.BooleanField(default=False, description="是否静态（0动态  1静态）")
    is_data_center: bool = fields.BooleanField(default=False, description="是否机房（0住宅  1机房）")
    pm_id: int = fields.IntField(null=True, description="t_proxy_machines.id  账号使用的机器的ID")
    req_usage_amount: int = fields.IntField(default=0,
                                            description="这个账号request使用的流量  （考虑一直写表影响性能 没有使用这个字段）")
    resp_usage_amount: int = fields.IntField(default=0,
                                             description="这个账号response使用的流量 （考虑一直写表影响性能 没有使用这个字段）")
    is_oxylabs: bool = fields.BooleanField(default=False, description="是不是oxylabs账号  （0:luminati 1:oxylabs）")
    has_special_area: bool = fields.BooleanField(default=False, description="是否有特殊地区的ip")
    user_tokens: TUserToken = None

"""
CREATE TABLE `t_dynamic_ip_pool` (
  `id` bigint(20) NOT NULL,
  `deleted_on` int(11) DEFAULT NULL,
  `created_on` int(11) DEFAULT NULL,
  `checked_on` int(11) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL COMMENT '主机',
  `port` int(11) DEFAULT NULL COMMENT '端口',
  `ip` varchar(255) DEFAULT NULL COMMENT '真实的ip代理地址',
  `expired` blob COMMENT '是够过期',
  `country_id` int(11) DEFAULT NULL COMMENT '国家id',
  `state_id` int(11) DEFAULT NULL,
  `city_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='动态流量池表 记录当前有效的代理记录，过期及时标记失效';
"""


class TDynamicIpPool(Base):
    class Meta:
        table = "t_dynamic_ip_pool"

    id: int = fields.IntField(pk=True, description="动态IP池ID")
    checked_on: int = fields.IntField(description="上次检测时间",default=0)
    host: str = fields.CharField(description="主机",max_length=255)
    port: int = fields.IntField(description="端口",default=0)
    ip: str = fields.CharField(description="真实的ip代理地址",max_length=255)
    country_id: int = fields.IntField(description="国家id",default=0)
    state_id: int = fields.IntField(description="省id",default=0)
    city_id: int = fields.IntField(description="市id",default=0)
    pi = fields.ForeignKeyField("models.TProxyIp", source_field="pi_id", related_name="pool",
                              description="代理信息", db_constraint=False, on_delete=NO_ACTION)
    pm_id:int = fields.IntField(description="pmid",default=0)
    ip_id:int = fields.IntField(description="ipid",default=0)
    status:int = fields.IntField(description="状态",default=0)

class  TIproyalLocation(Base):
    class Meta:
        table =  "t_iproyal_loacations"

    id: int = fields.IntField(pk=True, description="地区ID")
    proxy_type: int = fields.IntField(description="代理类型（目前只用它的静态代理proxy_type=1, locationid&proxy_type需要唯")
    location_id: int = fields.IntField(description="Iproyal地区ID")
    country_id: int = fields.IntField(description="国家ID")
    ceiling: int = fields.IntField(description="购买上限")


class  TIproyalOrder(Base):
    class Meta:
        table = "t_iproyal_orders"

    id: int = fields.IntField(pk=True, description="订单ID")
    order_id: int = fields.IntField(description="Iproyal的订单ID")
    status: int = fields.IntField(description="状态（0生产中  1生成成功）")
    location_id: int = fields.IntField(description="Iproyal地区ID")
    pi_id: int = fields.IntField(description="t_proxy_ips.id  代理对应id")
    pay_order_id: int = fields.IntField(description="t_ip_orders.id  对应我们内部订单的ID")
    traffic_id:int = fields.IntField(description="t_traffic_orders.id  对应我们内部订单的ID")
    expired: int = fields.IntField(description="过期时间")
    expired_on:str = fields.CharField(max_length=255,description="过期时间")

class TProxyIp(Base):
    class Meta:
        table = "t_proxy_ips"

    id: int = fields.IntField(pk=True, description="代理ID")
    username: str = fields.CharField(max_length=128, description="代理用户名",default="")
    password: str = fields.CharField(max_length=128, description="代理密码",default="")
    pm_id: int = fields.IntField(null=True, description="t_proxy_machines.id  代理服务器id")
    forward_port: int = fields.IntField(null=True, description="转发代理代理端口")
    port: int = fields.IntField(null=True, description="转发的代理端口(这个字段已经用不到了)",default=0)
    # ip_id: int = fields.IntField(null=True, description="t_ips.id 代理的IP （！不是代理的host的IP）")
    source: str = fields.CharField(max_length=128,
                                   description="代理来源 （=t_proxy_suppliers.name 此处不关联只用作快速筛选)",default="")
    online: int = fields.IntField(null=True, description="代理是否存活，不会恢复（0已死  1存活）")
    status: int = fields.IntField(null=True, description="代理状态 （0不可用 1可用  2隐藏）")
    health: int = fields.IntField(null=True, description="代理是否健康，不健康有可能恢复健康 （0不健康  1健康）")
    # country_id: int = fields.IntField(null=True, description="t_geo_country.id  国家id   (0不知道地区，需要脚本获取地区信息 -1不需要显示地区或者获取地区失败  下同)")
    # state_id: int = fields.IntField(null=True, description="t_geo_state.id")
    # city_id: int = fields.IntField(null=True, description="t_geo_city.id")
    user_tokens: TUserToken = None
    ip = fields.ForeignKeyField("models.TIps", source_field="ip_id", related_name="proxys", description="代理的IP",
                                db_constraint=False, on_delete=NO_ACTION)
    country = fields.ForeignKeyField("models.TGeoCountry", source_field="country_id", related_name="proxys",
                                     description="代理的国家", db_constraint=False, on_delete=NO_ACTION,default=0)
    state = fields.ForeignKeyField("models.TGeoState", source_field="state_id", related_name="proxys",
                                   description="代理的省份", db_constraint=False, on_delete=NO_ACTION,default=0)
    city = fields.ForeignKeyField("models.TGeoCity", source_field="city_id", related_name="proxys",
                                  description="代理的城市", db_constraint=False, on_delete=NO_ACTION,default=0)


class TUser(Base):
    class Meta:
        table = "t_users"

    uid: int = fields.IntField(pk=True, description="用户ID")
    name: str = fields.CharField(max_length=32, description="用户名")
    password: str = fields.CharField(max_length=64, description="用户密码 加密后")
    email: str = fields.CharField(max_length=128, description="用户邮箱")
    salt: str = fields.CharField(max_length=64, description="加密用的盐")
    super_sell: bool = fields.BooleanField(default=True, description="此用户是否启用多买（这个字段已经不用了")
    register_ip_id: int = fields.IntField(description="t_ips.id用户注册时客户端IP", default=0)
    register_ip: str = fields.CharField(max_length=128, description="字符串类型用户注册时客户端IP")
    apply_use_api: bool = fields.BooleanField(default=False, description="是否正在申请使用开发者api")
    use_api: bool = fields.BooleanField(default=False, description="是否允许使用开发者api")
    free_911: bool = fields.BooleanField(default=True, description="此用户是否启用免费911代理（这个字段已经不用了")
    subid: str = fields.CharField(max_length=32, description="来源", default="")
    could_get_special_areas: bool = fields.BooleanField(default=False,
                                                        description="能获取到特殊地区的ip ，对应 t_proxy_special_area.special_type\r\n0的情况下是不可获取")
    is_use: bool = fields.BooleanField(default=True,
                                       description="账号是否正常使用 默认1 正常，0为不可用，现在用来控制流量转发的开关")
    token: str = fields.CharField(max_length=255, description="用户接入api使用的token", default="")
    is_indebted:bool = fields.BooleanField(default=False, description="是否是欠费用户")
    is_gpt:bool = fields.BooleanField(default=False, description="是否gpt用户")
    phone:str = fields.CharField(max_length=255, description="用户手机号", default="")
    user_id:str = fields.CharField(max_length=255, description="用户手机号", default="")
    register_from:str=fields.CharField(max_length=255, description="什么方式注册 google or phone or pwd", default="pwd")
    lang:str=fields.CharField(max_length=255, description="注册的时候对应的lang", default="zh-CN")
    resource_area:int = fields.IntField(description="t_ips.id用户注册时客户端IP", default=1)

"""
CREATE TABLE `t_refund_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` int(10) NOT NULL DEFAULT '0',
  `refund_amount` float NOT NULL DEFAULT '0',
  `created_on` int(10) NOT NULL DEFAULT '0',
  `modified_on` int(10) NOT NULL DEFAULT '0',
  `deleted_on` int(10) NOT NULL DEFAULT '0',
  `log_id` int(10) NOT NULL DEFAULT '0',
  `refund_source` varchar(255) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  `refund_source_log_id` int(10) NOT NULL DEFAULT '0',
  `status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '# 0 未处理 1 已处理',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
"""




class TTokenTag(Model):
    class Meta:
        table = "t_token_tag"
    id = fields.IntField(pk=True, description="id")
    token_id = fields.IntField(description="t_token.id",default=0)
    is_deleted = fields.IntField(description="是否删除",default=0)
    deleted_on = fields.IntField(description="删除时间",default=0)
    created_on = fields.IntField(description="创建时间",default=time.time)
    type = fields.CharField(max_length=255, description="标签类型", default="")
    uid:int = fields.IntField(description="用户id",default=0)


class TProxyTtl(Model):
    class Meta:
        table = "t_proxy_ttl"
    id = fields.IntField(pk=True, description="id")
    pi_id = fields.IntField(description="t_proxy_suppliers.id",default=0)
    expire_date = fields.DatetimeField(description="第三方过期时间")
    use_date = fields.DatetimeField(description="用户使用最大日期",auto_now=True)
    oracle_host = fields.CharField(max_length=168, description="域名",default="")

class TProxyMachines(Base):
    class Meta:
        table = "t_proxy_machines"
    id = fields.IntField(pk=True, description="id")
    ip_id = fields.IntField(description="t_ips.id",default=0)
    domain = fields.CharField(max_length=168, description="域名",default="")
    ps_id = fields.IntField(description="ps_id",default=0)
    is_linux = fields.BooleanField(description="是否是linux",default=True)
    forward_port_start = fields.IntField(description="转发端口起始",default=0)
    forward_port_end = fields.IntField(description="转发端口结束",default=0)
    fault = fields.BooleanField(description="是否故障",default=False)
    public_ip = fields.CharField(max_length=128, description="公网ip",default="")
    vm_name = fields.CharField(max_length=128, description="虚拟机名称",default="")


Project_Pydantic = pydantic_model_creator(TUser, name="TUser", exclude=("password", "salt", "register_ip_id", "token"))


"""
CREATE TABLE `t_app_version` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `package` varchar(100) NOT NULL COMMENT 'APP包名',
  `platform` varchar(20) NOT NULL COMMENT '平台(android/ios)',
  `version_name` varchar(20) NOT NULL COMMENT '版本名称(如1.0.0)',
  `version_code` int(11) NOT NULL COMMENT '版本号(内部版本号)',
  `download_url` varchar(255) NOT NULL COMMENT '下载地址',
  `file_url` varchar(255) COMMENT '安装包文件地址',
  `file_size` bigint(20) COMMENT '安装包大小(单位:字节)',
  `file_md5` varchar(32) COMMENT '文件MD5值',
  `is_must` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否强制更新(0否1是)',
  `depict` text   COMMENT '更新描述',
  `publish_on` bigint(20) NOT NULL DEFAULT 0 COMMENT '发布时间',
  PRIMARY KEY (`id`),
  KEY `idx_platform_package` (`platform`,`package`),
  KEY `idx_version_code` (`version_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP版本控制表';
"""
class TAppVersion(Base):
    class Meta:
        table = "t_app_version"
    id = fields.IntField(pk=True, description="主键ID")
    package = fields.CharField(max_length=100, description="APP包名")
    platform = fields.CharField(max_length=20, description="平台(android/ios)")
    version_name = fields.CharField(max_length=20, description="版本名称(如1.0.0)")
    version_code = fields.IntField(description="版本号(内部版本号)")
    download_url = fields.CharField(max_length=255, description="下载地址")
    file_size = fields.IntField(description="安装包大小(单位:字节)",default=0)
    file_md5 = fields.CharField(max_length=32, description="文件MD5值",default="")
    is_must = fields.IntField(description="是否强制更新(0否1是)",default=0)
    depict = fields.TextField(description="更新描述")
    publish_on = fields.IntField(description="发布时间",default=0)

"""
CREATE TABLE `t_app_version_control` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `platform` varchar(20) NOT NULL,
  `min_version_id` int(11) NOT NULL,
  `recommend_version_id` int(11) NOT NULL,
  `zh_force_update_desc` text,
  `zh_recommend_update_desc` text,
  `created_on` int(11) NOT NULL DEFAULT '0',
  `modified_on` int(11) NOT NULL DEFAULT '0',
  `deleted_on` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='APP版本控制表';
"""

class TAppVersionControl(Base):
    class Meta:
        table = "t_app_version_control"
    id = fields.IntField(pk=True, description="主键ID")
    platform = fields.CharField(max_length=20, description="平台(android/ios)")
    min_version_id = fields.IntField(description="最小版本号",default=0)
    recommend_version_id = fields.IntField(description="推荐版本号",default=0)
    zh_force_update_desc = fields.TextField(description="强制更新描述")
    zh_recommend_update_desc = fields.TextField(description="推荐更新描述")
