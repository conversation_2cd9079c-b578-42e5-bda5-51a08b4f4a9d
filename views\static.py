# -*- coding: utf-8 -*-
# @Time    : 2023/10/18 16:18
# <AUTHOR> hzx1994
# @File    : static.py
# @Software: PyCharm
# @description:
import base64
import datetime

from _decimal import Decimal
from fastapi import FastAPI, APIRouter
from starlette.requests import Request
from starlette.responses import HTMLResponse
from starlette.staticfiles import StaticFiles
from starlette.templating import Jinja2Templates
from tortoise import Tortoise

from conf import constants
from utils import is_from_gpt

app = APIRouter()




templates = Jinja2Templates(directory="templates")


@app.get("/items/{id}")
async def read_item(request: Request, id: str):
    return templates.TemplateResponse("item.html", {"request": request, "id": id})


async def get_order_info(order_id:str) ->dict :
    conn = Tortoise.get_connection("default")
    order = await conn.execute_query("""SELECT tu.uid, CAST(orderid AS CHAR) as orderid, tio.created_on, tu.name as user_name, if(tu.email>"",tu.email,tu.phone) as user_email, text,ut_id,amount,
        tio.currency, pay_order, pay_method, value, tio.extra_value, note, en_note, price, service_fee,tio.value /1000 as tmp_value, tio.value / 1000 +service_fee as receive_currency FROM t_ip_orders tio 
        LEFT JOIN t_users tu on tio.uid = tu.uid 
        LEFT JOIN t_payways tp on tio.payway = tp.payway
        WHERE orderid = %s AND status > 0 AND valid = 1 AND tio.deleted_on = 0 limit 1""", order_id)
    if order:
        return order[1][0]
    else:
        return {}

async def get_order_info_by_py_order(pay_order:str):
    conn = Tortoise.get_connection("default")
    order = await conn.execute_query("""
        SELECT * FROM t_orders_info WHERE deleted_on = 0 AND pay_order = %s limit 1
    """, pay_order)
    if order:
        if order[0] >=1:
            return order[1][0]
        else:
            return {}
    else:
        return {}

@app.get("/bill/zh/{order_id}")
@app.get("/bill/en/{order_id}")
async def read_item(request: Request, order_id: str,f:str='ai'):
    print(str(request.url.path))
    text = {
        "账单": ("账单", 'INVOICE'),
        "账单编号": ("账单编号", 'Order ID'),
        "已付款": ("已付款", 'completed'),
        "用户": ("用户", 'User Info'),
        "支付给": ("支付给", 'Pay To'),
        "账单日期": ("账单日期", 'Date'),
        "付款方式": ("付款方式", 'Payment Method'),
        "开票给": ("开票给", 'Bill To'),
        "账单项目": ("账单项目", 'Bill Item'),
        "描述": ("描述", 'Describe'),
        "金额": ("金额", 'Amount'),
        "手续费": ("手续费", 'Service charge'),
        "小计": ("小计", 'Subtotal'),
        "总计": ("总计", 'Total'),
        "交易": ("交易", 'Trade'),
        "交易日期": ("交易日期", 'Date'),
        "交易编号": ("交易编号", 'Transaction Number'),
        "手续费": ("手续费", 'Fee'),
    }
    if str(request.url.path).startswith("/static/bill/zh"):
        text = {k:v[0] for k,v in text.items()}
        note_key = "note"
    else:
        text = {k:v[1] for k,v in text.items()}
        note_key = "en_note"
    order_id = base64.b64decode(order_id).decode("utf-8")
    order = await get_order_info(order_id=order_id)
    price = order.get('currency',0)  or int(order.get('tmp_value',0)) or order.get("price",0) or 0
    service_fee = order.get('service_fee',0) or 0
    data = {
        'order_id': order.get('orderid',""),
        'order_created_on': order.get('created_on',""),
        'pay_date': order.get('created_on',""),
        'user_name': order.get('user_name',""),
        'user_email': order.get('user_email',""),
        'pay_name': order.get('user_name',""),
        'pay_email': order.get('user_email',""),
        'pay_order': order.get('pay_order',''),
        'order_note': order.get(note_key,""),
        'price': "{:.2f}".format(price),
        'service_fee': "{:.2f}".format(service_fee),
        'currency': "{:.2f}".format(order.get("receive_currency")),
        # "{:.2f}".format(order['currency']),
        'extra_value': order['extra_value'],
        'pay_price': "{:.2f}".format(order.get("receive_currency")),
        # "{:.2f}".format(order['receive_currency']),
        'pay_method': constants.PAY_METHODS.get(order.get('pay_method'), "-"),
        'funding': constants.PAY_METHODS.get(order.get('pay_method'), ""),
        'address1':'-',
        'address2':'-',
        'country':'-',
        'state':'-',
        'city':'-',
        "request": request,
        "order_service_fee": "{:.2f} * 5% + 1".format(order['tmp_value']),    
    }

    if order.get("ut_id"):
        data['currency'] = order['amount'] /1000
        price = order['amount'] /1000
        data['order_note'] += f"(id:{order['ut_id']})"
    data.update(text)
    if order['text']:
        data['pay_order'] += f"({order['text']})"
    order_info = await get_order_info_by_py_order(order['pay_order'])
    if order_info:
        data.update({
            'pay_date': order_info['created_on'],
            'pay_name': order_info['name'],
            'pay_email': order_info['email'],
            'pay_price': "{:.2f}".format(order_info['amount']),
            'funding': order_info['funding'],
            'address1': order_info.get("address1",'-') if order_info.get("address1",'-') else '-',
            'address2': order_info.get("address2",'-') if order_info.get("address2",'-') else "-",
            'country': order_info.get("country",'-') if order_info.get("country",'-') else '-',
            'state': order_info.get("state",'-') if order_info.get("state",'-') else '-',
            'city': order_info.get("city",'-') if order_info.get("city",'-') else '-',

        })
    if data['order_note'] == "自动续费":
        data['order_service_fee'] = "0"
    data['order_price'] = price
    # data['order_service_fee'] = service_fee
    data['order_subtotal'] = data['currency'] 
    data['order_total'] = data['currency'] 
    data['pay_total'] = data['currency'] 
    data['pay_date'] = data['order_created_on'] = datetime.datetime.fromtimestamp(data['pay_date'])
    print(data)



    if f == 'proxy302':
        return templates.TemplateResponse("bill-proxy302.html", data)
    else:
        return templates.TemplateResponse("bill-302ai.html", data)