# -*- coding: utf-8 -*-
# @Time    : 2023/9/26 11:31
# <AUTHOR> hzx1994
# @File    : api.py
# @Software: PyCharm
# @description:
from PIL import Image
import hashlib
from collections import Counter

from fastapi.params import Header
from sse_starlette import EventSourceResponse
import mimetypes
import os.path
from urllib.parse import urlparse
from uuid import uuid4
import aiofiles
from fastapi.responses import HTMLResponse,FileResponse
from fastapi import APIRouter, Depends, Query, File, UploadFile, Form, Body, Path, BackgroundTasks
from starlette.requests import Request
from controllers.gpt import copy_gpt_token_func, create_user_gpt_token, del_user_gpt_token, get_user_gpt_token_list, update_user_gpt_token, \
    get_gpt_logs, enable_or_disable_gpt, crontab_to_deduction_gpt, get_log_summary, get_gpt_detail, get_value_from_code, \
    get_model_list, crontab_to_check_limit_daily_cost, crontab_to_update_limit_daily_cost, upload, set_values, \
    create_user_gpts_token, search_gpts_by_code, update_img, upload_blob, get_gpt_tools_list, create_tool_for_user, \
    get_tool_value_from_code, upload_from_url, get_file_extension, upload_chat_record_func, get_chat_log, get_model_ids, \
    integration_func, get_tool_models_list, check_service_health, create_or_get_user_aibox, \
    crontab_to_check_limit_hour_cost, crontab_to_update_limit_hour_cost, get_model_price_fnc, change_user_indebted, \
    build_share_signature, get_tool_list, get_text_summary, create_or_update_tool, get_news_func, get_or_set_tool_conf, \
    get_share_code_summary_func, limit_api_key_func, get_api_key_limit_func, requst_log, \
    get_or_create_default_api_key_fuc, get_apis_list, get_models_apis_by_brand_id, add_service_at_sort_order, update_service_at_service_id, delete_service_at_service_id, get_categories_list, add_category, update_category_at_category_id, delete_category_at_category_id, get_brands_list, add_brand, update_brand_at_brand_id, delete_brand_at_brand_id, crontab_to_check_limit_monthly_cost, crontab_to_update_limit_monthly_cost, get_models_param_list, add_model_param, update_model_param_at_model_id, delete_model_param_at_model_id, get_models_param_at_id, sync_parameters, update_tool_models, get_robot_models, get_custom_models, create_custom_model, get_custom_model, update_custom_model, delete_custom_model
from controllers.gpt import get_region_form_db
from controllers.knowledge import *

from controllers.pubilc import test_user_action
from controllers.user import get_current_active_user, get_client_ip, get_user_from_api_key, get_current_admin
from models.api import ServiceRequest, CategoryRequest, CategoryResponse, BrandRequest, BrandResponse, ModelParamBase, ToolBase, ToolCategoryResponse, ToolCategoryCreate, ToolCategoryUpdate, CustomModelBase, CustomModelCreate, CustomModelResponse, CustomModelUpdate
from models.gpt import GptToken, LimitQuery, TimeQuery, APPName, Locale, LocaleGPT, ApiKeyLimit,ChatCompletionRequest,ChatCompletionResponse,ChatMessage,ChatCompletionResponseChoice,ChatCompletionResponseStreamChoice,DeltaMessage,KTasks
from models.response import suc_data, fail_data, BaseData
from models.user import UserOut
from utils import Tools, ip2int, get_extension_from_content_type, get_region
from libs.meta_chunking import lumberchunker
from controllers.file_uploader import FileUploader
app = APIRouter()
gpt = APIRouter()
kb = APIRouter()


async def process_files_in_background(
    files, kb_id, override, to_vector_store, chunk_size,
    chunk_overlap, zh_title_enhance, uid, split_mode,
    separator, max_chunk_length, is_summarize, task_id,
    encoding, doc2x, language, dynamic_merge, model_name
):
    """后台处理大量文件上传的任务"""
    try:
        Tools.log.info(f"开始后台处理 {len(files)} 个文件，任务ID: {task_id}")

        # 可以将进度存储到Redis中，供前端查询
        await Tools.redis.hset(f"file_upload_task:{task_id}", "status", "processing")
        await Tools.redis.hset(f"file_upload_task:{task_id}", "total", len(files))
        await Tools.redis.hset(f"file_upload_task:{task_id}", "completed", 0)

        # 限制并发处理的文件数量
        MAX_CONCURRENT = 3
        sem = asyncio.Semaphore(MAX_CONCURRENT)
        completed_count = 0

        async def process_file_with_progress(index, file):
            nonlocal completed_count
            async with sem:
                try:
                    # 这里应该调用实际的文件处理函数
                    # 注意：这里需要根据实际的create_knowledge_base_upload_files_func实现来调整
                    async for resp in create_knowledge_base_upload_files_func(
                        [file], kb_id, override, to_vector_store, chunk_size,
                        chunk_overlap, zh_title_enhance, uid, split_mode=split_mode,
                        separator=separator, max_chunk_length=max_chunk_length,
                        is_summarize=is_summarize, task_id=task_id, encoding=encoding,
                        doc2x=doc2x, background_tasks=None,  # 后台任务中不再嵌套后台任务
                        language=language, dynamic_merge=dynamic_merge, model_name=model_name
                    ):
                        pass  # 处理响应

                    completed_count += 1
                    await Tools.redis.hset(f"file_upload_task:{task_id}", "completed", completed_count)
                    Tools.log.info(f"文件 {file.filename} 处理完成 ({completed_count}/{len(files)})")

                except Exception as e:
                    Tools.log.error(f"处理文件 {file.filename} 时出错: {e}")
                    await Tools.redis.hset(f"file_upload_task:{task_id}", "error", str(e))

        # 并发处理所有文件
        tasks = [process_file_with_progress(i, file) for i, file in enumerate(files)]
        await asyncio.gather(*tasks, return_exceptions=True)

        # 标记任务完成
        await Tools.redis.hset(f"file_upload_task:{task_id}", "status", "completed")
        await Tools.redis.expire(f"file_upload_task:{task_id}", 3600)  # 1小时后过期

        Tools.log.info(f"后台任务 {task_id} 处理完成")

    except Exception as e:
        Tools.log.error(f"后台任务 {task_id} 处理失败: {e}")
        await Tools.redis.hset(f"file_upload_task:{task_id}", "status", "failed")
        await Tools.redis.hset(f"file_upload_task:{task_id}", "error", str(e))


@app.post("/crontab/cost/user/gpt", summary="定时任务，统计用户的gpt代理消耗情况")
async def build_job():
    await crontab_to_deduction_gpt()


@app.post("/crontab/check/service/health", summary="定时任务，检查用户的服务健康状态")
async def build_job():
    await check_service_health()


@app.post("/crontab/check/user/limit_daily_cost",
          summary="定时任务，每分钟一次，检查用户的每日gpt代理消耗,更新对应token的每日消耗")
async def check_limit_daily_cost():
    await crontab_to_check_limit_hour_cost()
    await crontab_to_check_limit_daily_cost()
    await crontab_to_check_limit_monthly_cost()
    return suc_data()


@app.post("/crontab/update/user/limit_hour_cost", summary="定时任务，每分钟一次，更新用户的每小时gpt代理消耗限制状态")
async def change_limit_daily_cost():
    await crontab_to_update_limit_hour_cost()
    await change_user_indebted()
    return suc_data()


@app.post("/crontab/update/user/limit_daily_cost", summary="定时任务，每10分钟一次，更新用户的每日gpt代理消耗限制状态")
async def change_limit_daily_cost():
    await crontab_to_update_limit_daily_cost()
    return suc_data()

@app.post("/crontab/update/user/limit_monthly_cost", summary="定时任务，每10分钟一次，更新用户的每月gpt代理消耗限制状态")
async def change_limit_monthly_cost():
    await crontab_to_update_limit_monthly_cost()
    return suc_data()

@app.post("/settings/{token_id}", summary="保存用户gpt代理的token的设置配置，包括标题和对应的的模型配置等等")
async def set_settings(token_id: int, values: dict = Body(...)):
    if await set_values(token_id, values=values):
        return suc_data()
    else:
        return fail_data()

@app.get("/v1/code",summary="获取默认的全能工具箱api-key")
async def get_default_api_key(uid_b64:str=Query("",title="用户uid的base64编码"),Uid:str=Header("",title="用户uid的base64编码"),Lang:str=Header('cn',description="语言",title="语言") ):
    return suc_data(data=await get_or_create_default_api_key_fuc(uid_b64 or Uid,lang=Lang))


@app.get("/request/log",summary= "获取请求日志")
async def get_requst_log(token_id: int,page_size: int=10,page:int=1):
    return  suc_data(data=await requst_log(token_id,page_size=page_size,page=page))

@app.get("/v1/tool/config/{share_code}",summary="获取工具自定义配置")
async def get_tool_config(user = Depends(get_current_active_user),share_code:str=Path(...,title="分享码")):
    return await get_or_set_tool_conf(share_code)

@app.put("/v1/tool/config/{share_code}",summary="自定义配置工具的属性")
async def set_tool_config(user = Depends(get_current_active_user),share_code: str = Path(...,title="分享码"),
                          values:dict = Body(...,title="要修改的配置"),current_version:str=Body(...,summary= "当前配置版本号")):
    return await get_or_set_tool_conf(share_code,values,current_version=current_version,check_version=True)

@app.delete("/v1/tool/config/{share_code}",summary="删除工具自定义配置")
async def get_tool_config(user = Depends(get_current_active_user),share_code:str=Path(...,title="分享码")):
    return await get_or_set_tool_conf(share_code,delete=True)



@app.get("/update/imgs")
async def update_imgs():
    await update_img()
    return suc_data()


@app.get("/news",summary="获取文章咨询")
def get_news(top_n:int=5):

    records = get_news_func(top_n=top_n)
    return suc_data(data={"records":records})

@app.post("/to_search", summary="工具超市搜索接口，转发到工具超市那边的接口")
async def to_search(request:Request):
    body = await request.json()
    # body = json.loads(body.decode('gbk'))
    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.post(f"https://api-academic.tools302.com/api/v1/search", json=body) as resp:
            return await resp.json()

@app.post("/upload/gpt/image", summary="上传gpt图片")
@app.post("/upload/gpts/image", summary="上传gpts图片")
async def upload_gpt_image(requests: Request, file: UploadFile = File(None), prefix=Form("imgs"),
                         need_compress: bool = Form(False, description="是否需要压缩图片，只支持jpg，png格式，png最后会转换jpg存储"),
                           url: str = Form('', title="url")):
    @utils.cache(ttl=10*60)
    async def get_area_flag(authorizations):
        try:
            token = authorizations.split(" ")[-1]
            user_dict = await get_user_from_api_key(token)
            uid = user_dict.get("uid")
            u = await TUser.filter(uid=uid).first().values("resource_area")
            return u
        except:
            ...
    s = prefix.split("/")
    # 如果没有日期后缀，添加今天的日期，格式为YYYYMMDD
    if len(s) == 1 or not s[-1].isdigit() or len(s[-1]) != 8:
        today = datetime.datetime.now().strftime("%Y%m%d")
        prefix = f"{prefix}/{today}"

    uploader = FileUploader(prefix=prefix)
    result = await uploader.process_upload(file=file, url=url, need_compress=need_compress)
    return suc_data(data=result)

@app.post("/tools/list", summary="获取工具集列表")
async def get_tools_list():
    return suc_data(data={"data": await get_gpt_tools_list()})


@app.get("/tools/{tool_id}", summary="获取对应用户的工具详情，没有就帮忙创建")
async def create_tool(tool_id: int, user: UserOut = Depends(get_current_active_user)):
    data = await create_tool_for_user(user.uid, tool_id)
    return suc_data(data={"data": data})


@gpt.get("/v1/{code}", summary="工具市场获取gpt的api_key")
async def get_values_from_code(request: Request, code, pwd="", is_zh: bool = Query(False,deprecated=True),lang=Header('',alias="Lang",title="语言",description="cn,jp,en")):
    # todo code为 abcd-search
    url = request.headers.get("Referer")
    if not lang:
        l = request.headers.get("Accept-Language")
        if l.startswith("zh") or l.startswith("cn"):
            lang = 'cn'
        elif l.startswith("jp") or l.startswith("ja"):
            lang = 'jp'
        else:
            lang = 'en'
    if url:
        u = urlparse(url)
        domain = u.netloc.split(".", 1)[-1]
    else:
        domain = ''
    split_array = code.split("-", maxsplit=1)
    if len(split_array) == 1:
        project_name = ''
        code = split_array[0]
    else:
        code, project_name = split_array
    data = await get_tool_value_from_code(code, pwd, lang=lang, pre=project_name, domain=domain)
    return data


@gpt.get("/{code}")
async def get_share_value(request: Request, code, pwd="",uid:str='',lang:str=Header('en', alias="Lang",title="语言")):
    ua = request.headers.get("user-agent")
    # lang = request.headers.get("Lang",'en')
    ip = await get_client_ip(request)
    url = request.headers.get("Referer") or request.headers.get("referer") or request.headers.get(
        "origin") or request.headers.get("Origin")
    project_name = ""
    if url:
        u = urlparse(url)
        domain = u.netloc.split(".", 1)[-1]
        path_code = u.netloc.split(".", 1)[0].split("-",1)
        if len(path_code) == 2:
            project_name = path_code[1]
        else:
            project_name = ''
    else:
        domain = ''

    split_array = code.split("-", maxsplit=1)
    if len(split_array) == 1:
        code = split_array[0]
    else:
        code, _ = split_array
        project_name = project_name or _

    if lang.startswith("zh") or lang.startswith("cn"):
        lang = 'cn'
    elif lang.startswith("jp") or lang.startswith("ja"):
        lang = 'jp'
    else:
        lang = 'en'

    value = await get_value_from_code(code, pwd,uid=uid, lang=lang, pre=project_name, domain=domain)
    if isinstance(value, int):
        return fail_data(code=f"{value}", msg='error')
    return suc_data(data=value)


@app.get("/models/ids", summary="返回需要模型下拉列表的ids")
async def get_ids_model(user: UserOut = Depends(get_current_active_user)):
    return suc_data(data=await get_model_ids())


@app.get("/models", summary="获取模型列表")
@kb.get("/knowledge_base/models", summary="获取模型列表")
async def get_models(request: Request,use_in='chatbot', tool_id=Query(0, description="工具id"),
                     user: UserOut = Depends(get_current_active_user)):
    # use_in: mj 绘画
    refer_host = request.headers.get("referer") or request.headers.get("origin") or ''
    # if "/docs" in refer_host:
    #     return

    data = await get_model_list(use_in, tool_id=tool_id, uid=user.uid)
    region = await get_region_form_db(user.uid)
    if region == 0:
        filter_name = ['OpenAI模型','Anthropic模型','Google模型','Midjourney']
        data["data"] = [i for i in data["data"] if i['model_type'] not in filter_name]
    return suc_data(data=data)


@app.get("/models/robot", summary="获取聊天机器人模型列表")
async def get_models():
    return suc_data(data=await get_robot_models())


@app.get("/token/tool/models", summary="获取工具对应的模型")
async def get_tool_models(user: UserOut = Depends(get_current_active_user)):
    data = await get_tool_models_list(user.uid)
    return suc_data(data={"tools": data})
@app.post("/api_key/limit", summary="对api进行限制")
async def limit_api_key(body: ApiKeyLimit, user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    data = await limit_api_key_func(body,uid=user.uid)
    return data

@app.get("/api_key/{token_id}",summary="获取api限制的配置")
async def get_api_key_limit(token_id: int, user: UserOut = Depends(get_current_active_user)):
    data = await get_api_key_limit_func(token_id,user.uid)
    return suc_data(data=data)


@app.post('/token/aibox', summary="创建或者获取用户的工具箱")
async def create_aibox(user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    data = await create_or_get_user_aibox(user.uid)
    return suc_data(data=data)


@app.post("/token", summary="生成GPT Token")
async def create_token(body: GptToken, user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    data = await create_user_gpt_token(user.uid, body=body)
    return suc_data(data={"data": data})


# @app.post("/tool_box/token/{tool_id}", summary="工具箱")
# async def create_token(body: GptToken, user: UserOut = Depends(get_current_active_user),tool_id:int=Path(...)):
#     if resp := await test_user_action(user):
#         return resp
#     body.tool_id = -99
#     body.is_robot=1
#     data = await create_user_gpt_token(user.uid, body=body,tool_id=tool_id)
#     return suc_data(data={"data":data})


@app.post("/zero_shot/token", summary="生成竞技场 Token")
async def create_token(body: GptToken, user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
    body.tool_id = 7
    body.is_robot = 1
    data = await create_user_gpt_token(user.uid, body=body)
    return suc_data(data={"data": data})


@app.post("/gpts/token", summary="生成GPTs Token")
async def create_token(body: GptToken, user: UserOut = Depends(get_current_active_user)):
    if resp := await test_user_action(user):
        return resp
        # await create_user_gpts_token(user.uid, body=body)
    data = await create_user_gpt_token(user.uid, body=body, is_gpts=True)
    return suc_data(data={"data": data})


@app.get("/search/gpts", summary="搜索gpts机器人")
async def search_gpts(gpts_code: str = Query(..., title="机器人名")):
    res = await search_gpts_by_code(gpts_code)
    if not res:
        return suc_data(msg='未找到机器人')
    return suc_data(data=res)


@app.get("/token/enable/{token_id}", summary="开关 GPT Token")
async def able_gpt_token(token_id: int, user: UserOut = Depends(get_current_active_user)):
    suc = await enable_or_disable_gpt(user.uid, token_id)
    if not suc:
        return fail_data()
    return suc_data()


@app.get("/token/{token_id}", summary="获取GPT Token明细")
async def update_gpt_token(token_id: int, user: UserOut = Depends(get_current_active_user)):
    is_suc = await get_gpt_detail(user.uid, token_id)
    if is_suc:
        return suc_data(data=is_suc)
    else:
        return fail_data()


@app.put("/token/{token_id}", summary="更新GPT Token")
async def update_gpt_token(request: Request,body: GptToken, token_id: int, user: UserOut = Depends(get_current_active_user)):
    res = {}
    all_body = await request.json()
    is_suc = await update_user_gpt_token(user.uid, token_id, body=body, res=res,all_body=all_body)
    if is_suc:
        return suc_data()
    else:
        return fail_data(**res)


@app.put("/token/gpts/{token_id}", summary="更新GPTs Token")
async def update_gpt_token(body: GptToken, token_id: int, user: UserOut = Depends(get_current_active_user)):
    res = {}
    await asyncio.sleep(1)
    is_suc = await update_user_gpt_token(user.uid, token_id, body=body, res=res, is_gpts=True)
    if is_suc:
        return suc_data()
    else:
        return fail_data(**res)


@app.delete("/token/{token_id}", summary="删除GPT Token")
async def delete_user_gpt_token(token_id: int, user: UserOut = Depends(get_current_active_user)):
    await del_user_gpt_token(user.uid, token_id)
    return suc_data()


@app.get("/gpts/tokens", summary="获取GPTs Token列表")
async def get_gpts_token_list(user: UserOut = Depends(get_current_active_user), token_name=Query(None, title="令牌名"),
                              api_key=Query(None, title="api key"), base_query: LimitQuery = Depends(),
                              use_in: str = Query('', title="使用场景"), type='',
                              is_robot=Query(0, title="是否机器人类型"),
                              filter_type=Query('token_name', title='搜索类型'), text=Query('', title="搜索文本")):
    return suc_data(
        data=await get_user_gpt_token_list(uid=user.uid, token_name=token_name, api_key=api_key, page=base_query.page,
                                           is_gpts=True, use_in=use_in, type=type,
                                           page_size=base_query.page_size, is_robot=is_robot, text=text,
                                           filter_type=filter_type))

# 复制一个gpt token
@app.post("/token/copy/{token_id}",summary="复制一个gpt token")
async def copy_gpt_token(token_id:int,user: UserOut = Depends(get_current_active_user)):
    return await copy_gpt_token_func(user.uid,token_id) 


@app.get("/tokens", summary="获取GPT Token列表")
async def get_token_list(user: UserOut = Depends(get_current_active_user), token_name=Query(None, title="令牌名"),
                         use_in: str = Query('', title="使用场景"), type='',
                         api_key=Query(None, title="api key"), base_query: LimitQuery = Depends(),
                         is_robot=Query(0, title="是否机器人类型"), filter_type=Query('token_name', title='搜索类型'),
                         text=Query('', title="搜索文本")):
    return suc_data(
        data=await get_user_gpt_token_list(uid=user.uid, token_name=token_name, api_key=api_key, page=base_query.page,
                                           use_in=use_in, type=type,
                                           page_size=base_query.page_size, is_robot=is_robot, text=text,
                                           filter_type=filter_type))


@app.get("/tool/tokens", summary="获取GPT tool Token列表")
async def get_token_list(user: UserOut = Depends(get_current_active_user), token_name=Query(None, title="令牌名"),
                         use_in: str = Query('', title="使用场景"), type='',
                         api_key=Query(None, title="api key"), base_query: LimitQuery = Depends(),
                         is_robot=Query(0, title="是否机器人类型"), filter_type=Query('token_name', title='搜索类型'),
                         text=Query('', title="搜索文本"),lang: str = Header(default="zh-CN")):
    return suc_data(
        data=await get_user_gpt_token_list(uid=user.uid, token_name=token_name, api_key=api_key, page=base_query.page,
                                           use_in=use_in, type=type,
                                           page_size=base_query.page_size, is_robot=is_robot, text=text,
                                           filter_type=filter_type, lang=lang))


@app.get("/logs", summary="获取GPT Token消耗日志列表")
async def get_token_logs(user: UserOut = Depends(get_current_active_user), time_query=Depends(TimeQuery),
                         limit_query: LimitQuery = Depends(), token_name: str = Query("", title="名称"),
                         filter_type=Query('name', title="过滤类型"), text=Query('', title="过滤内容"),
                         token_id=Query(0, title="id"),kb_id=Query(0, title="知识库id")):
    return suc_data(
        data=await get_gpt_logs(user.uid, token_name, time_query.start_time, time_query.end_time, limit_query.page,
                                limit_query.page_size, filter_type, text, token_id,kb_id=kb_id))


@app.get("/summary", summary="gpt 统计汇总")
async def get_summary(requests: Request, user: UserOut = Depends(get_current_active_user),
                      time_query=Depends(TimeQuery), particle_size: str = Query(..., title="颗粒度"),
                      token_id: int = Query(0, title="机器人/api 对应的id"),kb_id=Query(0, title="知识库id"),
                      use_in:str = Query('tool',title='使用场景')
                      ):
    zone = requests.headers.get("Tz", '')
    data = await get_log_summary(user.uid, time_query.start_time, time_query.end_time, particle_size, zone=zone,
                                 token_id=token_id,kb_id=kb_id,use_in=use_in)
    return suc_data(data=data)


@app.get('/share_code/summary', summary="分享码统计汇总")
async def get_share_code_summary(requests: Request,user: UserOut = Depends(get_current_active_user), time_query=Depends(TimeQuery),
                                 particle_size: str = Query(..., title="颗粒度")):
    data = await get_share_code_summary_func(user.uid,time_query.start_time, time_query.end_time, particle_size)
    return suc_data(data=data)


@app.post("/chat/record/sync/upload", summary="上传聊天记录")
async def upload_chat_record(requests: Request, file: UploadFile = File(...),
                             share_code: str = Form(..., title="分享码"),
                             sync_pwd: str = Form("", title="同步密码"),
                             timestamp: int = Form(..., title="客户端对应的时间戳 毫秒级"),
                             device: str = Form(..., title="设备名"),
                             ):
    """
    上传聊天记录
    * 如果密码不存在 那么创建一个密码，然后上传文件
    * 如果密码存在，那么直接记录对应的文件
    最后返回密码和刚刚上传的文件链接

    """
    import time
    timestamp = int(time.time()*1000)
    file = await file.read()
    return await upload_chat_record_func(file, share_code, timestamp, device, sync_pwd)


@app.post("/chat/record/sync/logs", summary="获取聊天记录最新对应的三条日志", response_model=BaseData)
async def download_chat_record(requests: Request,
                               share_code: str = Form(..., title="分享码"),
                               sync_pwd: str = Form("", title="同步密码"),
                               ):
    """返回最近3条日志列表"""
    return await get_chat_log(share_code, sync_pwd)


# 应用集成
# 增加集成信息
@app.post("/integration/add/{app_name}/{token_id}", summary="增加集成信息")
async def add_integration(requests: Request, user: UserOut = Depends(get_current_active_user),
                          app_name: APPName = Path(..., title="应用名称"), token_id: int = Path(...),
                          body: dict = Body(..., title="集成信息",
                                            description="token_id 必填,feishu_app_id feishu_app_secret feishu_verification_token feishu_encrypt_key feishu_bot_name")):
    return await integration_func(body=body, token_id=token_id, app_name=app_name)


@app.get("/integration/{token_id}", summary="获取集成信息", deprecated=True)
async def get_integration(requests: Request, user: UserOut = Depends(get_current_active_user),
                          token_id: int = Path(..., title="token_id")):
    # 获取集成信息。

    return await integration_func("get", token_id=token_id)


@app.put("/integration/{token_id}", summary="修改集成信息")
async def update_integration(requests: Request, user: UserOut = Depends(get_current_active_user),
                             token_id: int = Path(..., title="代理id"),
                             body: dict = Body(..., title="集成信息",
                                               description="token_id 必填,feishu_app_id feishu_app_secret feishu_verification_token feishu_encrypt_key feishu_bot_name")):
    return await integration_func("update", body, token_id=token_id)


@app.get("/wechat/share/card",summary='微信分享卡加密')
async def get_card_sign(url:str=Query(...,title="分享链接")):
    res = await build_share_signature(url)
    return suc_data(data={"result":res})
# 修改集成信息

@app.get('/model/price',summary="获取模型价格")
async def get_model_price(model_name:str):
    return await get_model_price_fnc(model_name)


@app.delete("/integration/{app_name}/{token_id}", summary="删除集成信息", deprecated=True)
async def delete_integration(requests: Request, user: UserOut = Depends(get_current_active_user),
                             app_name: APPName = Path(title="应用名称"),
                             token_id: int = Path(..., title="token_id")):
    # 删除集成信息
    return await integration_func("delete", token_id=token_id, app_name=app_name)

@app.get("/tool/list",summary="获取工具列表")
async def get_tool_list_view(lang:Locale=Header('', description="可以不传，但传的参必须是枚举定义的",title='语言',alias='Lang')):
    return suc_data(data=await get_tool_list(lang=lang))


"""
      `en_name` varchar(255) NOT NULL DEFAULT '',
  `jp_name` varchar(255) NOT NULL DEFAULT '',
  `name` varchar(128) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  `en_description` varchar(512) NOT NULL DEFAULT '',
  `jp_description` varchar(255) NOT NULL DEFAULT '',
  `description` varchar(512) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  `cn_domain` varchar(255) CHARACTER SET latin1 NOT NULL DEFAULT '',
  `domain` varchar(128) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  `tool_logo_video_url` varchar(255) NOT NULL,
  `logo_url` varchar(512) NOT NULL DEFAULT '',
  `prefix` varchar(255) NOT NULL DEFAULT '',
  `extra` json NOT NULL,
  `en_logo_url` varchar(255) NOT NULL DEFAULT '',
  `en_tool_logo_video_url` varchar(255) NOT NULL DEFAULT '',
  `ord` int(11) DEFAULT '99',
  `cate_id` int(11) DEFAULT NULL,
  `en_tool_logo_url` varchar(255) DEFAULT NULL,
  `cn_tool_logo_url` varchar(255) DEFAULT NULL,
  `jp_tool_logo_url` varchar(255) DEFAULT NULL,
  `jp_logo_url` varchar(255) DEFAULT NULL,
  `search_description` varchar(512) DEFAULT NULL,

    """
@app.post("/tool/item",summary="创建或者修改工具列表")
async def create_or_update(domain:str=Form("",description="工具域名"),prefix:str= Form("",description='域名前缀'),
                           cn_name:str=Form("",description="工具名称"),jp_name:str= Form("",description="工具名称"),en_name:str= Form("",description="工具名称"),
                           cn_description:str=Form("",description="工具描述"),en_description:str=Form("",description="工具描述"),jp_description:str=Form("",description="工具描述"),
                           # cn_logo_url:str=Form("",description="中文 后台编辑端显示图片"),en_logo_url:str=Form("",description="英文 后台编辑端显示图片"),jp_logo_url:str=Form("",description="日文 后台编辑端显示图片"),
                           cn_logo:UploadFile=File("",description="中文 后台编辑端显示图片"),en_logo:UploadFile=File("",description="英文 后台编辑端显示图片"),jp_logo:UploadFile=File("",description="日文 后台编辑端显示图片"),
                           # cn_tool_logo_url:str=Form("",description="中文 工具端显示图片"),en_tool_logo_url:str=Form("",description="英文 工具端显示图片"),jp_tool_logo_url:str=Form("",description="日文 工具端显示图片"),
                           cn_tool_logo:UploadFile=File("",description="中文 工具端显示图片"),en_tool_logo:UploadFile=File("",description="英文 工具端显示图片"),jp_tool_logo:UploadFile=File("",description="日文 工具端显示图片"),
                           #cn_tool_logo_video_url:str=Form("",description="中文 视频"),en_tool_logo_video_url:str=Form("",description="英文 视频"),jp_tool_logo_video_url:str=Form("",description="日文 视频"),
                           cn_tool_logo_video:UploadFile=File("",description="中文 视频"),en_tool_logo_video:UploadFile=File("",description="英文 视频"),jp_tool_logo_video:UploadFile=File("",description="日文 视频"),
                           cate_id:int=Form(0,description="分类id"),ord:int=Form(99,description="排序"),
                           open_source:bool=Form(False),open_source_url:str=Form(""),
                           search_description:str=Form("",description="搜索描述"),tool_id:int= Form(None,description="工具id，存在的情况下为修改记录")
                           ):
    values = {
        "domain": domain,
        "prefix": prefix,
        "name": cn_name,
        "open_source": open_source,
        "open_source_url": open_source_url,
        "jp_name": jp_name,
        "en_name": en_name,
        "description": cn_description,
        "en_description": en_description,
        "jp_description": jp_description,
        "logo_url": await upload_blob(await cn_logo.read(),cn_logo.filename) if cn_logo else '',
        "en_logo_url": await upload_blob(await en_logo.read(),en_logo.filename) if en_logo else '',
        "jp_logo_url": await upload_blob(await jp_logo.read(),jp_logo.filename) if jp_logo else '',
        "zh_tool_logo_url": await upload_blob(await cn_tool_logo.read(),cn_tool_logo.filename) if cn_tool_logo else '',
        "en_tool_logo_url": await upload_blob(await en_tool_logo.read(),en_tool_logo.filename) if en_tool_logo else '',
        "jp_tool_logo_url": await upload_blob(await jp_tool_logo.read(),jp_tool_logo.filename) if jp_tool_logo else '',
        "tool_logo_video_url": await upload_blob(await cn_tool_logo_video.read(),cn_tool_logo_video.filename) if cn_tool_logo_video else '',
        "en_tool_logo_video_url": await upload_blob(await en_tool_logo_video.read(),en_tool_logo_video.filename) if en_tool_logo_video else '',
        "jp_tool_logo_video_url": await upload_blob(await jp_tool_logo_video.read(),jp_tool_logo_video.filename) if jp_tool_logo_video else '',
        "cate_id": cate_id,
        "ord": ord,
        "search_description": search_description,
        "id": tool_id,
        "extra": {}
    }
    asyncio.ensure_future(create_or_update_tool(**values))
    return suc_data()


@app.get("/v1/tools/categories", summary="获取所有工具分类")
async def get_all_categories():
    tool_categories = await TToolCategory.all()
    return suc_data(data=[ToolCategoryResponse(**category.__dict__) for category in tool_categories])


@app.get("/v1/tools/categories/{category_id}", summary="根据id获取单个工具分类")
async def get_category(category_id: int):
    tool_category = await TToolCategory.get_or_none(id=category_id)
    if not tool_category:
        return fail_data(code=404, msg="Tool category not found")
    return {"code": 0, "msg": "success", "data": ToolCategoryResponse(**tool_category.__dict__)}


@app.post("/v1/tools/categories", summary="创建工具分类")
async def create_category(category_info: ToolCategoryCreate):
    tool_category = await TToolCategory.create(**category_info.model_dump())
    return {"code": 0, "msg": "success", "data": ToolCategoryResponse(**tool_category.__dict__)}


@app.put("/v1/tools/categories/{category_id}", summary="更新指定id的工具分类")
async def update_category(category_id: int, category_info: ToolCategoryUpdate):
    existing_category = await TToolCategory.get_or_none(id=category_id)
    if not existing_category:
        return fail_data(data={"id": category_id}, code=404, msg="Tool category not found")
    
    update_data = {key: value for key, value in category_info.model_dump().items() if value is not None}
    if not update_data:
        return fail_data(data={"id": category_id}, code=400, msg="No fields to update")
    
    await TToolCategory.filter(id=category_id).update(**update_data)
    
    tool_category = await TToolCategory.get(id=category_id)
    return {"code": 0, "msg": "success", "data": ToolCategoryResponse(**tool_category.__dict__)}


@app.delete("/v1/tools/categories/{category_id}", summary="删除指定id的工具分类")
async def delete_category(category_id: int):
    if not await TToolCategory.filter(id=category_id).exists():
        return fail_data(code=404, msg="Tool category not found")
    await TToolCategory.filter(id=category_id).delete()
    return suc_data(data={"id": category_id})


@app.get("/v1/tools", summary="获取工具列表")
async def get_all_tool(lang:LocaleGPT=Header('', description="可以不传，但传的参必须是枚举定义的",title='语言',alias='Lang')):
    tools = await TGptTool.filter(id__gt=2)
    # 统计每个 category_id 的工具数量
    category_counter = Counter(tool.cate_id for tool in tools)

    # 获取所有分类信息
    categories = await TToolCategory.all()

    # 定义字段映射，根据语言选择合适的字段
    lang_field_mapping = {
        "zh": {"cate_name": "cn_cate_name", "name": "name", "description": "description", "logo_url": "logo_url", "tool_logo_url": "zh_tool_logo_url", "tool_logo_video_url": "tool_logo_video_url"},
        "en": {"cate_name": "en_cate_name", "name": "en_name", "description": "en_description", "logo_url": "en_logo_url", "tool_logo_url": "en_tool_logo_url", "en_tool_logo_video_url": "tool_logo_video_url"},
        "ja": {"cate_name": "jp_cate_name", "name": "jp_name", "description": "jp_description", "logo_url": "jp_logo_url", "tool_logo_url": "jp_tool_logo_url", "jp_tool_logo_video_url": "tool_logo_video_url"},
    }

    # 确保 lang 合法，不合法时使用默认语言 "zh"
    lang_fields = lang_field_mapping.get(lang, lang_field_mapping["zh"])

    # 构造 summary 列表
    summary = [
        {
            "category_name": getattr(category, lang_fields["cate_name"]),
            "category_id": category.id,
            "count": category_counter.get(category.id, 0),
        }
        for category in categories
    ]

    # 构造工具列表，根据语言选择相应的字段
    data = [
        {
            "id": tool.id,
            "name": getattr(tool, lang_fields["name"]),  # 使用 lang 动态选择字段
            "description": getattr(tool, lang_fields["description"]),
            "logo_url": getattr(tool, lang_fields["logo_url"]),
            "tool_logo_video_url": getattr(tool, lang_fields["tool_logo_url"]),
            "domain": tool.domain,
            "cn_domain": tool.cn_domain,
            "prefix": tool.prefix,
            "cate_id": tool.cate_id,
            "extra": tool.extra,
            "ord": tool.ord,
            "created_time": tool.created_time,
            "open_source": tool.open_source,
            "open_source_url": tool.open_source_url
        }
        for tool in tools
    ]

    result = {
        "data": data,
        "summary": summary
    }

    return suc_data(data=result)


@app.get("/v1/tools/{tool_id}", summary="获取工具详情")
async def get_tool(tool_id: int):
    tool = await TGptTool.filter(id=tool_id).first()
    if not tool:
        return fail_data(code=404, msg="Tool not found")
    return {"code": 0, "msg": "success", "data": ToolBase(**tool.__dict__)}


@app.post("/v1/tools", summary="创建工具")
async def create_tool(tool_info: ToolBase, user: UserOut = Depends(get_current_admin)):
    tool = await TGptTool.create(**tool_info.model_dump(exclude={'id'}))
    return {"code": 0, "msg": "success", "data": ToolBase(**tool.__dict__)}


@app.put("/v1/tools/{tool_id}", summary="更新工具")
async def update_tool(tool_id: int, tool_info: ToolBase, user: UserOut = Depends(get_current_admin)):
    tool = await TGptTool.filter(id=tool_id).first()
    if not tool:
        return fail_data(data={"id": tool_id}, code=404, msg="Tool not found")
    
    update_data = {key: value for key, value in tool_info.model_dump().items() if value is not None}
    if not update_data:
        return fail_data(data={"id": tool_id}, code=400, msg="No fields to update")
    
    await TGptTool.filter(id=tool_id).update(**update_data)
    
    tool = await TGptTool.get(id=tool_id)
    return {"code": 0, "msg": "success", "data": ToolBase(**tool.__dict__)}


@app.delete("/v1/tools/{tool_id}", summary="删除工具")
async def delete_tool(tool_id: int, user: UserOut = Depends(get_current_admin)):
    if not await TGptTool.filter(id=tool_id).exists():
        return fail_data(code=404, msg="Tool not found")
    await TGptTool.filter(id=tool_id).delete()
    return suc_data(data={"id": tool_id})


@app.get("/tools/{tool_id}/models", summary="获取工具对应的模型")
async def get_tool_models(tool_id: int):
    models = await TModel.filter(tool_id_list__contains=[tool_id]).all()
    return suc_data(data=[{ model.id : model.name } for model in models])


@app.post("/tools/{tool_id}/models", summary="更新工具对应的模型")
async def _update_tool_models(tool_id: int, model_ids: List[int]):
    return suc_data(data=await update_tool_models(tool_id, model_ids))


# 移除admin验证 初步测试已通过 dependencies=[Depends(get_current_admin)]
@app.get("/apis/categories", summary="获取api分类列表")
async def get_categories():
    return await get_categories_list()


@app.post("/apis/categories", summary="添加api分类")
async def create_category(category: CategoryRequest, user: UserOut = Depends(get_current_admin)):
    return await add_category(category)


@app.put("/apis/categories/{category_id}", summary="更新指定id的api分类")
async def update_category(category_id: int, category: CategoryRequest, user: UserOut = Depends(get_current_admin)):
    return await update_category_at_category_id(category_id, category)


@app.delete("/apis/categories/{category_id}", summary="删除指定id的api分类")
async def delete_category(category_id: int, user: UserOut = Depends(get_current_admin)):
    return await delete_category_at_category_id(category_id)

# 获取所有 brands
@app.get("/apis/brands", summary="获取api品牌列表")
async def get_brands():
    return await get_brands_list()

# 创建新 brand
@app.post("/apis/brands", summary="添加api品牌")
async def create_brand(brand: BrandRequest, user: UserOut = Depends(get_current_admin)):
    return await add_brand(brand)

# 更新指定 brand
@app.put("/apis/brands/{brand_id}", summary="更新指定id的api品牌")
async def update_brand(brand_id: int, brand: BrandRequest, user: UserOut = Depends(get_current_admin)):
    return await update_brand_at_brand_id(brand_id, brand)

# 删除指定 brand
@app.delete("/apis/brands/{brand_id}", summary="删除指定id的api品牌")
async def delete_brand(brand_id: int, user: UserOut = Depends(get_current_admin)):
    return await delete_brand_at_brand_id(brand_id)

@app.get("/apis/list", summary="获取api超市列表")
async def get_apis_list_view(lang: LocaleGPT = Header('', description="支持zh/en/ja，默认为zh",title='语言',alias='lang')):
    return await get_apis_list(lang=lang)


@app.get("/apis/brand/{brand_id}", summary="获取指定brand下的models和apis")
async def get_apis_brand_view(lang: LocaleGPT = Header('', description="支持zh/en/ja，默认为zh",title='语言',alias='lang'),
                              brand_id: int = Path(..., title="品牌id")):
    return await get_models_apis_by_brand_id(lang=lang, brand_id=brand_id)


@app.post("/services", summary="在指定 sort_order 处添加一条数据")
async def add_service(request: ServiceRequest, user: UserOut = Depends(get_current_admin)):
    return await add_service_at_sort_order(request)


@app.put("/services/{service_id}", summary="修改指定 service_id 处的数据")
async def update_service(request: ServiceRequest, service_id: int, user: UserOut = Depends(get_current_admin)):
    return await update_service_at_service_id(request, service_id)


@app.delete("/services/{service_id}", summary="删除指定service_id的数据")
async def delete_service(service_id: int, user: UserOut = Depends(get_current_admin)):
    return await delete_service_at_service_id(service_id)


@app.get("/models/param", summary="获取模型参数列表")
async def get_models():
    return await get_models_param_list()


@app.post("/models/param", summary="添加模型参数")
async def create_model(model: ModelParamBase, user: UserOut = Depends(get_current_admin)):
    return await add_model_param(model)

@app.get("/models/{model_id}/param", summary="获取指定id的模型参数")
async def get_models(model_id: str):
    return await get_models_param_at_id(model_id)

@app.put("/models/{model_id}/param", summary="更新指定id的模型参数")
async def update_model(model_id: str, model: ModelParamBase, user: UserOut = Depends(get_current_admin)):
    return await update_model_param_at_model_id(model_id, model)


@app.delete("/models/{model_id}/param", summary="删除指定id的模型参数")
async def delete_model(model_id: str, user: UserOut = Depends(get_current_admin)):
    return await delete_model_param_at_model_id(model_id)


@app.get("/v1/text/parsing", summary="文本解析")
async def textract_view_public(requests: Request,url: str=Query(...,title="文件url"),
                        user: UserOut = Depends(get_current_active_user)):
    token = requests.headers.get("Authorization")
    try:
        result = await get_text_summary(url,token.split(" ")[-1],model="")
    except:
        result = ""

    return suc_data(data={"msg":result})

@app.post("/textract", summary="文本抽取")
async def textract_view(requests: Request,url: str=Body(...,title="文件url"),model:str=Body(""),
                        extra: str=Body('',title="额外参数,不用填"),Token:str=Header()):
    try:
        result = await get_text_summary(url,Token,model)
    except:
        result = url

    return suc_data(data={"msg":result})


@kb.put("/change/{kb_id}")
async def change_knowledge_base(requests: Request, user: UserOut = Depends(get_current_active_user),
                                kb_id: int = Path(..., title="知识库id"),
                                kb_name: str = Body("", title="知识库名称"),
                                is_global: bool = Body(False, title="回答模式 全局或者本地"),
                                kb_info:str=Body('',title="知识库备注")):
    return await change_kb_info(kb_id,kb_name,kb_info,user.uid,is_global=is_global)



@kb.post("/create_knowledge_base", summary="创建知识库")
@kb.post("/knowledge_base/create_knowledge_base", summary="创建知识库")
async def create_knowledge_base(requests: Request, user: UserOut = Depends(get_current_active_user),
                                kb_name: str = Body("", title="知识库名称"),
                                kb_info: str = Body('', title="知识库备注"),
                                kb_type: str = Body('chatchat', title="知识库类型  rag_nano or chatchat"),
                                model_id = Body(163, title="嵌入模型 用于向量化"),
                                llm_model_id = Body(0, title="rag使用的llm 用户向量化"),


                                ):
    body = {
        "knowledge_base_name": kb_name,
        "remark": kb_info,
        "kb_type": kb_type,
        "model_id": model_id,
        "llm_model_id": llm_model_id,
        "uid": user.uid
    }

    return await create_knowledge_base_func(body=body)


@kb.post("/create_knowledge_base/upload/files", summary="上传文件到知识库,可以选择是不是需要做向量化")
@kb.post("/knowledge_base/upload/files", summary="上传文件到知识库,可以选择是不是需要做向量化")
async def create_knowledge_base_upload_files(requests: Request, background_tasks:BackgroundTasks,user: UserOut = Depends(get_current_active_user),
                                             files: List[UploadFile] = File(..., description="上传文件，支持多文件"),
                                             kb_id: int = Form(0),
                                             override: bool = Form(False, description="覆盖已有文件"),
                                             to_vector_store: bool = Form(True, description="上传文件后是否进行向量化"),
                                             chunk_size: int = Form(250, description="知识库中单段文本最大长度"),
                                             chunk_overlap: int = Form(50,
                                                                       description="知识库中相邻文本重合长度"),
                                             zh_title_enhance: bool = Form(False,
                                                                           description="是否开启中文标题加强"),
                                             split_mode: str = Form('',description="分词模式,传统知识库可选参数为：jina/separator/meta_chunking;graph知识库可选参数为:chunk_size/separator,默认空字符"),
                                             separator: str = Form('', description="当split_mode为separator模式时，指定分隔符，默认为空"),
                                             doc2x:bool = Form(False,description="是否使用doc2x"),
                                             encoding:str = Form('',description="编码类型"),
                                             max_chunk_length:int = Form(4000,description=""),
                                             is_summarize: bool = Form(False,description="是否对分块进行总结，使用deepseek模型，默认为否"),
                                             language: str=Form("zh",description= "meta-chunking切片的源文本语言,可选zh/en,默认为zh"),
                                             dynamic_merge:str = Form("no",description="meta-chunking切片的动态合并模式,可选yes/no,默认为no"),
                                             model_name: str=Form("deepseek-chat",description="meta-chunking切片的模型,默认为deepseek-chat"),
                                             ):
    async def func(files):
        result = [{"file_name": file.filename, "current": 0, "total": 1,"status":0} for file in files]
        task_id = uuid.uuid4().hex
        yield_done = True
        wait_count = 0

        # 优化：如果文件数量较多，使用后台任务处理
        MAX_SYNC_FILES = 3  # 超过3个文件时使用后台任务

        if len(files) > MAX_SYNC_FILES:
            # 大量文件使用后台任务处理，避免阻塞请求
            Tools.log.info(f"文件数量 {len(files)} 超过同步处理限制，使用后台任务处理")

            # 将文件上传任务添加到后台任务队列
            background_tasks.add_task(
                process_files_in_background,
                files, kb_id, override, to_vector_store, chunk_size,
                chunk_overlap, zh_title_enhance, user.uid, split_mode,
                separator, max_chunk_length, is_summarize, task_id,
                encoding, doc2x, language, dynamic_merge, model_name
            )

            # 立即返回任务ID，客户端可以通过任务ID查询进度
            yield suc_data(data={
                "task_id": task_id,
                "message": "文件较多，已提交后台处理",
                "files_count": len(files),
                "status": "background_processing"
            }).model_dump_json()
            yield '[DONE]'
            return

        # 少量文件使用并发处理而非顺序处理
        MAX_CONCURRENT_FILES = 2  # 限制并发文件处理数量
        sem = asyncio.Semaphore(MAX_CONCURRENT_FILES)

        async def process_single_file(index, file):
            """处理单个文件的异步函数"""
            async with sem:
                async for resp in create_knowledge_base_upload_files_func(
                    [file], kb_id, override, to_vector_store, chunk_size,
                    chunk_overlap, zh_title_enhance, user.uid, split_mode=split_mode,
                    separator=separator, max_chunk_length=max_chunk_length,
                    is_summarize=is_summarize, task_id=task_id, encoding=encoding,
                    doc2x=doc2x, background_tasks=background_tasks,
                    language=language, dynamic_merge=dynamic_merge, model_name=model_name
                ):
                    result[index] = resp
                    if isinstance(resp, str):
                        nonlocal wait_count, yield_done
                        if wait_count == 0:
                            wait_count += 1
                            yield_done = False

                    # 实时返回进度
                    data = suc_data(data=result)
                    yield data.model_dump_json()

        # 并发处理文件，但限制并发数量
        tasks = []
        for index, file in enumerate(files):
            task = asyncio.create_task(process_single_file(index, file))
            tasks.append(task)

        # 等待所有文件处理完成
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            Tools.log.error(f"文件处理出错: {e}")
            yield suc_data(data={"error": str(e)}).model_dump_json()

        if yield_done:
            yield '[DONE]'
        else:
            yield suc_data(data={"task_id": task_id}).model_dump_json()
            yield '[WAIT]'
    return EventSourceResponse(func(files))


@kb.get("/knowledge_base/upload/task/{task_id}", summary="查询文件上传任务进度")
async def get_upload_task_progress(task_id: str, user: UserOut = Depends(get_current_active_user)):
    """查询后台文件上传任务的进度"""
    try:
        task_key = f"file_upload_task:{task_id}"
        task_info = await Tools.redis.hgetall(task_key)

        if not task_info:
            return fail_data(msg="任务不存在或已过期")

        # 转换字节字符串为普通字符串
        result = {}
        for key, value in task_info.items():
            if isinstance(key, bytes):
                key = key.decode('utf-8')
            if isinstance(value, bytes):
                value = value.decode('utf-8')
            result[key] = value

        # 计算进度百分比
        total = int(result.get('total', 0))
        completed = int(result.get('completed', 0))
        progress = (completed / total * 100) if total > 0 else 0

        return suc_data(data={
            "task_id": task_id,
            "status": result.get('status', 'unknown'),
            "total": total,
            "completed": completed,
            "progress": round(progress, 2),
            "error": result.get('error', None)
        })

    except Exception as e:
        Tools.log.error(f"查询任务进度失败: {e}")
        return fail_data(msg=f"查询失败: {str(e)}")


@kb.post("/knowledge_base/update_docs/files",summary="更新指定的文件向量化")
async def update_docs(requests: Request,background_tasks:BackgroundTasks, user: UserOut = Depends(get_current_active_user),
                      kb_id: int = Body(..., title="知识库id"),
                      split_mode=Body('',description="分词模式,新增 jina/separator，默认空字符"),
                      separator=Body('',description="当split_mode为separator模式时，指定分隔符，默认为空"),
                      max_chunk_length: int = Body(4000, description=""),
                      is_summarize: bool = Body(False, description=""),

    file_names: List[str] = Body([], description="文件id")

                      ):
    result = [{"file_name": file, "current": 0, "total": 1, "status": 0} for file in file_names]
    index_dict = {file_name: index for index, file_name in enumerate(file_names)}
    async def func():
        for i in file_names:
            async for resp in update_docs_fnc([i],kb_id,user.uid,split_mode=split_mode,separator=separator,max_chunk_length=max_chunk_length,is_summarize=is_summarize,background_tasks = background_tasks):
                if resp == "DONE":
                    continue
                result[index_dict.get(resp.get("file_name"))] = resp
                data = suc_data(data=result)
                yield data.model_dump_json()

        yield '[DONE]'

    return EventSourceResponse(func())

@kb.get("/knowledge_base/list_files/{kb_id}", summary="获取知识库文件列表")
async def list_files(requests: Request, user: UserOut = Depends(get_current_active_user),
                     page: int = Query(1, title="页码"),
                     page_size: int = Query(10, title="每页数量"),
                     kb_id: int = Path(...)):
    return await list_files_func(user.uid, kb_id,page,page_size)


@kb.get("/knowledge_base/info/{kb_id}", summary="获取知识库基本信息")
async def get_knowledge_base_info(requests: Request, user: UserOut = Depends(get_current_active_user),
                                  kb_id: int = Path(...)):
    return await get_knowledge_base_info_func(user.uid, kb_id)


@kb.get("/knowledge_base/list_knowledge_bases", summary="获取知识库列表")
async def get_knowledge_base_list(requests: Request, user: UserOut = Depends(get_current_active_user),
                                  page: int = Query(1, title="页码"),
                                  filter_type:str=Query(''),
                                  text:str=Query(''),
                                  page_size: int = Query(10, title="每页数量")
                                  ):
    return await get_knowledge_base_list_func(user.uid,page,page_size,filter_type,text)


@kb.get("/knowledge_base/graph/{uid}/{kb_id}",summary="获取知识库图谱")
async def get_graph(requests: Request, user: UserOut = Depends(get_current_active_user),
                    uid: int = Path(..., title="用户名id"),
                    kb_id: int = Path(..., title="知识库id"),):
    
    _ = await TKnowledgeBase.filter(uid=uid,id=kb_id).first().values("kb_name","type","file_count")
    if not _:
        return error_handler(status=-404)
    kb_name = _.get("kb_name")
    kb_type = _.get("type")
    kb_file_count  = _.get("file_count")
    #判断是否是graphrag类型的数据库
    if kb_type and kb_type != "rag_nano":
        return fail_data(code="-1",
                         msg="知识库类型错误")
    #判断知识库是否为空
    if kb_file_count == 0:
        return fail_data(code="-1",
                         msg="知识库文件为空")
    html_data = await get_graph_data(uid,kb_name)
    return HTMLResponse(content=html_data)

@kb.get("/knowledge_base/graphfile/{uid}/{kb_id}", summary="获取知识库图谱graphml格式的源文件")
async def get_graphfile(requests: Request, user: UserOut = Depends(get_current_active_user),
                        uid: int = Path(..., title="用户名id"),
                        kb_id: int = Path(..., title="知识库id"),):
    
    _ = await TKnowledgeBase.filter(uid=uid,id=kb_id).first().values("kb_name","type","file_count")
    if not _:
        return error_handler(status=-404)
    kb_name = _.get("kb_name")
    kb_type = _.get("type")
    kb_file_count  = _.get("file_count")
    #判断是否是graphrag类型的数据库
    if kb_type and kb_type != "rag_nano":
        return fail_data(code="-1",
                         msg="知识库类型错误")
    #判断知识库是否为空
    if kb_file_count == 0:
        return fail_data(code="-1",
                         msg="知识库文件为空")
    
    graphml_file_path = await get_graph_file(uid,kb_name)

    # 检查文件是否存在
    if not graphml_file_path.exists():
        return fail_data(code="-1",
                         msg="graphml文件不存在")
    return FileResponse(graphml_file_path,filename=f"{kb_name}.graphml")


@kb.delete("/knowledge_base/delete_knowledge_base/{kb_id}", summary="删除知识库")
async def delete_knowledge_base(requests: Request, user: UserOut = Depends(get_current_active_user),
                                kb_id: int = Path(..., title="知识库id")):
    return await delete_knowledge_base_func(user.uid, kb_id)


@kb.delete("/knowledge_base/delete_docs", summary="删除知识库的指定文件")
async def delete_docs(requests: Request, user: UserOut = Depends(get_current_active_user),
                      kb_id: int = Body(..., title="知识库id"),
                      only_vs: int = Body(False, title="是否只删除向量库的文件"),

                      file_names: List[str] = Body(..., title="文件名") ):
    return await delete_docs_func(user.uid, kb_id, file_names,only_vs)





LLM_DEVICE = "auto"

HISTORY_LEN = 3

MAX_TOKENS = 2048

TEMPERATURE = 0.7
@kb.post("/chat/knowledge_base_chat", summary="对话，知识库问答")
async def chat(request: Request,
               query: str = Body(..., description="用户输入", examples=["你好"]),
                              top_k: int = Body(3, description="匹配向量数"),
                              score_threshold: float = Body(
                                  0.5,
                                  description="知识库匹配相关度阈值，取值范围在0-1之间，SCORE越小，相关度越高，取到1相当于不筛选，建议设置在0.5左右",
                                  ge=0,
                                  le=2
                              ),
                kb_id: int = Body(0, description="知识库id"),
                              history: List[dict] = Body(
                                  [],
                                  description="历史对话",
                                  examples=[[
                                      {"role": "user",
                                       "content": "我们来玩成语接龙，我先来，生龙活虎"},
                                      {"role": "assistant",
                                       "content": "虎头虎脑"}]]
                              ),
                              stream: bool = Body(True, description="流式输出,只兼容了流式"),
                              model_name: str = Body(...),
                              temperature: float = Body(TEMPERATURE, description="LLM 采样温度", ge=0.0, le=1.0),
                                 user_prompt:str  = Body("", description="根据条件不能回答问题要怎么回答"),
                              max_tokens: int = Body(
                                  None,
                                  description="限制LLM生成Token数量，默认None代表模型最大值"
                              ),
                              prompt_name: str = Body(
                                  "default",
                                  description="使用的prompt模板名称(在configs/prompt_config.py中配置)"
                              )):
    api_key = request.headers.get("Authorization","").split(" ")[-1]
    if not api_key:
        return {"error":{
            "err_code":-10002,
            "message":"Missing 302 Knowledge_Apikey",
            "message_cn":"缺少 302 API 知识库密钥",
            "message_jp":"302 APIキーがありません",
            "type":"api_error"
        }}
    map_token = await TTokenMapping.filter(
        external_token_id=Subquery(TToken.filter(value=api_key).first().values("id"))).first().values(
        "id", "user_id")
    Tools.log.debug(f"map_token:{map_token}")
    if map_token is None:
        return fail_data(msg="不支持的ApiKey,请使用知识库机器人的KEY")
    uid = map_token.get("user_id")
    if not kb_id:
        token_id = map_token.get("id")
        token = await TTokenInfo.get(token_id=token_id).values("extra", "settings")
        kb_id = token.get("extra").get("kb_id")
    _ = await TKnowledgeBase.filter(id=kb_id).first().values("kb_name",'type')
    if not _:
        return error_handler(status=-404)
    kb_name = _.get("kb_name")
    kb_type = _.get("type")
    if kb_type and kb_type != 'chatchat':
        # rag_nano
        """
        {
    "code": 0,
    "msg": "success",
    "data": {
        "answer": "## 叶文洁的角色与重要性\n\n叶文洁是《"
    }
}
        """
        if not stream:
            tmp = ""
            async for chunk in query_from_rag_nano(query,api_key=api_key,base_url=Tools.config.kb.base_url,model=model_name,tok_k=top_k,uid=uid,kb_name=kb_name,user_prompt=user_prompt):
                if chunk == 'DONE':
                    return suc_data(data={"answer":tmp})
                resp = json.loads(chunk)
                tmp += resp.get("data").get("answer")

        return EventSourceResponse(query_from_rag_nano(query,api_key=api_key,base_url=Tools.config.kb.base_url,model=model_name,tok_k=top_k,uid=uid,kb_name=kb_name,user_prompt=user_prompt))


    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=api_key).first().values("id"))).first().values("id","user_id","status","limit_cost",'current_cost')
    token_info = await TTokenInfo.filter(token_id=map_token.get("id")).first().values("current_date_cost","limit_daily_cost", "current_month_cost", "limit_monthly_cost")
    uid = map_token.get("user_id")
    _ = await TUser.filter(uid=uid).first().values("is_indebted")
    status = map_token.get("status")
    if map_token.get("limit_cost",0)>0 and map_token.get("limit_cost",0) <= map_token.get("current_cost",0):
        status = -103
    if token_info.get("limit_daily_cost") and token_info.get("limit_daily_cost") <= token_info.get("current_date_cost"):
        status = -3
    if token_info.get("limit_monthly_cost") and token_info.get("limit_monthly_cost") <= token_info.get("current_month_cost"):
        status = -6
    if _.get("is_indebted"):
        status = -100
    if resp := await error_handler(status):
        return resp

    if not query:
        return {"error": "query is required"}
    if not stream:
        tmp = ""
        async for chunk in chat_with_kb_func(query=query, top_k=top_k, api_key=api_key,base_url=Tools.config.kb.base_url,kb_id=kb_id,
                                   score_threshold=score_threshold, history=history, stream=stream,user_prompt=user_prompt,
                                   model_name=model_name, temperature=temperature, max_tokens=max_tokens,
                                   prompt_name=prompt_name):
            if chunk == 'DONE':
                return suc_data(data={"answer": tmp})
            resp = json.loads(chunk)
            tmp += resp.get("data").get("answer")

    return EventSourceResponse(chat_with_kb_func(query=query, top_k=top_k, api_key=api_key,base_url=Tools.config.kb.base_url,kb_id=kb_id,
                                   score_threshold=score_threshold, history=history, stream=stream,user_prompt=user_prompt,
                                   model_name=model_name, temperature=temperature, max_tokens=max_tokens,
                                   prompt_name=prompt_name))


#openai形式接口
@kb.post("/v1/chat/completions", summary="Openai形式接口,知识库问答",response_model=ChatCompletionResponse)
async def chat_with_opeani(request:Request,chatrequest:ChatCompletionRequest):
    api_key = request.headers.get("Authorization").split(" ")[-1]
    if not api_key:
        return {"error":{
            "err_code":-10002,
            "message":"Missing 302 Knowledge_Apikey",
            "message_cn":"缺少 302 API 知识库密钥",
            "message_jp":"302 APIキーがありません",
            "type":"api_error"
        }}
    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=api_key).first().values("id"))).first().values("id","user_id")
    Tools.log.debug(f"map_token:{map_token}")
    uid = map_token.get("user_id")
    if uid:
        token_id = map_token.get("id")
        token = await TTokenInfo.get(token_id=token_id).values("extra","settings")
        kb_id = token.get("extra").get("kb_id")
        Tools.log.debug(f"kb_id:{kb_id}")
    else:
        return fail_data(msg="知识库不存在")
    know_base = await TKnowledgeBase.filter(id=kb_id).first().values("kb_name","type")
    if not know_base:
        return error_handler(status=-404)
    kb_name = know_base.get("kb_name")
    kb_type = know_base.get("type")
    req_dict = chatrequest.model_dump()
    Tools.log.debug(f"req_dict:{req_dict}")
    query = req_dict.get("messages")[-1].get("content")
    Tools.log.debug(f"content：{query}")
    stream = req_dict.get("stream")
    Tools.log.debug(f"stream:{stream}")
    model_name = req_dict.get("model")
    Tools.log.debug(f"model_name:{model_name}")
    #获取用户提示词
    user_prompt = req_dict.get("messages")[0].get("content") if req_dict.get("messages")[0].get("role") == "system" else None
    #生成会话任务id
    id ="chatcmpl-"+ str(uuid.uuid1().hex)
    if kb_type and kb_type != 'chatchat':
        # rag_nano
        """
        {
    "code": 0,
    "msg": "success",
    "data": {
        "answer": "## 叶文洁的角色与重要性\n\n叶文洁是《"
    }
}
    """
        if not stream:
            tmp = ""
            async for chunk in query_from_rag_nano(query,api_key=api_key,base_url=Tools.config.kb.base_url,model=model_name,uid=uid,kb_name=kb_name,user_prompt = user_prompt):
                if chunk == 'DONE':
                    message = ChatMessage(
                        role="assistant",
                        content=tmp,
                    )
                    choice_data = ChatCompletionResponseChoice(
                        index=0,
                        message=message,
                        finish_reason="stop",
                    )

                    return ChatCompletionResponse(
                        model=model_name,
                        id=id,
                        choices=[choice_data],
                        object="chat.completion"
                    )
                resp = json.loads(chunk)
                tmp += resp.get("data").get("answer")
        
        async def genera_openai():
            async for chunk in query_from_rag_nano(query,api_key=api_key,base_url=Tools.config.kb.base_url,model=model_name,uid=uid,kb_name=kb_name,user_prompt = user_prompt):
                
                if chunk == 'DONE':
                    yield '[DONE]'
                    return

                resp = json.loads(chunk)
                send_msg= resp.get("data").get("answer")
            
                message = DeltaMessage(
                    content=send_msg,
                    role="assistant",
                    function_call=None,
                )
                choice_data = ChatCompletionResponseStreamChoice(
                    index=0,
                    delta=message,
                    finish_reason="stop"
                )
                chunk = ChatCompletionResponse(
                    model=model_name,
                    id=id,
                    choices=[choice_data],
                    created=int(time.time()),
                    object="chat.completion.chunk"
                )
                yield "{}".format(chunk.model_dump_json(exclude_unset=True))

        return EventSourceResponse(genera_openai(),media_type="text/event-stream")

    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=api_key).first().values("id"))).first().values("id","user_id","status","limit_cost",'current_cost')
    token_info = await TTokenInfo.filter(token_id=map_token.get("id")).first().values("current_date_cost","limit_daily_cost", "current_month_cost", "limit_monthly_cost")
    uid = map_token.get("user_id")
    _ = await TUser.filter(uid=uid).first().values("is_indebted")
    status = map_token.get("status")
    if map_token.get("limit_cost",0)>0 and map_token.get("limit_cost",0) <= map_token.get("current_cost",0):
        status = -103
    if token_info.get("limit_daily_cost") and token_info.get("limit_daily_cost") <= token_info.get("current_date_cost"):
        status = -3
    if token_info.get("limit_monthly_cost") and token_info.get("limit_monthly_cost") <= token_info.get("current_month_cost"):
        status = -6
    if _.get("is_indebted"):
        status = -100
    if resp := await error_handler(status):
        return resp

    if not query:
        return {"error": "query is required"}
    temperature = req_dict.get("temperature")
    max_tokens = req_dict.get("max_token")
    history = req_dict.get("messages")
    if not stream:
        tmp = ""
        async for chunk in chat_with_kb_func(query=query, top_k=3, api_key=api_key,base_url=Tools.config.kb.base_url,kb_id=kb_id,
                                   score_threshold=0.5, history=history, stream=stream,user_prompt="",
                                   model_name=model_name, temperature=temperature, max_tokens=max_tokens,
                                   prompt_name="default"):
            if chunk == 'DONE':
                message = ChatMessage(
                        role="assistant",
                        content=tmp,
                    )
                choice_data = ChatCompletionResponseChoice(
                        index=0,
                        message=message,
                        finish_reason="stop",
                    )
            
                return ChatCompletionResponse(
                        model=model_name,
                        id=id,
                        choices=[choice_data],
                        object="chat.completion"
                    )
            resp = json.loads(chunk)
            tmp += resp.get("data").get("answer")
    
    async def genera_openai():
        async for chunk in chat_with_kb_func(query=query, top_k=3, api_key=api_key,base_url=Tools.config.kb.base_url,kb_id=kb_id,
                                   score_threshold=0.5, history=history, stream=stream,user_prompt="",
                                   model_name=model_name, temperature=temperature, max_tokens=max_tokens,
                                   prompt_name="default"):
                
                if chunk == 'DONE':
                    yield '[DONE]'
                    return
                resp = json.loads(chunk)
                send_msg= resp.get("data").get("answer")

                message = DeltaMessage(
                    content=send_msg,
                    role="assistant",
                    function_call=None,
                )
                choice_data = ChatCompletionResponseStreamChoice(
                    index=0,
                    delta=message,
                    finish_reason="stop"
                )
                chunk = ChatCompletionResponse(
                    model=model_name,
                    id=id,
                    choices=[choice_data],
                    created=int(time.time()),
                    object="chat.completion.chunk"
                )
                yield "{}".format(chunk.model_dump_json(exclude_unset=True))

    return EventSourceResponse(genera_openai(),media_type="text/event-stream")


@kb.post("/chat/agent", summary="对话，agent问答")
async def chat_with_agent(request: Request,query: str = Body(..., description="用户输入", examples=["恼羞成怒"]),
                     history = Body([],description="历史对话",examples=[[
                                                       {"role": "user", "content": "请使用知识库工具查询今天北京天气"},
                                                       {"role": "assistant",
                                                        "content": "使用天气查询工具查询到今天北京多云，10-14摄氏度，东北风2级，易感冒"}]]
                                                   ),
                     stream: bool = Body(True, description="流式输出"),
                     model_name: str = Body('gpt-4o', description="LLM 模型名称。"),
                     temperature: float = Body(TEMPERATURE, description="LLM 采样温度", ge=0.0, le=1.0),
                     max_tokens  = Body(None, description="限制LLM生成Token数量，默认None代表模型最大值"),
                     user_prompt:str  = Body("", description="根据条件不能回答问题要怎么回答"),
                     prompt_name: str = Body("default",
                                             description="使用的prompt模板名称(在configs/prompt_config.py中配置)"),
                     ):
    api_key = request.headers.get("Authorization", "").split(" ")[-1]

    map_token = await TTokenMapping.filter(external_token_id=Subquery(TToken.filter(value=api_key).first().values("id"))).first().values("id","user_id","status")
    uid = map_token.get("user_id")
    _ = await TUser.filter(uid=uid).first().values("is_indebted")
    status = map_token.get("status")
    if _.get("is_indebted"):
        status = -100
    if resp := await error_handler(status):
        return resp

    return EventSourceResponse(chat_with_agent_func(query=query, api_key=api_key,base_url=f"{Tools.config.kb.base_url}/v1",
                                   history=history, stream=stream,
                                   model_name=model_name, temperature=temperature, max_tokens=max_tokens,
                                   prompt_name=prompt_name))


@kb.post("/knowledge_base/to_md/{kb_id}", summary="上传url，入库向量化")
async def create_knowledge_base_upload_md(request:Request,background_tasks:BackgroundTasks,kb_id: int,
                                          url_list: List[str] = Form(..., description="url列表"),
                                          override: bool = Form(False, description="覆盖已有文件"),
                                          to_vector_store: bool = Form(True, description="上传文件后是否进行向量化"),
                                          chunk_size: int = Form(100, description="知识库中单段文本最大长度"),
                                          split_mode: str = Form('',description="分词模式,新增 jina/separator，默认空字符"),
                                          separator: str = Form('', description="当split_mode为separator模式时，指定分隔符，默认为空"),
                                          max_chunk_length:int = Form(4000, description="文本切分模式,新增 jina 默认空字符"),
                                          chunk_overlap: int = Form(20,
                                                                    description="知识库中相邻文本重合长度"),
                                          zh_title_enhance: bool = Form(False,
                                                                        description="是否开启中文标题加强"),
                                          is_summarize: bool = Form(False, description="是否开启分片总结"),
                                          language: str=Form("zh",description= "meta-chunking切片的源文本语言,可选zh/en,默认为zh"),
                                          dynamic_merge:str = Form("no",description="meta-chunking切片的动态合并模式,可选yes/no,默认为no"),
                                          model_name: str=Form("deepseek-chat",description="meta-chunking切片的模型,默认为deepseek-chat"),
                                          user: UserOut = Depends(get_current_active_user)):
    url_list = url_list[0].split(",") if len(url_list) == 1 else url_list
    tz = request.headers.get("Tz")
    # return EventSourceResponse(func(files))
    return EventSourceResponse(to_md5(url_list=url_list,kb_id=kb_id,override=override,to_vector_store=to_vector_store,chunk_size=chunk_size,
           chunk_overlap=chunk_overlap,zh_title_enhance=zh_title_enhance,uid=user.uid,tz=tz,split_mode=split_mode,
           separator=separator,max_chunk_length=max_chunk_length,is_summarize=is_summarize,background_tasks = background_tasks,
           language = language,dynamic_merge = dynamic_merge,model_name=model_name))



@kb.post("/knowledge_base/crawler/{kb_id}", summary="crawler，入库向量化")
async def create_knowledge_base_upload_crawler(request:Request,background_tasks:BackgroundTasks,kb_id: int,
                                          url: str = Form(..., description="url列表"),
                                          depth: int = Form(1, description="爬取层级"),
                                          t: int = Form(10, description="爬取线程数"),
                                          target_content: str = Form("", description="css样式解析"),
                                          override: bool = Form(False, description="覆盖已有文件"),
                                          to_vector_store: bool = Form(True, description="上传文件后是否进行向量化"),
                                          chunk_size: int = Form(100, description="知识库中单段文本最大长度"),
                                          split_mode: str = Form('',description="分词模式,新增 jina/separator，默认空字符"),
                                          separator: str = Form('', description="当split_mode为separator模式时，指定分隔符，默认为空"),
                                          max_chunk_length:int = Form(4000, description="文本切分模式,新增 jina 默认空字符"),
                                          chunk_overlap: int = Form(20,
                                                                    description="知识库中相邻文本重合长度"),
                                          zh_title_enhance: bool = Form(False,
                                                                        description="是否开启中文标题加强"),
                                          is_summarize: bool = Form(False, description="是否进行分块总结"),
                                          language: str=Form("zh",description= "meta-chunking切片的源文本语言,可选zh/en,默认为zh"),
                                          dynamic_merge:str = Form("no",description="meta-chunking切片的动态合并模式,可选yes/no,默认为no"),
                                          model_name: str=Form("deepseek-chat",description="meta-chunking切片的模型,默认为deepseek-chat"),
                                             user: UserOut = Depends(get_current_active_user)):
    tz = request.headers.get("Tz")
    # return EventSourceResponse(func(files))
    kw = dict(url=url, kb_id=kb_id, override=override, to_vector_store=to_vector_store,
               chunk_size=chunk_size,depth=depth,t=t,target_content=target_content,
               chunk_overlap=chunk_overlap, zh_title_enhance=zh_title_enhance, uid=user.uid, tz=tz)
    url_list = await download_url(**kw)
    Tools.log.info(f"url_list:{url_list}")


    return EventSourceResponse(to_md5(url_list=url_list,kb_id=kb_id,override=override,to_vector_store=to_vector_store,chunk_size=chunk_size,
           chunk_overlap=chunk_overlap,zh_title_enhance=zh_title_enhance,uid=user.uid,tz=tz,is_msg=True,split_mode=split_mode,separator=separator,max_chunk_length=max_chunk_length,
           is_summarize=is_summarize,background_tasks = background_tasks,language=language,dynamic_merge =dynamic_merge,model_name=model_name))
    # await get_path_list(**kw)
    # return EventSourceResponse(
    #     crawler(**kw))

@kb.get("/knowledge_base/list_snippets/{file_id}", summary="获取文件片段列表")
async def list_snippets(file_id: int,
                        page: int = Query(1, description="page,可选，post请求不用"),
                        text: str = Query("", description="模糊查询"),
                        page_size: int = Query(10, description="page_size,可选，post请求不用"),
                        user: UserOut = Depends(get_current_active_user)):
    return await list_snippets_func(file_id=file_id,uid=user.uid,page=page,page_size=page_size,text=text)

@kb.put("/knowledge_base/list_snippets/{uuid}",summary="修改文件片段的状态")
async def update_snippets(uuid:str,kb_id:int,user: UserOut = Depends(get_current_active_user)):
    await update_snippets_func(uuid=uuid,uid=user.uid,kb_id=kb_id)
    return suc_data()


@kb.delete("/knowledge_base/crawler/stop/{request_id}")
async def stop_crawler(request_id: str):

    await Tools.redis.setex(f"crawler_{request_id}", 60*5,'1')
    return suc_data()

#file_meta_chunking API
@kb.post("/knowledge_base/file_meta_chunking")
async def file_meta_chunking(request: Request,
                        files: List[UploadFile] = File(..., description="上传文件，支持多文件"),
                        model_name:str = Form("deepseek-chat",description="meta_chunking切片的llm名称"),
                        language:str = Form("zh",description="文件语言，可选zh/en,默认为zh"),
                        dynamic_merge:str = Form("no",description="是否动态合并，可选yes/no，默认为no"),
                        ):
    import functools
    from controllers.gpt import get_text_summary
    api_key = request.headers.get("Authorization", "").split(" ")[-1]
    if not api_key:
        return {"error":{
            "err_code":-10002,
            "message":"Missing 302 Apikey",
            "message_cn":"缺少 302 API 密钥",
            "message_jp":"302 APIキーがありません",
            "type":"api_error"
        }}
    base_url = Tools.config.kb.base_url
    api_configure = {"api_key":api_key,"base_url":base_url,"model_name":model_name}
    meta_chunking = functools.partial(lumberchunker,api_name ="openai",api_configure=api_configure,language=language,dynamic_merge=dynamic_merge)
    chunkgs_res = []
    for file in files:
        if file.filename=='':
            return {"error":{
            "err_code":-10016,
            "message":"Failed to get the file.",
            "message_cn":"获取文件失败",
            "message_jp":"ファイルの取得に失敗しました。",
            "type":"file_error"
        }}
        file_dict = {}
        file_content = await file.read()
        task_name = KTasks.upload_blob
        Tools.log.debug(f"task_name:{task_name}")
        encoded_string = base64.b64encode(file_content).decode('utf-8')
        kw_data = {"data":encoded_string,"file_name":file.filename,"pre":f"meta_chunking/files","proxy_blob":True,"content_type":None}
        blob_url = await rec_celery_blob_url(task_name,kw_data)
        result = await get_text_summary(blob_url,api_key,)
        meta_chunks = await meta_chunking(text=result)
        file_dict["file_name"] = file.filename
        file_dict["chunks_len"] = len(meta_chunks)
        file_dict["chunks"] = [[chunk] for chunk in meta_chunks]
        chunkgs_res.append(file_dict)
    return chunkgs_res

#text_meta_chunking API
@kb.post("/knowledge_base/text_meta_chunking")
async def text_meta_chunking(request: Request,
                        text: str = Form("",description="需要切片的文本"),
                        model_name:str = Form("deepseek-chat",description="meta_chunking切片的llm名称"),
                        language:str = Form("zh",description="文件语言，可选zh/en,默认为zh"),
                        dynamic_merge:str = Form("no",description="是否动态合并，可选yes/no，默认为no"),):
    import functools
    api_key = request.headers.get("Authorization", "").split(" ")[-1]
    if not api_key:
        return {"error":{
            "err_code":-10002,
            "message":"Missing 302 Apikey",
            "message_cn":"缺少 302 API 密钥",
            "message_jp":"302 APIキーがありません",
            "type":"api_error"
        }}
    if not text:
            return {"error":{
            "err_code":-10016,
            "message":"Failed to get the text.",
            "message_cn":"获取切片文本失败",
            "message_jp":"テキストのスライス取得に失敗しました",
            "type":"text_error"
        }}
        
    base_url = Tools.config.kb.base_url
    api_configure = {"api_key":api_key,"base_url":base_url,"model_name":model_name}
    meta_chunking = functools.partial(lumberchunker,api_name ="openai",api_configure=api_configure,language=language,dynamic_merge=dynamic_merge)
    chunkgs_res = []
    text_dict = {}
    meta_chunks = await meta_chunking(text=text)
    text_dict["chunks_len"] = len(meta_chunks)
    text_dict["chunks"] = [[chunk] for chunk in meta_chunks]
    chunkgs_res.append(text_dict)
    return chunkgs_res


@app.get("/sync_model_param",summary="同步模型参数")
async def sync_model_param():
    sync_model = await sync_parameters()
    return suc_data(data=sync_model)


@app.get("/custom/model",summary="获取用户自定义模型")
async def get_user_custom_model(user: UserOut = Depends(get_current_active_user)):
    return await get_custom_models(user.uid)


@app.post("/custom/model",summary="新增用户自定义模型")
async def add_user_custom_model(request: Request,
                                custom_model: CustomModelCreate,
                                user: UserOut = Depends(get_current_active_user)):
    return await create_custom_model(user.uid, custom_model)

@app.get("/custom/model/{model_id}",summary="获取用户自定义模型")
async def get_user_custom_model(request: Request,
                                model_id: int,
                                user: UserOut = Depends(get_current_active_user)):
    return await get_custom_model(user.uid, model_id)

@app.put("/custom/model/{model_id}",summary="修改用户自定义模型")
async def update_user_custom_model(request: Request,
                                   model_id: int,
                                   custom_model: CustomModelUpdate,
                                   user: UserOut = Depends(get_current_active_user)):
    return await update_custom_model(user.uid, model_id, custom_model)


@app.delete("/custom/model/{model_id}",summary="删除用户自定义模型")
async def delete_user_custom_model(request: Request,
                                   model_id: int,
                                   user: UserOut = Depends(get_current_active_user)):
    return await delete_custom_model(user.uid, model_id)