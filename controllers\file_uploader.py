import datetime
from typing import Optional, Union
import os
import uuid
import aiofiles
import aiohttp
import mimetypes
import chardet
from pathlib import Path
from PIL import Image
from io import BytesIO
from fastapi import UploadFile
from controllers.gpt import upload, upload_blob
from utils import Tools
import utils

class FileProcessor:
    """文件处理类"""
    
    @staticmethod
    def get_file_extension(content_type: str) -> str:
        """根据Content-Type获取文件后缀"""
        extension = mimetypes.guess_extension(content_type)
        extension = extension.rsplit(".")[-1]
        Tools.log.debug(f"获取文件扩展名: content_type={content_type}, extension={extension}")
        return extension if extension else ''

    @staticmethod
    def get_image_format(content: bytes) -> Optional[str]:
        """获取图片格式"""
        try:
            img = Image.open(BytesIO(content))
            format = img.format.lower()
            Tools.log.debug(f"获取图片格式: format={format}")
            return format
        except Exception as e:
            Tools.log.error(f"获取图片格式失败: {e}")
            return None

    @staticmethod
    def detect_encoding(content: bytes) -> str:
        """检测文件编码
        
        Args:
            content: 文件内容字节
            
        Returns:
            str: 检测到的编码名称，如果检测失败则返回 'utf-8'
        """
        try:
            result = chardet.detect(content)
            encoding = result['encoding'] if result['confidence'] > 0.6 else 'utf-8'
            Tools.log.debug(f"检测文件编码: encoding={encoding}, confidence={result['confidence']}")
            return encoding
        except Exception as e:
            Tools.log.error(f"检测文件编码失败: {e}")
            return 'utf-8'

    @staticmethod
    def generate_file_path(original_name: str) -> tuple:
        """生成文件路径"""
        today = datetime.datetime.now().strftime("%Y%m%d")
        file_path_without_extension = os.path.join('tmp', uuid.uuid4().hex.replace("-", '').lower())
        if "." in original_name:
            extension = original_name.rsplit(".", 1)[-1]
        else:
            extension = 'png'
        file_path = f"{file_path_without_extension}.{extension}"
        Tools.log.debug(f"生成文件路径: original_name={original_name}, file_path={file_path}")
        return file_path, today

    @staticmethod
    def get_content_type(filename: str) -> str:
        """根据文件名获取Content-Type
        
        Args:
            filename: 文件名
            
        Returns:
            str: Content-Type，如果无法判断则返回 'application/octet-stream'
        """
        content_type, _ = mimetypes.guess_type(filename)
        if not content_type:
            # 为常见文本文件设置默认Content-Type
            ext = filename.lower().split('.')[-1] if '.' in filename else ''
            text_extensions = {
                'md': 'text/markdown',
                'txt': 'text/plain',
                'json': 'application/json',
                'xml': 'text/xml',
                'html': 'text/html',
                'htm': 'text/html',
                'css': 'text/css',
                'js': 'text/javascript'
            }
            content_type = text_extensions.get(ext, 'application/octet-stream')
        return content_type

class ImageCompressor:
    """图片压缩类"""
    
    def __init__(self, max_size_bytes: int = 1000000, initial_quality: int = 85):
        self.max_size_bytes = max_size_bytes
        self.initial_quality = initial_quality
        Tools.log.debug(f"初始化图片压缩器: max_size_bytes={max_size_bytes}, initial_quality={initial_quality}")

    async def compress_content(self, content: bytes, file_name: str) -> tuple[bytes, str]:
        """压缩图片内容
        
        Args:
            content: 图片内容
            file_name: 文件名
            
        Returns:
            tuple: (压缩后的内容, 新的文件名)
        """
        try:
            if file_name.lower().endswith(".png"):
                compressed = await utils.compress_png_content(
                    content,
                    max_size_bytes=self.max_size_bytes,
                    initial_quality=self.initial_quality
                )
                new_name = f"{file_name.rsplit('.', 1)[0]}.jpg"  # PNG会被压缩为JPG
                return compressed, new_name
            elif file_name.lower().endswith((".jpg", ".jpeg")):
                compressed = await utils.compress_jpg_content(
                    content,
                    max_size_bytes=self.max_size_bytes,
                    initial_quality=self.initial_quality
                )
                return compressed, file_name
            return content, file_name
        except Exception as e:
            Tools.log.error(f"压缩图片内容失败: error={e}")
            return content, file_name

class FileUploader:
    """文件上传类"""
    
    def __init__(self, prefix: str = "imgs"):
        self.prefix = prefix
        self.file_processor = FileProcessor()
        self.compressor = ImageCompressor()
        Tools.log.info(f"初始化文件上传器: prefix={prefix}")

    async def get_url_content(self, url: str) -> tuple[bytes, str, str]:
        """获取URL内容"""
        Tools.log.info(f"开始获取URL内容: url={url}")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        error_msg = f"获取URL内容失败: status={response.status}"
                        Tools.log.error(error_msg)
                        raise Exception(error_msg)
                    
                    content = await response.read()
                    content_type = response.headers.get('Content-Type', '')
                    file_extension = self.file_processor.get_file_extension(content_type)
                    
                    # 生成文件名
                    file_name = f"{uuid.uuid4().hex}.{file_extension}" if file_extension else f"{uuid.uuid4().hex}.png"
                    
                    Tools.log.info(f"URL内容获取完成: content_length={len(content)}")
                    return content, file_name, content_type
        except Exception as e:
            Tools.log.error(f"获取URL内容失败: url={url}, error={e}")
            raise

    async def process_upload(self, 
                           file: Optional[UploadFile] = None, 
                           url: str = '', 
                           need_compress: bool = False) -> dict:
        """处理文件上传"""
        try:
            if file:
                content = await file.read()
                name = file.filename
                # 检测文件编码
                encoding = self.file_processor.detect_encoding(content)
                Tools.log.info(f"检测到文件编码: {encoding}")
                
                # 获取正确的Content-Type
                content_type = self.file_processor.get_content_type(name)
                Tools.log.info(f"文件Content-Type: {content_type}")
                
                # 检查并更新文件扩展名
                img_format = self.file_processor.get_image_format(content)
                if img_format and not name.lower().endswith(img_format):
                    name = f"{name.rsplit('.', 1)[0]}.{img_format}"
                    Tools.log.debug(f"更新文件格式: new_name={name}")
                
                # 压缩处理
                if need_compress:
                    Tools.log.info(f"开始压缩文件内容")
                    content, name = await self.compressor.compress_content(content, name)
                
                # 上传文件
                Tools.log.info(f"开始上传文件到blob: file_name={name}")
                upload_url = await upload_blob(
                    content, 
                    file_name=name, 
                    pre=self.prefix, 
                    proxy_blob=True, 
                    encoding=encoding,
                    content_type=content_type
                )
                Tools.log.info(f"文件上传完成: upload_url={upload_url}")
                return {"url": upload_url, "encoding": encoding}
            
            # 处理URL上传
            elif url:
                Tools.log.info(f"开始处理URL上传: url={url}")
                content, file_name, content_type = await self.get_url_content(url)
                
                # 检测文件编码
                encoding = self.file_processor.detect_encoding(content)
                
                # 压缩处理
                if need_compress:
                    Tools.log.info(f"开始压缩URL文件内容")
                    content, file_name = await self.compressor.compress_content(content, file_name)
                
                # 获取正确的Content-Type
                content_type = self.file_processor.get_content_type(file_name)
                
                # 上传文件
                Tools.log.info(f"开始上传URL文件: file_name={file_name}")
                upload_url = await upload_blob(
                    content, 
                    file_name=file_name, 
                    pre=self.prefix, 
                    proxy_blob=True,
                    encoding=encoding,
                    content_type=content_type
                )
                Tools.log.info(f"URL文件上传完成: upload_url={upload_url}")
                return {"url": upload_url, "encoding": encoding}
            
            return {"url": ""}
            
        except Exception as e:
            Tools.log.error(f"文件上传处理失败: error={e}")
            return {"url": ""}
        finally:
            if file:
                await file.close()
                Tools.log.debug("文件句柄已关闭") 