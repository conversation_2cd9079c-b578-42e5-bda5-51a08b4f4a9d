# -*- coding: utf-8 -*-
from fastapi.params import Form, Query
from pydantic import BaseModel, EmailStr


class Base():
    def to_dict(self):
        return self.__dict__


class ResetPW(BaseModel):
    password: str


class Nmae(BaseModel):
    name: str


class UserBody(BaseModel):
    email: EmailStr=''
    password: str
    name: str=''
    from_invite_code:str=""
    phone: str=''
    captcha: str
    code: str
    email_code:str
    ref: str=''
    is_gpt: bool= False

    def to_dict(self):
        return self.model_dump()


class UserIn(Base):

    def __init__(self, password=Query(...), name=Query(...), email=Query(''),phone='',region=Query(0),lang=Query("zh-CN",title="语言",description=""),
                 from_invite_code=Query(""),captcha=Query(...,description="验证码"),code=Query(...,description="验证码对应的code"),ref=Query("",title="来源"),is_gpt='',user_id=''):
        self.email = email
        self.password = password
        self.name = name
        self.from_invite_code = from_invite_code
        self.captcha = captcha
        self.code = code
        self.ref=ref
        self.is_gpt=is_gpt
        self.phone=phone
        self.region=region
        self.lang=lang
        self.user_id=user_id

class UserIn_Body(Base):

    def __init__(self, password='', name='', email='',region=0,user_id='',
                 from_invite_code='',captcha='',code='',ref='',is_gpt='',phone=''):
        self.email = email
        self.password = password
        self.name = name
        self.from_invite_code = from_invite_code
        self.captcha = captcha
        self.code = code
        self.ref=ref
        self.is_gpt=is_gpt
        self.phone=phone
        self.region=region
        self.user_id=user_id

class UserInModel(BaseModel):
    email: str
    password: str
    name: str
    from_invite_code: str = None


class UserLoginIn(BaseModel):
    email: str = ""
    phone:str = ""
    password: str="test"
    ref:str = ""
    event:str = ""
    captcha:str = ""
    code:str = ""
    login_from: str = 'proxy302'

class UserRegion(BaseModel):
    region:int = 0


class UserOut(BaseModel
              ):
    uid: int=0
    name: str = None
    email: str=""
    is_use: bool = False
    deleted_on: int = 0
    created_on: int=0
    use_api: int = 0
    salt: str=""
    invite_code: str=""
    password: str=""

if __name__ == '__main__':
    x = UserBody()
    print(x.to_dict())