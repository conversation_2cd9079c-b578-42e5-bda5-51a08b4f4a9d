# -*- coding: utf-8 -*-
# @Time    : 2024/8/27 10:28
# <AUTHOR> hzx1994
# @File    : Weichat.py
# @Software: PyCharm
# @description:

import hashlib

import aiohttp
import requests
import secrets

from utils import cache

WX_BASE_URL = 'https://api.weixin.qq.com/cgi-bin'
APP_ID = 'wx3e1f5e4b2198371d'
APP_SECRET = '7dab9ba283348fa1090b98c8e8e9e57e'


def generate_sha1(string):
    """Generate SHA1 hash for the given string."""
    return hashlib.sha1(string.encode('utf-8')).hexdigest()


def get_access_token(app_id, app_secret):
    """Get Access Token from WeChat API."""
    if not app_id or not app_secret:
        return None
    url = f"{WX_BASE_URL}/token?grant_type=client_credential&appid={app_id}&secret={app_secret}"
    response = requests.get(url)
    data = response.json()
    access_token = data.get('access_token')
    print('getAccessToken:', data, access_token)
    return access_token

@cache(ttl=60*5)
async def get_signature(url):
    """Get signature information for a given URL."""
    if not url:
        return None
    access_token = get_access_token(APP_ID, APP_SECRET)
    if not access_token:
        return None
    ticket_url = f"{WX_BASE_URL}/ticket/getticket?access_token={access_token}&type=jsapi"
    async with aiohttp.ClientSession() as session:
        async with session.get(ticket_url) as resp:
            ticket_data = await resp.json()
        # response = requests.get(ticket_url)
        # ticket_data = response.json()
        jsapi_ticket = ticket_data.get('ticket')

        # async with session.get('http://worldtimeapi.org/api/timezone/Etc/UTC') as resp:
        #     r_js = await resp.json()
        import time
        timestamp = int(time.time())
        nonce_str = secrets.token_hex(8)  # Generate a random string of 16 characters
        string_to_sign = f"jsapi_ticket={jsapi_ticket}&noncestr={nonce_str}&timestamp={timestamp}&url={url}"
        signature = generate_sha1(string_to_sign)

    result = {
        'nonceStr': nonce_str,
        'jsapi_ticket': jsapi_ticket,
        'timestamp': timestamp,
        'url': url,
        'signature': signature,
        'appId':APP_ID
    }


    return result


# Example usage
if __name__ == '__main__':
    example_url = 'https://example.com'
    get_signature(example_url)
