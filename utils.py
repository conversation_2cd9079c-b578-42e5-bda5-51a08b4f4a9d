import asyncio
import base64
import functools
import hashlib
import io
import json
import logging
import mimetypes
import pickle
import random
import socket
import string
import time
from binascii import hexlify
from datetime import datetime
from hashlib import md5
from pathlib import Path
import aiohttp
import firebase_admin
import pytz
import qrcode
from fastapi import UploadFile
from firebase_admin import auth
from firebase_admin import credentials
import stripe
from deprecated.sphinx import deprecated
import aioredis
from loguru import logger
from passlib.context import CryptContext
from ronglian_sms_sdk import SmsSDK
from starlette.requests import Request
from PIL import Image
import io

from conf.constants import PartnerShareConfig
from exections import ComstomException
from libs.Email import Emailer, SendGridREmailer
from libs.FeiShu import FeiShu
from libs.Log import init_logger
from libs.MyCelery import MyCelery
from models.conf import Config, CacheSaveWhere
from models.response import StateCode
from pathlib import Path
from motor.motor_asyncio import AsyncIOMotorClient

class Tools:
    config: Config = None
    base_dir = None
    celery: MyCelery = None
    vm_celery: MyCelery = None
    redis: aioredis.Redis = None
    redis_14: aioredis.Redis = None
    redis_us: aioredis.Redis = None

    log = logger
    mo:AsyncIOMotorClient = None

    # 邮箱
    email: Emailer = None
    email_for_gpt: Emailer = None
    # 飞书
    feishu1: FeiShu = None
    feishu2: FeiShu = None
    feishu3: FeiShu = None
    feishu4: FeiShu = None
    feishu_302ai: FeiShu = None
    cache_img: list = []


def current_timestamp():
    return int(time.time())

def load_stripe_key():
    stripe.api_key = Tools.config.stripe.sk

    PartnerShareConfig.data_return_key = Tools.config.partner_share.data_return_key
    PartnerShareConfig.user_integration_key = Tools.config.partner_share.user_integration_key
    PartnerShareConfig.product_id = Tools.config.partner_share.product_id


def load_feishu():
    Tools.feishu1 = FeiShu(Tools.config.feishu.get("id_1"))
    Tools.feishu2 = FeiShu(Tools.config.feishu.get("id_2"))
    Tools.feishu3 = FeiShu(Tools.config.feishu.get("id_3"))
    Tools.feishu4 = FeiShu(Tools.config.feishu.get("id_4"))
    Tools.feishu_302ai = FeiShu(Tools.config.feishu.get("id_302ai","7b238bfe-34c7-47b0-9c0b-8edd896d3fe7"))


async def create_celery():
    Tools.log.debug(Tools.config.celery.dict())
    Tools.celery = MyCelery('proxy302', **Tools.config.celery.dict())
    vm_conf = Tools.config.celery.dict()
    vm_conf['backend'] = vm_conf['backend'][:-1]+"4"
    vm_conf['broker'] = vm_conf['broker'][:-1]+"4"
    Tools.vm_celery = MyCelery('proxy302-vm', **vm_conf)


async def load_redis():
    Tools.redis = await aioredis.from_url(
        Tools.config.redis_url
    )
    Tools.redis_14 = await aioredis.from_url(
        Tools.config.redis_url_14
    )
    Tools.redis_us = await aioredis.from_url(
        "redis://10.1.0.5:6379/0"
    )
    load_email()

async def load_mongo():
    Tools.mo = AsyncIOMotorClient(Tools.config.mongo_url)

def timestamp_to_date(timestamp, timezone='Asia/Shanghai', date_format="%Y-%m-%d"):
    """
    指定时区把时间戳转日期字符串，可以指定返回格式
    """
    # 创建一个UTC时间
    utc_time = datetime.utcfromtimestamp(timestamp)

    # 获取指定的时区
    tz = pytz.timezone(timezone)

    # 将UTC时间转换为指定时区的时间
    local_time = utc_time.replace(tzinfo=pytz.utc).astimezone(tz)

    # 格式化输出
    return local_time.strftime(date_format)



def get_diff_time_from_zone(zone:str):

    utc_timezone = pytz.timezone('UTC')
    if zone == 'Etc/Unknown':
        return 0
    china_timezone = pytz.timezone(zone)

    naive_time = datetime(2023, 6, 8, 12, 0, 0)
    utc_time = utc_timezone.localize(naive_time)
    china_time = china_timezone.localize(naive_time)

    time_difference = int(utc_time.timestamp() - china_time.timestamp())
    return time_difference

async def close_redis():
    await Tools.redis.close()

def load_email():
    api_key = Tools.config.sendgrid.get("api_key")
    from_email = Tools.config.sendgrid.get("from")
    from_gpt_email = Tools.config.sendgrid.get("from_gpt","<EMAIL>")
    Tools.email = SendGridREmailer(api_key, from_email)
    Tools.email_for_gpt = SendGridREmailer(api_key, from_gpt_email)


def is_from_gpt(requests:Request,must_true=False):
    if 'gpt302' in requests.headers.get("host")  or 'ai' in requests.headers.get("host"):
        return True
    if requests.headers.get("Isgpt"):
        return True
    return False

def init_loger(log_path: Path, err_path: Path,level = logging.DEBUG):
    """
    初始化日志
    """
    Tools.log = logger
    if not log_path.parent.exists():
        log_path.parent.mkdir()
    if not err_path.parent.exists():
        err_path.parent.mkdir()
    init_logger(level, log_path, err_path)


# ip转换
def int2ip(ip: int) -> str:
    """
    整型转ip
    :param ip:
    :return:
    """

    if not ip:
        return ''
    return '.'.join([str(ip >> offset & 0xff) for offset in (24, 16, 8, 0)])


def ip2int(ip: str) -> int:
    """
    ip转整型
    :param ip:
    :return:
    """
    return sum([int(ip.split('.')[i]) << [24, 16, 8, 0][i] for i in range(4)])


def ip2hex(ip: str) -> str:
    """
    ip转16进制
    :param ip:
    :return:
    """
    packed_ip_addr = socket.inet_aton(ip)
    hexStr = hexlify(packed_ip_addr)
    return hexStr.decode().upper()


# 弃用
@deprecated(version='1.0', reason="This function will be removed soon")
def encryption_password_or_decode(pwd: str, hashed_password: str = None):
    """
    密码加密或解密
    :param pwd: 原始密码
    :param hashed_password: 加密后的密码
    :return:
    """
    encryption_pwd = CryptContext(
        schemes=["sha256_crypt", "md5_crypt", "des_crypt"]
    )

    def encryption_password():
        password = encryption_pwd.hash(pwd)
        return password

    def decode_password():
        password = encryption_pwd.verify(pwd, hashed_password)
        return password

    return decode_password() if hashed_password else encryption_password()


# 随机生成邀请码
def generate_invite_code(email: str, num=8):
    import random
    import string
    pre = base64.b64encode(email.encode("utf-8")).decode()[0:4]
    return pre + ''.join(random.sample(string.ascii_letters + string.digits, num))


def get_hash_password(password: str, salt: str, created_on: str) -> str:
    """
    生成md5后的密码，用于数据库存储
    :param password:
    :param salt:
    :param created_on:
    :return:
    """
    f = md5((md5(password.encode()).hexdigest() + md5(salt.encode()).hexdigest()).
            encode()).hexdigest()
    f += md5(str(created_on).encode()).hexdigest()
    return md5(f.encode()).hexdigest()


def random_string(length: int, number: bool = True, uppercase: bool = True, lowercase: bool = True) -> str:
    """
    随机字符串
    :param length:
    :param number:
    :param uppercase:
    :param lowercase:
    :return:
    """
    if type(length) != int:
        raise TypeError("length must be int")
    scope = ""
    if number:
        scope += string.digits
    if uppercase:
        scope += string.ascii_uppercase
    if lowercase:
        scope += string.ascii_lowercase
    if scope:
        return ''.join(random.choice(scope) for _ in range(length))
    else:
        raise ValueError("number / uppercase / lowercase not all False")


def to_ptc(value: int,number=2):
    if not value:
        return 0
    return round(value / 1000, number)


import asyncio
import aiofiles
from PIL import Image
import io
import os

def get_extension_from_content_type(content_type):
    # 确保 mimetypes 数据库已初始化
    mimetypes.init()

    # 获取对应的文件扩展名
    extension = mimetypes.guess_extension(content_type)

    # 如果找不到对应的扩展名，返回 None
    if extension is None:
        return None

    # 移除前面的点号
    return extension.lstrip('.')
async def compress_jpg_async(input_path='', output_folder='', max_size_bytes=1000000, initial_quality=85,content=None):
    """
    异步压缩 JPG 图像，保存到本地，并确保大小小于指定大小

    :param input_path: 输入 JPG 文件的路径
    :param output_folder: 输出文件夹的路径
    :param max_size_bytes: 最大允许的文件大小（字节），默认1MB
    :param initial_quality: 初始 JPEG 质量，范围为1-95，默认为85
    :return: 压缩后的 JPG 文件保存路径
    """
    if not content:
        async with aiofiles.open(input_path, mode='rb') as f:
            content = await f.read()

    img = Image.open(io.BytesIO(content))

    filename = os.path.basename(input_path)
    output_path = os.path.join(output_folder, f"compressed_{filename}")
    try:
        os.remove(input_path)
    except:
        pass
    quality = initial_quality
    for i in range(10):
        img.save(output_path, 'JPEG', quality=quality, optimize=True)

        if os.path.getsize(output_path) <= max_size_bytes:
            return output_path

        quality -= 5
        if quality < 20:
            print(
                f"Warning: Could not reduce {input_path} below {max_size_bytes / 1000000:.2f}MB while maintaining acceptable quality.")
            return output_path
    return output_path

async def png_to_jpeg_async(input_path='', output_folder='', max_size_bytes=1000000, initial_quality=85,content=None):
    """
    异步将PNG图像转换为JPEG格式并保存到本地，确保大小小于指定大小

    :param input_path: 输入PNG文件的路径
    :param output_folder: 输出文件夹的路径
    :param max_size_bytes: 最大允许的文件大小（字节），默认1MB
    :param initial_quality: 初始JPEG质量，范围为1-95，默认为85
    :return: 转换后的JPEG文件保存路径
    """
    if not content:
        async with aiofiles.open(input_path, mode='rb') as f:
            content = await f.read()

    img = Image.open(io.BytesIO(content))

    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
        background = Image.new('RGB', img.size, (255, 255, 255))
        background.paste(img, mask=img.split()[3] if img.mode == 'RGBA' else None)
    else:
        background = img.convert('RGB')

    filename = os.path.splitext(os.path.basename(input_path))[0] + '.jpg'
    output_path = os.path.join(output_folder, filename)
    Tools.log.info(f"png_to_jpeg_async output_path: {output_path}")
    try:
        os.remove(input_path)
    except:
        pass
    quality = initial_quality
    for i in range(10):
        background.save(output_path, 'JPEG', quality=quality, optimize=True)

        if os.path.getsize(output_path) <= max_size_bytes:
            return output_path

        quality -= 5
        if quality < 20:
            print(
                f"Warning: Could not reduce {input_path} below {max_size_bytes / 1000000:.2f}MB while maintaining acceptable quality.")
            return output_path
    return output_path

def load_func(func=None):
    def async_decorators(method):
        """
        异步装饰器装饰异步方法
        :param method: 被装饰协程（异步方法）
        :return:
        """

        @functools.wraps(method)
        async def wrapper(*args, **kwargs):
            # 此处必须await 切换调用被装饰协程，否则不会运行该协程
            st = time.time()
            result = await method(*args, **kwargs)
            cost_time = round(time.time() - st, 2)
            Tools.log.debug(f"{method.__name__} cost_time {cost_time}")
            if func:
                await func(*args, name=method.__name__, cost_time=cost_time, result=result, **kwargs)
            return result

        return wrapper

    return async_decorators


# 全局缓存使用的字典
cache_dict = {}


def cache(where=CacheSaveWhere.redis, ttl=3600*2):
    def async_decorators(method):
        """
            异步装饰器装饰异步方法
            :param method: 被装饰协程（异步方法）
            :return:
            """


        @functools.wraps(method)
        async def wrapper(*args, **kwargs):
            st = time.time()
            cache_key = f"proxy302_cache_{method.__name__}_"+hashlib.md5(pickle.dumps(args) + pickle.dumps(kwargs)).hexdigest()
            Tools.log.debug(f"{method.__name__} start cache_key:{cache_key}")

            if where == CacheSaveWhere.local:
                result = cache_dict.get(cache_key)
                if result is None:

                    # 此处必须await 切换调用被装饰协程，否则不会运行该协程
                    result = await method(*args, **kwargs)
                    cache_dict[cache_key] = result
            else:
                result = await Tools.redis.get(cache_key)
                if not result:
                    result = await method(*args, **kwargs)
                    await Tools.redis.setex(cache_key, ttl, pickle.dumps(result))
                else:
                    result = pickle.loads(result)
            ed = time.time()
            Tools.log.debug(f"{method.__name__} cost_time {round(ed - st, 2)} s")
            return result

        return wrapper

    return async_decorators


proxy_json_path = Path('conf')/'proxy302_firebase.json'
gpt_json_path = Path('conf')/'gpt302_firebase.json'
cred = credentials.Certificate(proxy_json_path)
gpt_cred = credentials.Certificate(gpt_json_path)
proxy = firebase_admin.initialize_app(cred,name='proxy')
gpt = firebase_admin.initialize_app(gpt_cred,name="gpt")

def get_info_from_fire_google(id_token:str,is_gpt=False):


    # 从请求中获取ID令牌
    # id_token = 'eyJhbGciOiJSUzI1NiIsImtpZCI6ImViYzIwNzkzNTQ1NzExODNkNzFjZWJlZDI5YzU1YmVmMjdhZDJjY2IiLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.M4iFQYchbQQ--ePEO8m1pOezwzx7ExEwCxxaJiRnbXd2MNbHKeBy4Xvp8M_BrDu4uWt8bpDCdg2xaj5xs0b7DH6dUqRdb2QX7-6ws_EQseTss2YcZ1nFx13CAPBdIstxwQq0oG1DSF3t6pLR4SrPihZi-8IbXass4JJNrzMRwXjvPpWsClolt2cKn8-x9RUxDBrZEx3j6tyu3KSL7sk1FKvWNdU4lNVkuGI0y7WKQnDUdo7FNu6dJs5hoJFv7GycrG1HMTAHhLwYrIXCFwzsuF845q5LzDCTypZk1mRcLPP9Q-k1BbP59d-pUY58RzB_-ckahyWacmDzgbIYKD4G4Q'

    try:
        decoded_token = auth.verify_id_token(id_token,app=gpt if is_gpt else proxy)
        # 验证成功，获取用户的UID，可以用于进一步的用户管理或业务逻辑
        return decoded_token
    except firebase_admin.auth.InvalidIdTokenError as e:
        # 处理令牌无效的情况
        Tools.log.error("error InvalidIdTokenError ")
        Tools.log.error(e)
    except Exception as e:
        Tools.log.error(e)
    return None
async def get_user_info_from_fire(id_token:str,is_gpt=False):
    # 使用服务账户密钥初始化Firebase应用
    loop = asyncio.get_event_loop()
    await asyncio.sleep(2)
    return await loop.run_in_executor(None, functools.partial(get_info_from_fire_google, id_token,is_gpt=is_gpt))


async def sms(action:str='get',mobile='',is_gpt=False,**kwargs):
    DAYTIME = 24*3600
    ip = kwargs.get("ip",'')
    if not mobile:
        Tools.log.error(f"send sms error mobile is empty")
        return False
    key = "SMS_"

    mobile = mobile.replace("+86","").replace(" ","")
    async def send_message():
        if await Tools.redis.get(key+mobile+"lock"):
            Tools.log.error(f"send sms error SMS_LOCK_{mobile} is lock")
            return False

        ip_lock_key = f"SMS_LOCK_{ip}"
        phone_key = f"SMS_LOCK_{mobile.replace(' ', '')}"
        if value := ip and await Tools.redis.get(ip_lock_key):
            if int(value.decode()) >= 3:
                await Tools.redis.incr(ip_lock_key)
                Tools.log.error(f"send sms error {ip} 频繁发送短信")
                return False
        else:
            await Tools.redis.setex(ip_lock_key, DAYTIME, 1)

        if value := await Tools.redis.get(phone_key):
            if int(value.decode()) >= 3:
                await Tools.redis.incr(phone_key)
                Tools.log.error(f"send sms error {phone_key} 频繁发送短信")
                return False
        else:
            await Tools.redis.setex(phone_key, DAYTIME, 1)


        await Tools.redis.incr(phone_key)
        await Tools.redis.incr(ip_lock_key)

        accId = Tools.config.rly_conf.get("acc_id")
        accToken = Tools.config.rly_conf.get("acc_token")
        app_name = "app_id" if not is_gpt else 'gpt_app_id'
        tid_name = "tid" if not is_gpt else 'gpt_tid'
        appId = Tools.config.rly_conf.get(app_name)
        tid = Tools.config.rly_conf.get(tid_name)

        sdk = SmsSDK(accId, accToken, appId)
        # mobile = '13662379450'
        value = random.randint(1000, 9999)

        await Tools.redis.setex(key+mobile+"lock",60*1,1)
        await Tools.redis.setex(key+mobile,60*10,value)
        datas = (value,'10分钟')
        resp = sdk.sendMessage(tid, mobile, datas)
        dict_resp = json.loads(resp)
        statusCode = dict_resp.get("statusCode")
        if statusCode == "000000":
            return True
        if statusCode == "160040":
            raise ComstomException(detail='发送次数已超上限',code=StateCode.CaptchaLimitError.value)
        else:
            Tools.log.error(f"send sms error {resp}")

    async def get_code():
        value = await Tools.redis.get(key+mobile)
        if not value:
            return None
        return value.decode()

    if action=='get':
        return await get_code()
    else:
        return await send_message()

def text_to_qrcode_to_b64(text=''):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=1,
    )
    qr.make(fit=True)
    qr.add_data(text)
    img = qr.make_image()

    buf = io.BytesIO()
    img.save(buf, format='PNG')
    image_stream = buf.getvalue()
    heximage = base64.b64encode(image_stream)
    return 'data:image/png;base64,' + heximage.decode()

# 获取url的域名
def get_domain(url):
    url = url.replace("http://", "").replace("https://", "")
    if "/" in url:
        url = url.split("/")[0]
    return url

def get_path(url):
    url = url.replace("http://", "").replace("https://", "")
    if "/" in url:
        url = url.split("/",maxsplit=1)[-1]
        return url
    return ""
def get_region(url):
    if not url:
        return 1
    domain = get_domain(url)
    if domain == Tools.config.gpt_dashboard_host or 'cn' not in domain:
        return 1
    if domain == Tools.config.gpt_dashboard_cn_host:
        return 0
    return 0

async def cache_img(file_path:Path):
    async with Tools.redis.lock("cache_img_lock",sleep=10):
        if file_path in Tools.cache_img:
            return
        tmp = []
        for p in file_path.iterdir():
            if p.is_file() and p.suffix == '.png'   :
                with open(p,'rb') as f:
                    tmp.append(pickle.dumps((p.name,f.read())))
        if await Tools.redis.keys("cache_img"):
            return
        await Tools.redis.lpush("cache_img",*tmp)


def create_images(file_path:Path=Path('static','imgs')):
    """
    启动任务的时候执行
    """
    if not Path('tmp').exists():
        Path('tmp').mkdir()

    # 确保目录存在
    if not file_path.exists():
        file_path.mkdir(parents=True)
        
    # 只要目录下有一个文件就返回
    try:
        next(file_path.iterdir())
        return
    except StopIteration:
        pass  # 目录为空，继续创建图片

    def job():
        image = ImageCaptcha(fonts=['verdanaz.ttf'], width=150, height=60)
        value = random.randint(0, 999999)
        name = hashlib.md5(str(value).encode()).hexdigest()
        image.write("{:0>6}".format(value), file_path / f'{name}.png')
    from concurrent.futures import ThreadPoolExecutor,wait
    from captcha.image import ImageCaptcha
    with ThreadPoolExecutor(max_workers=30) as pool:
        futures = [pool.submit(job) for _ in range(1000)]
        wait(futures)


def generate_sign(params: dict, timestamp: str, secret_key: str) -> str:
    """
    生成请求签名
    Args:
        params: 请求参数字典
        timestamp: 请求时间戳
        secret_key: 密钥
    Returns:
        str: 生成的签名
    """
    # 提取所有键名并转换为小写
    lowercase_keys = [k.lower() for k in params.keys()]
    
    # 按自然顺序对键名排序
    lowercase_keys.sort()
    
    # 拼接排序后的键名
    key_string = "&".join(lowercase_keys)
    
    # 拼接时间戳和密钥
    sign_string = f"{key_string}{timestamp}{secret_key}"
    
    # 生成签名 (使用 SHA256 算法)
    import hashlib
    sign = hashlib.sha256(sign_string.encode()).hexdigest()
    
    return sign


async def get_partner_share_auth_code(base_url:str,user_id: str, product_key: str, secret_key:str,target_product_key:str, extra: dict = None) -> str:
    """
    获取PartnerShare授权码
    Args:
        user_id: 用户ID
        product_key: 应用标识
        extra: 额外用户信息
    Returns:
        str: 授权码
    """
    timestamp = str(int(time.time()))
    
    data = {
        "user_id": str(user_id),
        "product_key": product_key,
        "target_product_key": target_product_key
    }
    
    if extra:
        data["extra"] = extra
        
    # 生成签名
    sign = generate_sign(data, timestamp, secret_key)
    
    headers = {
        "Content-Type": "application/json",
        "x-Product-Key": product_key,
        "x-Timestamp": timestamp,
        "x-Sign": sign
    }
    print(product_key)
    url = f"{base_url}/open/api/oauth/getAuthorizationCode"
    
    try:
        async with aiohttp.ClientSession() as session:
            Tools.log.debug(f"Get partner share auth code data: {data}")
            Tools.log.debug(f"Get partner share auth code headers: {headers}")
            async with session.post(url, json=data, headers=headers) as resp:
                print(resp.headers)
                result = await resp.json()
                if result.get("code") == 0:
                    print(result)
                    return result["data"]["code"]
                else:
                    Tools.log.error(f"Get partner share auth code failed: {result}")
                    return None
    except Exception as e:
        Tools.log.error(f"Get partner share auth code error: {str(e)}")
        return None

async def get_partner_share_user_info( code: str, product_key: str='phh3Hkj8u83',base_url: str='https://testing-api.partnershare.net',secret_key:str='Z8C8PcyM1bzOkuhkjm31Mp3Vh') -> dict:
    """
    通过授权码获取用户信息
    Args:
        base_url: 基础URL
        code: 授权码
        product_key: 应用标识
    Returns:
        dict: 用户信息
    """
    timestamp = str(int(time.time()))
    
    data = {
        "code": code
    }
    
    # 生成签名
    sign = generate_sign(data, timestamp, secret_key)
    
    headers = {
        "Content-Type": "application/json", 
        "x-Product-Key": product_key,
        "x-Timestamp": timestamp,
        "x-Sign": sign
    }
    
    url = f"{base_url}/open/api/oauth/getUserByAuthorizationCode"
    
    try:
        async with aiohttp.ClientSession() as session:
            Tools.log.debug(f"Get partner share user info data: {data}")
            Tools.log.debug(f"Get partner share user info headers: {headers}")
            async with session.post(url, json=data, headers=headers) as resp:
                result = await resp.json()
                Tools.log.info(f"Get partner share user info result: {result}")
                if result.get("code") == 0:
                    #  {'user_key': 'iwj76O11p2ou1dOPu6Rnihcj5iBd', 'user_name': '来自AdsPower的授权用户iwj76O11', 'ref': 'AdsPower', 'ref_product_key': 'K20xon3htdg', 'locale': 'zh', 'user_id': '2861912'}
                    Tools.log.info(f"Get partner share user info result: {result['data']}")
                    return result["data"]
                else:
                    Tools.log.error(f"Get partner share user info failed: {result}")
                    return None
    except Exception as e:
        Tools.log.error(f"Get partner share user info error: {str(e)}")
        return None

async def notify_partner_share_user_bind( user_key: str, user_id: str, product_key: str='phh3Hkj8u83',base_url: str='https://testing-api.partnershare.net',secret_key:str='Z8C8PcyM1bzOkuhkjm31Mp3Vh') -> dict:
    """
    通知PartnerShare完成用户绑定
    Args:
        base_url: 基础URL
        user_key: 授权用户唯一标识
        user_id: 被授权方用户唯一标识
        product_key: 应用标识
    Returns:
        dict: 绑定结果
    """
    timestamp = str(int(time.time()))
    
    data = {
        "user_key": user_key,
        "user_id": str(user_id)
    }
    
    # 生成签名
    sign = generate_sign(data, timestamp, secret_key)
    
    headers = {
        "Content-Type": "application/json",
        "x-Product-Key": product_key, 
        "x-Timestamp": timestamp,
        "x-Sign": sign
    }
    
    url = f"{base_url}/open/api/tracer/relavanceOauthUser"
    
    try:
        async with aiohttp.ClientSession() as session:
            Tools.log.debug(f"Notify partner share user bind data: {data}")
            Tools.log.debug(f"Notify partner share user bind headers: {headers}")
            async with session.post(url, json=data, headers=headers) as resp:
                result = await resp.json()
                Tools.log.debug(f"Notify partner share user bind result: {result}")
                if result.get("code") == 0:
                    return result["data"]
                else:
                    Tools.log.error(f"Notify partner share user bind failed: {result}")
                    return None
    except Exception as e:
        Tools.log.error(f"Notify partner share user bind error: {str(e)}")
        return None

# if __name__ == '__main__':
#     import asyncio
#     asyncio.run(get_partner_share_auth_code("https://testing-api.partnershare.net","10042","phh3Hkj8u83"))
#     # asyncio.run(get_partner_share_user_info("https://testing-api.partnershare.net","3m3xafyaih","phh3Hkj8u83"))
    # asyncio.run(notify_partner_share_user_bind("https://testing-api.partnershare.net","a52vnfdBbLMult3fAXIeGhUPgsHP","10043","phh3Hkj8u83"))

async def compress_jpg_content(content: bytes, max_size_bytes: int = 1000000, initial_quality: int = 85) -> bytes:
    """压缩JPG图片内容
    
    Args:
        content: 图片内容
        max_size_bytes: 目标文件大小（字节）
        initial_quality: 初始压缩质量
        
    Returns:
        bytes: 压缩后的图片内容
    """
    try:
        # 打开图片
        img = Image.open(io.BytesIO(content))
        
        # 如果不是JPEG，转换为JPEG
        if img.format != 'JPEG':
            # 创建一个白色背景
            background = Image.new('RGB', img.size, (255, 255, 255))
            # 如果图片有透明通道，需要合并到白色背景
            if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
                background.paste(img, mask=img.convert('RGBA').split()[3])
            else:
                background.paste(img)
            img = background
        
        # 初始质量
        quality = initial_quality
        
        while quality > 5:  # 最低质量限制
            # 保存到内存
            output = io.BytesIO()
            img.save(output, format='JPEG', quality=quality, optimize=True)
            compressed_content = output.getvalue()
            
            # 检查大小
            if len(compressed_content) <= max_size_bytes:
                Tools.log.debug(f"JPG压缩完成: quality={quality}, size={len(compressed_content)}")
                return compressed_content
                
            # 降低质量继续尝试
            quality -= 5
            
        # 如果达到最低质量还是太大，返回最后的结果
        Tools.log.warning(f"JPG压缩达到最低质量: quality=5, size={len(compressed_content)}")
        return compressed_content
        
    except Exception as e:
        Tools.log.error(f"压缩JPG内容失败: {e}")
        return content

async def compress_png_content(content: bytes, max_size_bytes: int = 1000000, initial_quality: int = 85) -> bytes:
    """压缩PNG图片内容，会转换为JPG格式
    
    Args:
        content: 图片内容
        max_size_bytes: 目标文件大小（字节）
        initial_quality: 初始压缩质量
        
    Returns:
        bytes: 压缩后的JPG图片内容
    """
    try:
        # 打开PNG图片
        img = Image.open(io.BytesIO(content))
        
        # 创建一个白色背景
        background = Image.new('RGB', img.size, (255, 255, 255))
        
        # 如果图片有透明通道，需要合并到白色背景
        if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
            background.paste(img, mask=img.convert('RGBA').split()[3])
        else:
            background.paste(img)
            
        # 转换为JPG并压缩
        output = io.BytesIO()
        background.save(output, format='JPEG', quality=initial_quality)
        return await compress_jpg_content(
            content=output.getvalue(),
            max_size_bytes=max_size_bytes,
            initial_quality=initial_quality
        )
        
    except Exception as e:
        Tools.log.error(f"压缩PNG内容失败: {e}")
        return content
