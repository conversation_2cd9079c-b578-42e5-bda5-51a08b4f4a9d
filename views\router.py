from fastapi import APIRouter

from models.response import BaseData
from views import proxy, user, api, static,gpt

router = APIRouter(responses={404: {"description": "Not found"},200:{"description":"success response","model":BaseData}})
"""
# 定义响应模型
class Item(BaseModel):
    id: int
    name: str
    description: str = None

# 创建一个 APIRouter 实例
router = APIRouter(
    responses={
        200: {
            "model": Item,  # 指定返回模型
            "description": "Successful Response - Item retrieved successfully.",
        },
        404: {"description": "Item not found"},
        500: {"description": "Internal server error"}
    }
)
"""

router.include_router(user.app,tags=["user"],prefix="/user")
router.include_router(proxy.app,tags=["proxy"],prefix="/proxy")
router.include_router(api.app,tags=["api"],prefix="/api")
router.include_router(static.app,tags=["static"],prefix="/static")
router.include_router(gpt.app,tags=["GPT"],prefix="/gpt/api")
router.include_router(gpt.gpt,tags=["GPT"],prefix="/bot")
router.include_router(gpt.kb,tags=["kb,知识库"],prefix="/kb")
