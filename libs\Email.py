# -*- coding: utf-8 -*-
# @Time    : 2023/9/6 18:43
# <AUTHOR> hzx1994
# @File    : Email.py
# @Software: PyCharm
# @description:
import smtplib
import traceback
from email.mime.text import MIMEText
import sendgrid
from sendgrid.helpers.mail import *



class Emailer:
    STYLE =  """
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>Document</title>
  <style>
    .body {
  width: 100%;
  position: relative;
}
.body .bg {
  width: 100%;
}
.body .bg img {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  display: block;
}
.body .container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  margin-top: -188px;
}
.body .container .logo {
  padding-top: 100px;
}
.body .container .logo img {
  display: block;
  width: 39%;
  max-width: 200px;
  margin: 0 auto;
}
.body .container h1 {
  font-size: 18px;
  margin-top: 50px;
  margin-bottom: 30px;
}
.body .container p {
  font-size: 15px;
}
.body .container a {
  display: inline-block;
  font-size: 15px;
  margin: 20px 0;
}
.body .container .btn {
  display: block;
  text-align: center;
  width: 100px;
  padding: 10px 15px;
  border-radius: 5px;
  background-color: #8e47f0;
  color: #fff;
  text-decoration: none;
  margin: 0 auto;
  margin-top: 30px;
}

  </style>
  
        
</head>
<body>
    <div class="body">
      <div class="bg">
        <!-- 背景图 -->
        <img src="https://proxyblob.blob.core.windows.net/gpt/imgs/emails/gpt.png" alt="">
      </div>
      <div class="container">
        <div class="logo">
          <!-- logo图 -->
          <img src="https://proxyblob.blob.core.windows.net/gpt/imgs/emails/img_v3_029q_11949078-a9a8-40c1-b818-99bfa3cf705g.png" alt="">
        </div>
        """

    PROXY_STYLE = """
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta http-equiv="X-UA-Compatible" content="IE=edge">
          <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
          <title>Document</title>
          <style>
            .body {
          width: 100%;
          position: relative;
        }
        .body .bg {
          width: 100%;
        }
        .body .bg img {
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
          display: block;
        }
        .body .container {
          width: 100%;
          max-width: 600px;
          margin: 0 auto;
          margin-top: -188px;
        }
        .body .container .logo {
          padding-top: 100px;
        }
        .body .container .logo img {
          display: block;
          width: 39%;
          max-width: 200px;
          margin: 0 auto;
        }
        .body .container h1 {
          font-size: 18px;
          margin-top: 50px;
          margin-bottom: 30px;
        }
        .body .container p {
          font-size: 15px;
        }
        .body .container a {
          display: inline-block;
          font-size: 15px;
          margin: 20px 0;
        }
        .body .container .btn {
          display: block;
          text-align: center;
          width: 100px;
          padding: 10px 15px;
          border-radius: 5px;
          background-color: #8e47f0;
          color: #fff;
          text-decoration: none;
          margin: 0 auto;
          margin-top: 30px;
        }
        
          </style>
    </head>
    <body>
        <div class="body">
          <div class="bg">
            <!-- 背景图 -->
            <img src="https://proxyblob.blob.core.windows.net/gpt/imgs/emails/proxy302.png" alt="">
          </div>
          <div class="container">
            <div class="logo">
              <!-- logo图 -->
              <img src="https://proxyblob.blob.core.windows.net/gpt/imgs/emails/proxy302_logo.png" alt="">
            </div>
            """
    def send(self, to, title, content) -> bool:
        """
        发送邮件
        :param to:      要发送到的邮箱
        :param title:   邮箱标题
        :param content: 邮件内容（HTML）
        :return:  是否发送成功
        """
        pass

    def sendVerificationEmail(self, to: str, verification_url: str, to_user_name: str=None, lang='zh',is_gpt=False) -> bool:
        """
        发送注册验证邮件
        :param to:                  接受者的邮箱地址
        :param verification_url:    注册验证地址
        :param to_user_name:        邮箱接受者的用户名
        :return:    True: 成功  False：失败
        """
        if is_gpt:
            name = "302.AI"
        else:
            name = "Proxy302"
        if lang.startswith("zh"):
            func = self.makeVerificationRegisterHtmlGPT if is_gpt else  self.makeVerificationRegisterHtml
            content = func(to_user_name if to_user_name else to, verification_url,name)
            return self.send(to, f"{name}注册激活邮件", content)
        elif lang.startswith('jp') or lang.startswith('ja'):
            func = self.makeJaVerificationRegisterHtmlGPT if is_gpt else self.makeJaVerificationRegisterHtml
            content = func(to_user_name if to_user_name else to, verification_url, name)
            return self.send(to, f"{name}登録アクティベーションメール", content)
        else:
            func = self.makeEnVerificationRegisterHtmlGPT if is_gpt else self.makeEnglishVerificationRegisterHtml
            content = func(to_user_name if to_user_name else to, verification_url,name)
            return self.send(to, f"{name} registration activation email", content)

    def sendCode(self,to,code,zh=False,is_gpt=False):
        if is_gpt:
            name = "302.AI"
        else:
            name = "Proxy302"
        if zh:
            func = self.makeZhEmailCodeHtml
            content = func(to, code)
            return self.send(to, f"{name}验证码邮件", content)
        else:
            func = self.makeEnglishEmailCodeHtml
            content = func(to, code)
            return self.send(to, f"{name} registration code email", content)


    def sendResetPasswordEmail(self, to: str, token_url: str, to_user_name: str=None, lang='zh',is_gpt=False,**kwargs) -> bool:
        if kwargs.get("is_zh"):
            lang='zh'
        else:
            lang='en'
        name = "Proxy302"
        if is_gpt:
            name = "302.AI"
        if lang.startswith("zh"):
            fun = self.makeResetPasswordHtmlGpt if is_gpt else self.makeResetPasswordHtml
            content = fun(to_user_name if to_user_name else to, token_url)
            return self.send(to, f"{name}重置密码邮件", content)
        elif lang.startswith('jp') or lang.startswith('ja'):
            fun = self.makeJaResetPasswordHtmlGpt if is_gpt else self.makeJaResetPasswordHtml
            content = fun(to_user_name if to_user_name else to, token_url)
            return self.send(to, f"{name}パスワードリセットメール", content)
        else:
            fun = self.makeEnglishResetPasswordHtmlGpt if is_gpt else self.makeEnglishResetPasswordHtml
            content = fun(to_user_name if to_user_name else to, token_url)
            return self.send(to, f"{name} reset password email", content)


    def send_alarm_email(self,email,is_gpt=False,lang='zh',balance=0) -> bool:
        name = "Proxy302"
        if is_gpt:
            name = "302.AI"
        if lang.startswith("zh"):
            content = self.makeZhBalanceAlarmHtml(email, is_gpt,balance)
            return self.send(email, f"【系统提醒】{name}余额不足，请及时充值", content)
        elif lang.startswith('jp') or lang.startswith('ja'):
            content = self.makeJaBalanceAlarmHtml(email, is_gpt,balance)
            return self.send(email, f"【システム通知】{name}の残高不足について", content)
        else:
            content = self.makeEnBalanceAlarmHtml(email, is_gpt,balance)
            return self.send(email, f" [System Notification] {name} Account Balance Insufficient - Please Recharge In Time", content)

    def send_traffic_alarm_email(self,email,is_gpt=False,lang='zh',traffic_used=0) -> bool:
        name = "Proxy302"
        if is_gpt:
            name = "302.AI"
        if lang.startswith("zh"):
            content = self.makeZhTrafficAlarmHtml(email, is_gpt,traffic_used)
            return self.send(email, f"【系统提醒】Proxy302流量包即将用完，请尽快续购", content)
        elif lang.startswith('jp') or lang.startswith('ja'):
            content = self.makeJaTrafficAlarmHtml(email, is_gpt,traffic_used)
            return self.send(email, f"【システム通知】Proxy302の残高不足について", content)
        else:
            content = self.makeEnTrafficAlarmHtml(email, is_gpt,traffic_used)
            return self.send(email, f" [System Notification] Proxy302 Account Traffic Insufficient - Please Recharge In Time", content)

    @staticmethod
    def makeJaVerificationRegisterHtmlGPT(to_user_name: str, verification_url: str, name: str) -> str:
        verification_url = verification_url.replace("proxy302.com", '302.ai')
        html_content = Emailer.STYLE + f"""
                  <div>
                    <p>{to_user_name}様:</p>
                    <p>こんにちは！</p>
                    <p>{name}をご登録いただきありがとうございます。登録を完了するには、次のリンクをクリックしてください (クリックしてジャンプできない場合は、ブラウザにコピーして開いてください)。有効期限は 24 時間です。</p>
                    <p><a href="{verification_url}">{verification_url}</a></p>
                    <p>（システムから送信されますので返信しないでください）</p>
                </div>
                <a href="{verification_url}" class="btn">メール確認</a>
              </div>
            </div>
            </body>
            </html>
            """
        return html_content


    @staticmethod
    def makeVerificationRegisterHtmlGPT(to_user_name:str, verification_url:str,name:str) -> str:
        verification_url = verification_url.replace("proxy302.com",'302.ai')
        html_content = Emailer.STYLE+f"""
        <div>
            <p>尊敬的{to_user_name}:</p>
            <p>您好！</p>
            <p>感谢您注册{name}，请点击以下链接完成注册,(若点击跳转不成功可以复制到浏览器打开),24小时有效:</p>
            <p><a href="{verification_url}">{verification_url}</a></p>
            <p>（系统发送，请勿回复）</p>
        </div>
        <a href="{verification_url}" class="btn">验证邮箱</a>
      </div>
    </div>
</body>
</html>
        """
        return html_content

    @staticmethod
    def makeEnVerificationRegisterHtmlGPT(to_user_name: str, verification_url: str, name: str) -> str:
        verification_url = verification_url.replace("proxy302.com", '302.ai')
        html_content = Emailer.STYLE + f"""
            <div>
               <p>Dear {to_user_name}:</p>
                <p>Hello！</p>
                <p>Thank you for registering {name}, please click on the following link to complete the registration, (if you click on the link is not successful, you can copy to the browser to open), valid for 24 hours:</p>
                <p><a href="{verification_url}">{verification_url}</a></p>
                <p>(Sent by the system, please do not reply)</p>
            </div>
            <a href="{verification_url}" class="btn">Verify Email</a>
          </div>
        </div>
    </body>
    </html>
            """
        return html_content
    @staticmethod
    def makeVerificationRegisterHtml(to_user_name:str, verification_url:str,name:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE + f"""
                <div>
                    <p>尊敬的{to_user_name}:</p>
                    <p>您好！</p>
                    <p>感谢您注册{name}，请点击以下链接完成注册,(若点击跳转不成功可以复制到浏览器打开),24小时有效:</p>
                    <p><a href="{verification_url}">{verification_url}</a></p>
                    <p>（系统发送，请勿回复）</p>
                </div>
                <a href="{verification_url}" class="btn">验证邮箱</a>
              </div>
            </div>
        </body>
        </html>
                """
        return html_content

    @staticmethod
    def makeEnglishVerificationRegisterHtml(to_user_name:str, verification_url:str,name:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE + f"""
                   <div>
                      <p>Dear {to_user_name}:</p>
                       <p>Hello！</p>
                       <p>Thank you for registering {name}, please click on the following link to complete the registration, (if you click on the link is not successful, you can copy to the browser to open), valid for 24 hours:</p>
                       <p><a href="{verification_url}">{verification_url}</a></p>
                       <p>(Sent by the system, please do not reply)</p>
                   </div>
                   <a href="{verification_url}" class="btn">Verify Email</a>
                 </div>
               </div>
           </body>
           </html>
                   """
        return html_content

    @staticmethod
    def makeJaVerificationRegisterHtml(to_user_name: str, verification_url: str, name: str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE + f"""
                      <div>
                         <p>Dear {to_user_name}:</p>
                          <p>Hello！</p>
                          <p>Thank you for registering {name}, please click on the following link to complete the registration, (if you click on the link is not successful, you can copy to the browser to open), valid for 24 hours:</p>
                          <p><a href="{verification_url}">{verification_url}</a></p>
                          <p>(Sent by the system, please do not reply)</p>
                      </div>
                      <a href="{verification_url}" class="btn">Verify Email</a>
                    </div>
                  </div>
              </body>
              </html>
                      """
        return html_content

    @staticmethod
    def makeResetPasswordHtmlGpt(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.STYLE+f"""
        <div>
            <p>尊敬的{to_user_name}:</p>
            <p>您好！</p>
            <p>感谢使用302.AI, 请打开下方链接进行密码的重置, (若点击跳转不成功可以复制到浏览器打开),2小时有效:</p>
            <p><a href="{token_url}">{token_url}</a></p>
            </br>
            <p>（系统发送，请勿回复）</p>
        </div>
        """
        return html_content

    @staticmethod
    def makeZhEmailCodeHtml(to_user_name:str, code:str) -> str:
        """
        构建注册验证
        """
        html_content = Emailer.PROXY_STYLE + f"""
                <div>
                    <p>尊敬的{to_user_name}:</p>
                    <p>您好！</p>
                    <p>感谢您注册Proxy302，请使用以下验证码完成注册，2小时有效:</p>
                    <div style="background-color: #b6eff3; max-width:60px"><p >{code}</p></div>
                    </br>
                    <p>（系统发送，请勿回复）</p>
                </div>
                """
        return html_content

    @staticmethod
    def makeEnglishEmailCodeHtml(to_user_name:str, code:str) -> str:
        """
        构建注册验证
        """
        html_content = Emailer.PROXY_STYLE + f"""
                <div>
                    <p>Dear {to_user_name}:</p>
                    <p>Hello!</p>
                    <p>Thank you for registering for Proxy302, please use the following verification code to complete the registration, valid for 2 hours:</p>
                    <div style="background-color: #b6eff3; max-width:60px">{code}</p></div>
                    </br>
                    <p>（Notice: This is a system email, please do not reply）</p>
                </div>
                """
        return html_content


    @staticmethod
    def makeResetPasswordHtml(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE+f"""
        <div>
            <p>尊敬的{to_user_name}:</p>
            <p>您好！</p>
            <p>感谢使用Proxy302, 请打开下方链接进行密码的重置, (若点击跳转不成功可以复制到浏览器打开),2小时有效:</p>
            <p><a href="{token_url}">{token_url}</a></p>
            </br>
            <p>（系统发送，请勿回复）</p>
        </div>
        """
        return html_content

    @staticmethod
    def makeEnglishResetPasswordHtmlGpt(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.STYLE+f"""
        <div>
            <p>Dear {to_user_name}:</p>
            <p>Hello！</p>
            <p>Thank you for using 302.AI, please click on the following link to reset your password, (if you click on the link is not successful, you can copy to the browser to open), valid for 24 hours:</p>
            <p><a href="{token_url}">{token_url}</a></p>
            </br>
            <p>(Sent by the system, please do not reply)</p>
        </div>
        """
        return html_content
    @staticmethod
    def makeJaResetPasswordHtmlGpt(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.STYLE + f"""
          <div>
              <p>{to_user_name}様:</p>
              <p>こんにちは！</p>
              <p>302.AIをご利用いただき、ありがとうございます。パスワードをリセットするには、以下のリンクを開いてください。（クリックして移動できない場合は、ブラウザにコピーして開いてください）。リンクの有効期限は2時間です：</p>
              <p><a href="{token_url}">{token_url}</a></p>
              </br>
              <p>(システムから送信されますので返信しないでください)</p>
          </div>
          """
        return html_content

    @staticmethod
    def makeJaResetPasswordHtml(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE + f"""
        <div>
            <p>{to_user_name}様:</p>
            <p>こんにちは！</p>
            <p>Proxy302をご利用いただき、ありがとうございます。パスワードをリセットするには、以下のリンクを開いてください。（クリックして移動できない場合は、ブラウザにコピーして開いてください）。リンクの有効期限は2時間です：</p>
            <p><a href="{token_url}">{token_url}</a></p>
            </br>
            <p>（システムから送信されますので返信しないでください）</p>
        </div>
        """
        return html_content
    @staticmethod
    def makeEnglishResetPasswordHtml(to_user_name:str, token_url:str) -> str:
        """
        构建注册验证的html
        """
        html_content = Emailer.PROXY_STYLE+f"""
        <div>
            <p>Dear {to_user_name}:</p>
            <p>Hello！</p>
            <p>Thank you for using Proxy302, please click on the following link to reset your password, (if you click on the link is not successful, you can copy to the browser to open), valid for 24 hours:</p>
            <p><a href="{token_url}">{token_url}</a></p>
            </br>
            <p>(Sent by the system, please do not reply)</p>
        </div>
        """
        return html_content


    @staticmethod
    def makeZhBalanceAlarmHtml(to_user_name:str,is_gpt:bool,balance:int) -> str:
        """
        构建注册验证的html
        """
        prefix =Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix+f"""
        <div>
            <p>尊敬的{to_user_name}用户:</p>
            <p>您好！</p>
            <p>系统检测到您的{product_name}账户余额不足，为避免影响您的正常使用，请及时充值。</p>
            <p>如有任何疑问，请联系{product_name}客服团队获取帮助。</p>
            <p>{product_name}官网：{home} </p>
            <p>当前余额：{balance/1000} PTC</p>
            <p>此致</p>
            <P>{product_name}团队</p>
        </div>
        """
        return html_content
    @staticmethod
    def makeEnBalanceAlarmHtml(to_user_name:str,is_gpt:bool,balance:int) -> str:
        """
        构建注册验证的html
        """
        prefix =Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix+f"""
        <div>
            <p>Dear {to_user_name}:</p>
            <p>{product_name} has detected that your {product_name} account balance is running low. To ensure uninterrupted access to our services, we kindly request that you recharge your account at your earliest convenience.</p>
            <p>For assistance or any questions you may have, please don't hesitate to reach out to our {product_name} customer support team.</p>
            <p>{product_name} Official Website: {home} </p>
            <p>Current balance: {balance/1000} PTC</p>
            <p>Best regards,</p>
            <p>The {product_name} Team </p>
        </div>
        """
        return html_content

    @staticmethod
    def makeJaBalanceAlarmHtml(to_user_name:str,is_gpt:bool,balance:int) -> str:
        """
        构建注册验证的html
        """
        prefix = Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix + f"""
        <div>
            <p>{to_user_name} 様:</p>
            <p>いつもお世話になっております。</p>
            <p>{product_name}システムにより、お客様のアカウント残高が不足していることが確認されました。サービスのご利用に支障が出ないよう、速やかにアカウントのチャージをお願い申し上げます。</p>
            <p>ご不明点やサポートが必要な場合は、いつでも{product_name}カスタマーサポートチームまでお問い合わせください。</p>
            <p>{product_name}公式サイト：{home} </p>
            <p>現在の残高：{balance/1000} PTC</p>
            <p>何卒よろしくお願い申し上げます。</p>
            <p>{product_name}チーム </p>
        </div>
        """
        return html_content

    @staticmethod
    def makeZhTrafficAlarmHtml(to_user_name:str, is_gpt:bool, traffic_used:float) -> str:
        """
        构建流量包报警的html
        """
        prefix = Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix + f"""
        <div>
            <p>尊敬的{to_user_name}用户:</p>
            <p>您好！</p>
            <p>系统检测到您的{product_name}流量包剩余流量不足{traffic_used:.2f}GB，为避免影响您的正常使用，请及时购买新的流量包。</p>
            <p>如有任何疑问，请联系{product_name}客服团队获取帮助。</p>
            <p>{product_name}官网：{home} </p>
            <p>此致</p>
            <p>{product_name}团队</p>
        </div>
        """
        return html_content

    @staticmethod
    def makeEnTrafficAlarmHtml(to_user_name:str, is_gpt:bool, traffic_used:float) -> str:
        """
        构建流量包报警的html
        """
        prefix = Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix + f"""
        <div>
            <p>Dear {to_user_name}:</p>
            <p>{product_name} has detected that your remaining traffic is less than {traffic_used:.2f}GB. To ensure uninterrupted access to our services, we kindly request that you purchase a new traffic package at your earliest convenience.</p>
            <p>For assistance or any questions you may have, please don't hesitate to reach out to our {product_name} customer support team.</p>
            <p>{product_name} Official Website: {home} </p>
            <p>Best regards,</p>
            <p>The {product_name} Team </p>
        </div>
        """
        return html_content

    @staticmethod
    def makeJaTrafficAlarmHtml(to_user_name:str, is_gpt:bool, traffic_used:float) -> str:
        """
        构建流量包报警的html
        """
        prefix = Emailer.STYLE if is_gpt else Emailer.PROXY_STYLE
        product_name = "302.AI" if is_gpt else "Proxy302"
        home = "https://302.ai/" if is_gpt else "https://www.proxy302.com/"
        html_content = prefix + f"""
        <div>
            <p>{to_user_name} 様:</p>
            <p>いつもお世話になっております。</p>
            <p>{product_name}システムにより、お客様の残りトラフィックが{traffic_used:.2f}GB未満であることが確認されました。サービスのご利用に支障が出ないよう、速やかに新しいトラフィックパッケージのご購入をお願い申し上げます。</p>
            <p>ご不明点やサポートが必要な場合は、いつでも{product_name}カスタマーサポートチームまでお問い合わせください。</p>
            <p>{product_name}公式サイト：{home} </p>
            <p>何卒よろしくお願い申し上げます。</p>
            <p>{product_name}チーム </p>
        </div>
        """
        return html_content


class STMEmailer(Emailer):
    def __init__(self, server: str, user: str, password: str, _from: str):
        self._from, self.server, self.user, self.password = _from, server, user, password

    def send(self, to, title, content) -> bool:
        self.client = smtplib.SMTP_SSL(self.server, smtplib.SMTP_SSL_PORT)
        print("邮件服务器连接成功")
        self.client.login(self.user, self.password)
        print("邮箱登录成功")
        msg = MIMEText(content, 'html', 'utf-8')
        msg['Subject'] = title
        msg['From'] = self._from
        msg['To'] = to
        try:
            self.client.sendmail(self._from, to, msg.as_string())
            return True
        except smtplib.SMTPException as e:
            traceback.print_exc()
            print("邮件发送失败")
            return False


class SendGridREmailer(Emailer):
    def __init__(self, api_kay, _from):
        self.sg = sendgrid.SendGridAPIClient(api_key=api_kay)
        self.fromEmail =_from

    def send(self, to, title, content) -> bool:
        mail = Mail(from_email=self.fromEmail, to_emails=to, subject=title, html_content=content)
        print(mail)
        response = self.sg.send(mail)
        print("sendgrid:", response.status_code)
        # print(response.body)
        # print(response.headers)
        return True