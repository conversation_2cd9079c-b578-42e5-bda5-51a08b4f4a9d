# -*- coding: utf-8 -*-
# @Time    : 2024/1/26 14:09
# <AUTHOR> hzx1994
# @File    : gpt.py
# @Software: PyCharm
# @description:
import asyncio
import hashlib
import html
import json
import math
import mimetypes
import os
from concurrent.futures import Thread<PERSON>oolExecutor
from pathlib import Path
from urllib.parse import urlparse

import aiofiles
import oss2
import pandas as pd
import random
from datetime import datetime, timedelta

from openai import api_key
from pandas.core.common import flatten
import aiohttp
import pytz
import tortoise.transactions
from azure.storage.blob import ContentSettings
from azure.storage.blob.aio import BlobServiceClient
from starlette.responses import JSONResponse
from striprtf.striprtf import rtf_to_text
from tortoise.expressions import Q, RawSQL, F, Subquery, Case, When, Function
from pypika import CustomFunction
from tortoise import transactions
from tortoise.functions import Sum, Count, Max, Min

import utils
from controllers.knowledge import StreamProcessor
from controllers.pubilc import add_ready_ip_order
from exections import ShareCodeExistsErr, AuthException
from libs.IP import IP
from libs.Weichat import get_signature
from models.api import CategoryRequest, CategoryResponse, BrandRequest, BrandResponse, ModelParamBase, CustomModelBase, CustomModelCreate, CustomModelUpdate
from models.conf import CacheSaveWhere
from models.db.gpt import TToken, TTokenMapping, TLog, TModelPrice, TModel, TTokenInfo, TOpenaiGpts, TTokenSupplier, \
    TGptTool, TSnycLog, TRobotMapping, TAiAppBox, TKnowledgeBase, TDataStatistics, TApiLimit, TIpList, TService, TApisCategory, TApisBrand, TModelParam, TService, TCustomModels
from models.db.proxy import UserInfo, TUser, TIpOrders
from models.gpt import GptToken, GPTStatus, ToolsId, APPName, ApiKeyLimit, ListType, IpType
from models.proxy import Region
from models.gpt import GptToken, GPTStatus, ToolsId, APPName, tool_config
from models.response import StateCode, suc_data, fail_data
from utils import random_string, current_timestamp, Tools, to_ptc, cache, get_diff_time_from_zone, timestamp_to_date

ai_names = {'deepseek-ai/DeepSeek-R1': {'index': 1, 'name': 'deepseek-ai/DeepSeek-R1', 'id': 'deepseek-ai/DeepSeek-R1', 'des': '深度求索R1', 'des_en': 'DeepSeek R1', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-V3': {'index': 1, 'name': 'deepseek-ai/DeepSeek-V3', 'id': 'deepseek-ai/DeepSeek-V3', 'des': '深度求索V3', 'des_en': 'DeepSeek V3', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B': {'index': 1, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B', 'des': '深度求索R1-70B', 'des_en': 'DeepSeek R1 - 70B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B': {'index': 2, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-32B', 'des': '深度求索R1-32B', 'des_en': 'DeepSeek R1 - 32B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Qwen-14B': {'index': 3, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-14B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-14B', 'des': '深度求索R1-14B', 'des_en': 'DeepSeek R1 - 14B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B': {'index': 4, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Llama-8B', 'des': '深度求索R1-8B', 'des_en': 'DeepSeek R1 - 8B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B': {'index': 5, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', 'des': '深度求索R1-7B', 'des_en': 'DeepSeek R1 - 7B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B': {'index': 6, 'name': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', 'id': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', 'des': '深度求索R1-1.5B', 'des_en': 'DeepSeek R1 - 1.5B', 'img_url': 'https://file.302.ai/gpt/imgs/20250116-165942.jpg'}, 'sonar-reasoning': {'index': 13, 'name': 'sonar-reasoning', 'id': 'sonar-reasoning', 'des': 'Perplexity R1模型', 'des_en': 'Perplexity R1 Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'}, 'o3-mini': {'index': 1, 'name': 'o3-mini', 'id': 'o3-mini', 'des': '最新o3-mini', 'des_en': 'Latest o3-mini', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'o3-mini-2025-01-31': {'index': 1, 'name': 'o3-mini-2025-01-31', 'id': 'o3-mini-2025-01-31', 'des': '最新o3-mini', 'des_en': 'Latest o3-mini', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'deepseek-r1-baidu': {'index': 8, 'name': 'Deepseek-R1-Baidu', 'id': 'deepseek-r1-baidu', 'des': '百度部署的深度求索R1', 'des_en': 'DeepSeek R1', 'img_url': 'https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02an_d3cd2d4d-5726-4e0a-95cb-27b788e98a6g.png'}, 'deepseek-v3-baidu': {'index': 8, 'name': 'Deepseek-V3-Baidu', 'id': 'deepseek-v3-baidu', 'des': '百度部署的深度求索V3', 'des_en': 'DeepSeek V3', 'img_url': 'https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02an_d3cd2d4d-5726-4e0a-95cb-27b788e98a6g.png'}, 'deepseek-r1-huoshan': {'index': 8, 'name': 'Deepseek-R1-Huoshan', 'id': 'deepseek-r1-huoshan', 'des': '火山引擎部署的深度求索R1', 'des_en': 'DeepSeek R1', 'img_url': 'https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02an_d3cd2d4d-5726-4e0a-95cb-27b788e98a6g.png'}, 'deepseek-v3-huoshan': {'index': 8, 'name': 'Deepseek-V3-Huoshan', 'id': 'deepseek-v3-huoshan', 'des': '火山引擎部署的深度求索V3', 'des_en': 'DeepSeek V3', 'img_url': 'https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02an_d3cd2d4d-5726-4e0a-95cb-27b788e98a6g.png'}, 'gemini-2.0-flash': {'index': 1, 'name': 'gemini-2.0-flash', 'id': 'gemini-2.0-flash', 'des': 'Gemini 2.0 Flash', 'des_en': 'Gemini 2.0 Flash', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'}, 'gemini-2.0-flash-lite': {'index': 2, 'name': 'gemini-2.0-flash-lite', 'id': 'gemini-2.0-flash-lite', 'des': 'Gemini 2.0 Flash Lite', 'des_en': 'Gemini 2.0 Flash Lite', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'}, 'gemini-2.0-pro': {'index': 3, 'name': 'gemini-2.0-pro', 'id': 'gemini-2.0-pro', 'des': 'Gemini 2.0 Pro', 'des_en': 'Gemini 2.0 Pro', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'},'Doubao-1.5-pro-32k': {'index': 10, 'name': 'Doubao-1.5-pro-32k', 'id': 'Doubao-1.5-pro-32k', 'des': '字节豆包', 'des_en': 'Byte Dance Model', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg'}, 'Doubao-1.5-pro-256k': {'index': 10, 'name': 'Doubao-1.5-pro-256k', 'id': 'Doubao-1.5-pro-256k', 'des': '字节豆包', 'des_en': 'Byte Dance Model', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg'}, 'Doubao-1.5-lite-32k': {'index': 10, 'name': 'Doubao-1.5-lite-32k', 'id': 'Doubao-1.5-lite-32k', 'des': '字节豆包', 'des_en': 'Byte Dance Model', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg'}, 'Doubao-1.5-vision-pro-32k': {'index': 10, 'name': 'Doubao-1.5-vision-pro-32k', 'id': 'Doubao-1.5-vision-pro-32k', 'des': '字节豆包', 'des_en': 'Byte Dance Model', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg'}, 'step-1o-vision-32k': {'index': 10, 'name': 'step-1o-vision-32k', 'id': 'step-1o-vision-32k', 'des': '阶跃星辰', 'des_en': 'Stepfun', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02cc_a9625462-f3f4-4552-8cfd-6451617d4d2g.jpg'}, 'gemini-2.0-flash-thinking-exp-01-21': {'index': 7, 'name': 'gemini-2.0-flash-thinking-exp-01-21', 'id': 'gemini-2.0-flash-thinking-exp-01-21', 'des': 'Gemini 2.0 Flash Thinking', 'des_en': 'Gemini 2.0 Flash Thinking', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'}, 'sonar': {'index': 13, 'name': 'sonar', 'id': 'sonar', 'des': 'Perplexity模型', 'des_en': 'Perplexity Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'}, 'sonar-pro': {'index': 13, 'name': 'sonar-pro', 'id': 'sonar-pro', 'des': 'Perplexity模型', 'des_en': 'Perplexity Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'},'deepseek-ai/deepseek-vl2': {'index': 1, 'name': 'deepseek-ai/deepseek-vl2', 'id': 'deepseek-ai/deepseek-vl2', 'des': '深度求索', 'des_en': 'DeepSeek Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'deepseek-ai/DeeSseek-V2.5': {'index': 1, 'name': 'deepseek-ai/DeeSseek-V2.5', 'id': 'deepseek-ai/DeeSseek-V2.5', 'des': '深度求索', 'des_en': 'DeepSeek Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'},'deepseek-ai/DeepSeek-V2.5': {'index': 1, 'name': 'deepseek-ai/DeeSseek-V2.5', 'id': 'deepseek-ai/DeeSseek-V2.5', 'des': '深度求索', 'des_en': 'DeepSeek Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/QwQ-32B-Preview': {'index': 1, 'name': 'Qwen/QwQ-32B-Preview', 'id': 'Qwen/QwQ-32B-Preview', 'des': '开源版o1模型', 'des_en': 'Open Source Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/QVQ-72B-Preview': {'index': 1, 'name': 'Qwen/QVQ-72B-Preview', 'id': 'Qwen/QVQ-72B-Preview', 'des': '开源版o1多模态模型', 'des_en': 'Open Source Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2-1.5B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2-1.5B-Instruct', 'id': 'Qwen/Qwen2-1.5B-Instruct', 'des': '开源千问1.5B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2-7B-Instruct': {'index': 1, 'name': 'Qwen2-7B', 'id': 'Qwen/Qwen2-7B-Instruct', 'des': '阿里开源模型', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-Coder-32B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2.5-Coder-32B-Instruct', 'id': 'Qwen/Qwen2.5-Coder-32B-Instruct', 'des': '开源千问32B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2-VL-72B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2-VL-72B-Instruct', 'id': 'Qwen/Qwen2-VL-72B-Instruct', 'des': '开源多模态千问', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-72B-Instruct-128K': {'index': 1, 'name': 'Qwen/Qwen2.5-72B-Instruct-128K', 'id': 'Qwen/Qwen2.5-72B-Instruct-128K', 'des': '开源千问72B-128k上下文', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-32B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2.5-32B-Instruct', 'id': 'Qwen/Qwen2.5-32B-Instruct', 'des': '开源千问32B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-14B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2.5-14B-Instruct', 'id': 'Qwen/Qwen2.5-14B-Instruct', 'des': '开源千问14B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-7B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2.5-7B-Instruct', 'id': 'Qwen/Qwen2.5-7B-Instruct', 'des': '开源千问7B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Qwen/Qwen2.5-Coder-7B-Instruct': {'index': 1, 'name': 'Qwen/Qwen2.5-Coder-7B-Instruct', 'id': 'Qwen/Qwen2.5-Coder-7B-Instruct', 'des': '开源千 问编程模型', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Vendor-A/Qwen/Qwen2.5-72B-Instruct': {'index': 1, 'name': 'Vendor-A/Qwen/Qwen2.5-72B-Instruct', 'id': 'Vendor-A/Qwen/Qwen2.5-72B-Instruct', 'des': '开源千问72B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/Qwen/Qwen2-7B-Instruct': {'index': 1, 'name': 'Pro/Qwen/Qwen2-7B-Instruct', 'id': 'Pro/Qwen/Qwen2-7B-Instruct', 'des': '开源千问7B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/Qwen/Qwen2-VL-7B-Instrutct': {'index': 1, 'name': 'Pro/Qwen/Qwen2-VL-7B-Instrutct', 'id': 'Pro/Qwen/Qwen2-VL-7B-Instrutct', 'des': '开源多模态千问', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/Qwen/Qwen2.5-7B-Instruct': {'index': 1, 'name': 'Pro/Qwen/Qwen2.5-7B-Instruct', 'id': 'Pro/Qwen/Qwen2.5-7B-Instruct', 'des': '开源千问7B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/Qwen/Qwen2-1.5B-Instruct': {'index': 1, 'name': 'Pro/Qwen/Qwen2-1.5B-Instruct', 'id': 'Pro/Qwen/Qwen2-1.5B-Instruct', 'des': '开源千问1.5B', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'google/gemma-2-9b-it': {'index': 1, 'name': 'google/gemma-2-9b-it', 'id': 'google/gemma-2-9b-it', 'des': 'Google开源模型9B', 'des_en': 'Google Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/google/gemma-2-9b-it': {'index': 1, 'name': 'Gemma2-9B', 'id': 'Pro/google/gemma-2-9b-it', 'des': 'Google开源模型', 'des_en': 'Google Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'google/gemma-2-27b-it': {'index': 1, 'name': 'Gemma2-27B', 'id': 'google/gemma-2-27b-it', 'des': 'Google开源模型', 'des_en': 'Google Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/meta-llama/Meta-Llama-3.1-8B-Instruct': {'index': 1, 'name': 'Pro/meta-llama/Meta-Llama-3.1-8B-Instruct', 'id': 'Pro/meta-llama/Meta-Llama-3.1-8B-Instruct', 'des': 'Llama3.1小杯', 'des_en': 'Meta Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'meta-llama/Llama-3.3-70B-Instruct': {'index': 1, 'name': 'meta-llama/Llama-3.3-70B-Instruct', 'id': 'meta-llama/Llama-3.3-70B-Instruct', 'des': 'Meta最新开源模型', 'des_en': 'Meta Latest Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'meta-llama/Meta-Llama-3.1-405B-Instruct': {'index': 1, 'name': 'meta-llama/Meta-Llama-3.1-405B-Instruct', 'id': 'meta-llama/Meta-Llama-3.1-405B-Instruct', 'des': 'Llama3.1大杯', 'des_en': 'Meta Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'meta-llama/Meta-Llama-3.1-8B-Instruct': {'index': 1, 'name': 'meta-llama/Meta-Llama-3.1-8B-Instruct', 'id': 'meta-llama/Meta-Llama-3.1-8B-Instruct', 'des': 'Llama3.1小杯', 'des_en': 'Meta Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'meta-llama/Meta-Llama-3.1-70B-Instruct': {'index': 1, 'name': 'meta-llama/Meta-Llama-3.1-70B-Instruct', 'id': 'meta-llama/Meta-Llama-3.1-70B-Instruct', 'des': 'Llama3.1中杯', 'des_en': 'Meta Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/OpenGVLab/InternVL2-8B': {'index': 1, 'name': 'Pro/OpenGVLab/InternVL2-8B', 'id': 'Pro/OpenGVLab/InternVL2-8B', 'des': '电信开源多模 态模型', 'des_en': 'Open Source Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'OpenGVLab/InternVL2-26B': {'index': 1, 'name': 'OpenGVLab/InternVL2-26B', 'id': 'OpenGVLab/InternVL2-26B', 'des': '书生万象多模态模型', 'des_en': 'intern Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'internlm/internlm2_5-20b-chat': {'index': 1, 'name': 'internlm/internlm2_5-20b-chat', 'id': 'internlm/internlm2_5-20b-chat', 'des': '书生万象模型50B', 'des_en': 'intern Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'internlm/internlm2_5-7b-chat': {'index': 1, 'name': 'internlm/internlm2_5-7b-chat', 'id': 'internlm/internlm2_5-7b-chat', 'des': '书生万象模型7B', 'des_en': 'intern Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'THUDM/glm-4-9b-chat': {'index': 1, 'name': 'THUDM/glm-4-9b-chat', 'id': 'THUDM/glm-4-9b-chat', 'des': '智谱开源模型', 'des_en': 'ZHIPU Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'THUDM/chatglm3-6b': {'index': 1, 'name': 'THUDM/chatglm3-6b', 'id': 'THUDM/chatglm3-6b', 'des': '智谱开源模型', 'des_en': 'ZHIPU Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'Pro/THUDM/glm-4-9b-chat': {'index': 1, 'name': 'Pro/THUDM/glm-4-9b-chat', 'id': 'Pro/THUDM/glm-4-9b-chat', 'des': '智谱开源模型', 'des_en': 'ZHIPU Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'TeleAI/TeleMM': {'index': 1, 'name': 'TeleAI/TeleMM', 'id': 'TeleAI/TeleMM', 'des': '电源开源多模态模型', 'des_en': 'TeleAI Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'TeleAI/TeleChat2': {'index': 1, 'name': 'TeleAI/TeleChat2', 'id': 'TeleAI/TeleChat2', 'des': '电源开源模型', 'des_en': 'TeleAI Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'AIDC-AI/Marco-o1': {'index': 1, 'name': 'AIDC-AI/Marco-o1', 'id': 'AIDC-AI/Marco-o1', 'des': '开源版o1模型', 'des_en': 'Open Source Model', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, '01-ai/Yi-1.5-9B-Chat-16K': {'index': 1, 'name': '01-ai/Yi-1.5-9B-Chat-16K', 'id': '01-ai/Yi-1.5-9B-Chat-16K', 'des': '零一开源模型', 'des_en': '01.AI', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, '01-ai/Yi-1.5-6B-Chat': {'index': 1, 'name': '01-ai/Yi-1.5-6B-Chat', 'id': '01-ai/Yi-1.5-6B-Chat', 'des': '零一开源模型', 'des_en': '01.AI', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, '01-ai/Yi-1.5-34B-Chat-16K': {'index': 1, 'name': '01-ai/Yi-1.5-34B-Chat-16K', 'id': '01-ai/Yi-1.5-34B-Chat-16K', 'des': ' 零一开源模型', 'des_en': '01.AI', 'img_url': 'https://file.302.ai/gpt/imgs/20250120-160452.jpg'}, 'MiniMax-Text-01': {'index': 1, 'name': 'MiniMax-Text-01', 'id': 'MiniMax-Text-01', 'des': 'Minimax Model', 'des_en': 'Minimax模型', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_137ae84e-eff9-4538-aab2-43d7da63d09g.jpg'},
    "glm-zero-preview":   {
        "index": 11,
        "name": "GLM-Zero-Preview",
        "id": "glm-zero-preview",
        "des": "智谱推理模型",
        "des_en": "Zhipu Reasoning Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png"
      },
          "QVQ-72B-Preview":{
        "index": 2,
        "name": "QVQ-72B-Preview",
        "id": "QVQ-72B-Preview",
        "des": "开源模型",
        "des_en": "Open Source Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
    "Doubao-Vision-Lite-32k":    {
        "index": 10,
        "name": "Doubao-Vision-Lite-32k",
        "id": "Doubao-Vision-Lite-32k",
        "des": "字节豆包",
        "des_en": "Byte Dance Model",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg"
      },     "o1":{
        "index": 1,
        "name": "o1",
        "id": "o1",
        "des": "完整版O1",
        "des_en": "Full Version O1",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg"
      },
      "grok-2-1212":{
        "index": 13,
        "name": "grok-2-1212",
        "id": "grok-2-1212",
        "des": "xAI模型",
        "des_en": "xAI Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/xAI.jpg"
      },
      "grok-2-vision-1212":{
        "index": 13,
        "name": "grok-2-vision-1212",
        "id": "grok-2-vision-1212",
        "des": "xAI模型",
        "des_en": "xAI Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/xAI.jpg"
      },  "deepseek-vl2":{
        "index": 8,
        "name": "Deepseek-VL2",
        "id": "deepseek-vl2",
        "des": "深度求索",
        "des_en": "DeepSeek Model",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cc_5f798d25-defb-4971-9a47-bb30b30d29dg.png"
      },
      "qwen2.5-coder-32b-instruct":{
        "index": 2,
        "name": "Qwen2.5-Coder-32B",
        "id": "qwen2.5-coder-32b-instruct",
        "des": "阿里开源模型",
        "des_en": "Tongyi Qianwen",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
    "gemini-2.0-flash-thinking-exp-1219":{
        "index": 7,
        "name": "gemini-2.0-flash-thinking-exp-1219",
        "id": "gemini-2.0-flash-thinking-exp-1219",
        "des": "Gemini 2.0 Flash Thinking",
        "des_en": "Gemini 2.0 Flash Thinking",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },

    "o1-plus": {
        "index": 1,
        "name": "o1-plus",
        "id": "o1-plus",
        "des": "完整版O1",
        "des_en": "Full Version O1",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg"
    }, "gemini-1.5-flash-8b": {
        "index": 7,
        "name": "gemini-1.5-flash-8b",
        "id": "gemini-1.5-flash-8b",
        "des": "Gemini 1.5 Flash 8b",
        "des_en": "Gemini 1.5 Flash 8b",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },
    "gemini-2.0-flash-exp": {
        "index": 7,
        "name": "gemini-2.0-flash-exp",
        "id": "gemini-2.0-flash-exp",
        "des": "Gemini 2.0 Flash",
        "des_en": "Gemini 2.0 Flash",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },
    "qwen-vl-ocr": {
        "index": 2,
        "name": "Qwen-Vl-Ocr", 
        "id": "qwen-vl-ocr",
        "des": "阿里OCR模型",
        "des_en": "Alibaba OCR Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
    },
    "gemini-exp-1206": {
        "index": 7,
        "name": "gemini-exp-1206",
        "id": "gemini-exp-1206", 
        "des": "Google模型-20241206",
        "des_en": "Google model-20241206",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },
    "llama3.3-70b": {
        "index": 7,
        "name": "Llama-3.3-70B",
        "id": "llama3.3-70b",
        "des": "Meta最新开源模型", 
        "des_en": "Meta Latest Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg"
    },
    "nova-pro": {
        "index": 13,
        "name": "nova-pro",
        "id": "nova-pro",
        "des": "Amazon Nova Pro",
        "des_en": "Amazon Nova Pro", 
        "img_url": "https://file.302.ai/gpt/imgs/20241209-165055.jpg"
    },
    "nova-lite": {
        "index": 13,
        "name": "nova-lite",
        "id": "nova-lite",
        "des": "Amazon Nova Lite",
        "des_en": "Amazon Nova Lite",
        "img_url": "https://file.302.ai/gpt/imgs/20241209-165055.jpg"
    },
    "nova-micro": {
        "index": 13,
        "name": "nova-micro",
        "id": "nova-micro", 
        "des": "Amazon Nova Micro",
        "des_en": "Amazon Nova Micro",
        "img_url": "https://file.302.ai/gpt/imgs/20241209-165055.jpg"
    },
    "Qwen/QwQ-32B-Preview":{
        "index": 2,
        "name": "QwQ-32B-Preview",
        "id": "Qwen/QwQ-32B-Preview",
        "des": "开源模型",
        "des_en": "Tongyi Qianwen",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
    "mistral-large-2411": {
        "index": 11,
        "name": "Mistral-Large-2411",
        "id": "mistral-large-2411",
        "des": "Mistral最新模型",
        "des_en": "Mistral Latest Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_0afd75d6-70f2-4ff5-80a5-841e49d7e58g.jpg"
      },
    "pixtral-large-2411":{
        "index": 11,
        "name": "Pixtral-Large-2411",
        "id": "pixtral-large-2411",
        "des": "Mistral最新多模态模型",
        "des_en": "Mistral Latest Multimodal Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_0afd75d6-70f2-4ff5-80a5-841e49d7e58g.jpg"
      },
    "qwen-turbo-2024-11-01": {
        "index": 15,
        "name": "Qwen-turbo-2024-11-01",
        "id": "qwen-turbo-2024-11-01",
        "des": "阿里通义千问Turbo",
        "des_en": "Tongyi Qianwen Turbo",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
    "grok-beta":      {
        "index": 13,
        "name": "grok-beta",
        "id": "grok-beta",
        "des": "xAI模型",
        "des_en": "xAI Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/xAI.jpg"
      },
    "claude-3-5-haiku-20241022":      {
        "index": 4,
        "name": "claude-3-5-haiku-20241022",
        "id": "claude-3-5-haiku-20241022",
        "des": "最新Claude 3.5小杯",
        "des_en": "Latest Claude 3.5 Small",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg"
      },
        "yi-lightning":{
        "index": 5,
        "name": "Yi-Lightning",
        "id": "yi-lightning",
        "des": "零一万物",
        "des_en": "01.AI",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_95d70cea-b934-4795-a5f0-a7de66abf9fg.png"
    },
    "claude-3-5-sonnet-20241022":{
        "index": 4,
        "name": "claude-3.5-sonnet-20241022",
        "id": "claude-3-5-sonnet-20241022",
        "des": "最新Claude 3.5",
        "des_en": "Latest Claude3.5",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg"
    },
    "claude-3-5-sonnet-20240620":{
        "index": 16,
        "name": "claude-3.5-sonnet-20240620",
        "id": "claude-3-5-sonnet-20240620",
        "des": "旧版Claude 3.5",
        "des_en": "Old Claude3.5",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg"
    },
        "Llama-3.1-nemotron": {
        "index": 9,
        "name": "Llama-3.1-nemotron",
        "id": "llama-3.1-nemotron",
        "des": "Nvidia开源模型",
        "des_en": "Nvidia Open Source Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/Nvidia.jpg"
    },
    "llama-3.1-nemotron":{
        "index": 9,
        "name": "Llama-3.1-nemotron",
        "id": "llama-3.1-nemotron",
        "des": "Nvidia开源模型",
        "des_en": "Nvidia Open Source Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/Nvidia.jpg"
    },
    "grok-2": {
        "index": 13,
        "name": "grok-2",
        "id": "grok-2",
        "des": "xAI模型",
        "des_en": "xAI Model",
        "img_url": "https://file.302ai.cn/gpt/imgs/xAI.jpg"
    },
    "gemini-1.5-flash-002": {
        "index": 7,
        "name": "gemini-1.5-flash-002",
        "id": "gemini-1.5-flash-002",
        "des": "最新Google模型低价版",
        "des_en": "Google Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },
    "gemini-1.5-pro-exp-0801": {
        "index": 7,
        "name": "gemini-1.5-pro-0801",
        "id": "gemini-1.5-pro-exp-0801",
        "des": "Google模型0801",
        "des_en": "Google Model 0801",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },
    "llama3.2-90b": {
        "index": 7,
        "name": "Llama3.2-90B",
        "id": "llama3.2-90b",
        "des": "Meta最新多模态模型",
        "des_en": "Meta Latest Multi-Modal Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg"
    },
    "llama3.2-11b": {
        "index": 7,
        "name": "Llama3.2-11B",
        "id": "llama3.2-11b",
        "des": "Meta最新多模态模型",
        "des_en": "Meta Latest Multi-Modal Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg"
    },
'pplx-8b-online': {'index': 13, 'name': 'pplx-8b-online', 'id': 'pplx-8b-online', 'des': 'Perplexity模型小杯', 'des_en': 'Perplexity Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'}, 'pplx-70b-online': {'index': 13, 'name': 'pplx-70b-online', 'id': 'pplx-70b-online', 'des': 'Perplexity模型小杯', 'des_en': 'Perplexity Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'}, 'pplx-405b-online': {'index': 13, 'name': 'pplx-405b-online', 'id': 'pplx-405b-online', 'des': 'Perplexity模型小杯', 'des_en': 'Perplexity Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/20240909-124038.jpg'}, 'mattshumer/Reflection-Llama-3.1-70B': {'index': 7, 'name': 'Reflection-Llama-3.1-70B', 'id': 'mattshumer/Reflection-Llama-3.1-70B', 'des': '开源模型', 'des_en': 'Open Source Model', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'},

    "gemini-1.5-flash-001":{
        "index": 7,
        "name": "gemini-1.5-flash",
        "id": "gemini-1.5-flash-001",
        "des": "最新Google模型",
        "des_en": "Latest Google Model",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg"
    },   "glm-4v-plus":{
        "index": 4,
        "name": "GLM-4V-Plus",
        "id": "glm-4v-plus",
        "des": "智谱最新图像识别",
        "des_en": "Zhipu Latest Image Recognition",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png"
      },
"glm-4-plus":{
        "index": 3,
        "name": "GLM-4-Plus",
        "id": "glm-4-plus",
        "des": "智谱旗舰版",
        "des_en": "Zhipu Flagship Edition",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png"
      },
'Qwen/Qwen2-Math-72B-Instruct': {'index': 14, 'name': 'Qwen2-Math-72B', 'id': 'Qwen/Qwen2-Math-72B-Instruct', 'des': '阿里开源数学模型', 'des_en': 'Tongyi Qianwen Math Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_f164277f-74cc-4643-b49f-a11a8ef5b71g.png'}, 'glm-4-long': {'index': 3, 'name': 'GLM-4-Long', 'id': 'glm-4-long', 'des': '智谱AI模型：GLM-4-Long，清华大学研发', 'des_en': 'ZHIPU AI Model: GLM-4-Long, developed by Tsinghua University', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png'}, 'farui-plus': {'index': 10, 'name': 'farui-plus', 'id': 'farui-plus', 'des': '法睿法律模型', 'des_en': 'Law Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_4a722742-2954-447e-abfb-cf005e9e852g.jpg'}, 'abab6.5s-chat': {'index': 10, 'name': 'abab6.5s', 'id': 'abab6.5s-chat', 'des': 'Minimax模型', 'des_en': 'Minimax Model', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_137ae84e-eff9-4538-aab2-43d7da63d09g.jpg'}, 'hunyuan-lite': {'index': 10, 'name': 'Hunyuan-Lite', 'id': 'hunyuan-lite', 'des': '腾讯混元入门版', 'des_en': 'Tencent Model Lite', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_cbb8c615-55dc-4eca-a6fe-6d2f727f82ag.png'}, 'hunyuan-standard': {'index': 10, 'name': 'Hunyuan-Standard', 'id': 'hunyuan-standard', 'des': '腾讯混元标准版', 'des_en': 'Tencent Model Standard', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_cbb8c615-55dc-4eca-a6fe-6d2f727f82ag.png'}, 'hunyuan-pro': {'index': 10, 'name': 'Hunyuan-Pro', 'id': 'hunyuan-pro', 'des': '腾讯混元专业版', 'des_en': 'Tencent Model Pro', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_cbb8c615-55dc-4eca-a6fe-6d2f727f82ag.png'}, 'hunyuan-code': {'index': 10, 'name': 'Hunyuan-Code', 'id': 'hunyuan-code', 'des': '腾讯混元编程版', 'des_en': 'Tencent Model Code', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_cbb8c615-55dc-4eca-a6fe-6d2f727f82ag.png'}, 'hunyuan-vision': {'index': 10, 'name': 'Hunyuan-Vision', 'id': 'hunyuan-vision', 'des': '腾讯混元图像识别', 'des_en': 'Tencent Model Vision', 'img_url': 'https://file.302ai.cn/gpt/imgs/img_v3_02du_cbb8c615-55dc-4eca-a6fe-6d2f727f82ag.png'}, 'gpt-4o-2024-08-06': {'index': 1, 'name': 'gpt-4o-2024-08-06', 'id': 'gpt-4o-2024-08-06', 'des': '最新版GPT4o', 'des_en': 'Latest GPT4o', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'gpt-4o-mini-2024-07-18': {'index': 1, 'name': 'gpt-4o-mini-2024-07-18', 'id': 'gpt-4o-mini-2024-07-18', 'des': 'GPT4o-低价版', 'des_en': 'Cheap GPT-4o', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'chatgpt-4o-latest': {'index': 1, 'name': 'chatgpt-4o-latest', 'id': 'chatgpt-4o-latest', 'des': '最新OpenAI模型', 'des_en': 'Latest OpenAI Model', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'gpt-4': {'index': 1, 'name': 'gpt-4', 'id': 'gpt-4', 'des': '经典GPT4.0', 'des_en': 'Original GPT4.0', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'gemini-1.5-pro-exp-0801': {'index': 7, 'name': 'gemini-1.5-pro-0801', 'id': 'gemini-1.5-pro-exp-0801', 'des': '最新Google模型', 'des_en': 'Latest Google Model', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'},
    'ERNIE-4.0-8K': {'index': 1, 'name': '文心一言4.0', 'id': 'ERNIE-4.0-8K', 'des': '百度最新的AI模型',
                     'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_21596413-461f-4463-8a7b-54330bc218cg.png'},
    'qwen-max': {'index': 2, 'name': '通义千问', 'id': 'qwen-max', 'des': '阿里最新的AI模型',
                 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png'},
    'glm-4-0520': {'index': 3, 'name': '智谱GLM-4', 'id': 'glm-4', 'des': '智谱AI最新AI模型，来自清华大学',
              'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png'},
    'glm-4v': {'index': 4, 'name': '智谱GLM-4V', 'id': 'glm-4v',
               'des': '智谱AI最新图像识别AI模型，来自清华大学',
               'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png'},
    'Baichuan2-53B': {'index': 5, 'name': '百川AI', 'id': 'Baichuan2-53B',
                      'des': '百川智能的AI模型，来自搜狗创始人王小川',
                      'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_27d69105-5ef6-4411-bdc6-606d5b7c6ddg.png'},
    'moonshot-v1-8k': {'index': 6, 'name': '月之暗面AI', 'id': 'moonshot-v1-8k',
                       'des': '月之暗面最新的AI模型，也是应用kimi所使用的AI模型',
                       'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_9297c194-e63f-4a1b-a4a2-3d6f1181751g.jpg'},
    'yi-34b-chat-0205': {'index': 7, 'name': '零一万物AI', 'id': 'yi-34b-chat-0205',
                         'des': '零一万物最新的AI模型，来自于前google副总裁李开复',
                         'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_95d70cea-b934-4795-a5f0-a7de66abf9fg.png'},
    'yi-vl-plus': {'index': 8, 'name': '零一万物AI-VL', 'id': 'yi-vl-plus',
                   'des': '零一万物最新的图像识别AI模型，来自于前google副总裁李开复',
                   'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_95d70cea-b934-4795-a5f0-a7de66abf9fg.png'},
    'suno-v3': {
        "index": 1, "name": "Suno", "id": "suno-v3",
        "des": "Suno是一个专业高质量的AI歌曲和音乐创作平台，可以用很简单的指示创作一首歌曲",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029e_e184f90c-7d31-405a-81a1-ae473ce44d3g.png",
        "detail": ["Make a song about moon", "风格：Jazz-hiphop , Blues 中文歌词：床前明月光，疑是地上霜。"],

    },'suno-v3.5': {
        "index": 1, "name": "Suno-V3.5", "id": "suno-v3.5",
        "des": "Suno是一个专业高质量的AI歌曲和音乐创作平台，可以用很简单的指示创作一首歌曲",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029e_e184f90c-7d31-405a-81a1-ae473ce44d3g.png",
        "detail": ["Make a song about moon", "风格：Jazz-hiphop , Blues 中文歌词：床前明月光，疑是地上霜。"],

    }

    , "llama3-70b-8192": {
        "index": 9,
        "name": "LLaMA3-70B",
        "id": "llama3-70b-8192",
        "des": "Meta最新开源模型",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg"
    },
    "gpt-4o":
        {
            "index": 1,
            "name": "gpt-4o",
            "id": "gpt-4o",
            "des": "最新版GPT4.0",
            "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg"
        },
    "llama3-8b-8192": {
        "index": 10,
        "name": "LLaMA3-8B",
        "id": "llama3-8b-8192",
        "des": "Meta最新开源模型",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg"
    }
    , "deepseek-chat": {
        "index": 14,
        "name": "DeepSeek-Chat",
        "id": "deepseek-chat",
        "des": "最便宜国产模型",
        "img_url": "https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02an_d3cd2d4d-5726-4e0a-95cb-27b788e98a6g.png"}
    , 'command-r-plus': {
        "index": 11,
        "name": "Command R+",
        "id": "command-r-plus",
        "des": "Cohere开源模型",
        "img_url": "https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02a2_198e79bd-6697-4963-9762-3399d4a2659g.jpg"
    }, 'gpt-4-all': {'index': 1, 'name': 'gpt-4-plus', 'id': 'gpt-4-all', 'des': '官方ChatGPT Plus',
                     'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'},
    'gpt-4-turbo': {'index': 2, 'name': 'gpt-4-turbo', 'id': 'gpt-4-turbo', 'des': '最新版GPT4.0',
                    'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'},
    'gpt-4-turbo-preview': {'index': 3, 'name': 'gpt-4-turbo-preview', 'id': 'gpt-4-turbo-preview',
                            'des': '最便宜GPT4.0',
                            'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'},
    'gpt-3.5-turbo-0125': {'index': 4, 'name': 'gpt-3.5-turbo-0125', 'id': 'gpt-3.5-turbo-0125', 'des': '最便宜GPT3.5',
                           'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'},
    'claude-3-opus-20240229': {'index': 5, 'name': 'claude-3-opus', 'id': 'claude-3-opus-20240229',
                               'des': '最强Claude 3',
                               'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg'},
    'claude-3-haiku-20240307': {'index': 6, 'name': 'claude-3-haiku', 'id': 'claude-3-haiku-20240307',
                                'des': '最便宜Claude 3',
                                'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg'},
    'gemini-1.5-pro': {'index': 7, 'name': 'gemini-1.5-pro', 'id': 'gemini-1.5-pro', 'des': '最新Google模型',
                       'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_5ca8559b-95b3-4914-8f05-45053f3610fg.jpg'},
    'llama2-70b-4096': {'index': 8, 'name': 'LLaMA2-70B', 'id': 'llama2-70b-4096', 'des': 'Meta开源模型',
                        'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'},
    'mixtral-8x7b-32768': {'index': 9, 'name': 'Mixtral-8x7B', 'id': 'mixtral-8x7b-32768', 'des': 'Mistral开源模型',
                           'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_0afd75d6-70f2-4ff5-80a5-841e49d7e58g.jpg'},
    'Gemma-7b-it': {'index': 10, 'name': 'Gemma-7B', 'id': 'Gemma-7b-it', 'des': 'Google开源模型',
                    'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_02c1a455-6ca9-4eab-8eb5-dcf86a592bbg.jpg'},
      "qwen-vl-max": {
        "index": 9,
        "name": "通义千问vl-max",
        "id": "qwen-vl-max",
        "des": "阿里千问图像识别",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },"Doubao-pro-32k":
      {
        "index": 10,
        "name": "豆包大模型",
        "id": "Doubao-pro-32k",
        "des": "字节跳动-豆包",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02c2_a0911277-6271-47e0-9231-e84b03d6d03g.jpg"
      },
      "glm-4-air":{
        "index": 11,
        "name": "智谱GLM-4Air",
        "id": "glm-4-air",
        "des": "智谱最新AI模型，来自清华大学",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png"
      },
"qwen2-72b-instruct":{
        "index": 14,
        "name": "通义千问2-72b",
        "id": "qwen2-72b-instruct",
        "des": "阿里开源模型",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
      "qwen2-7b-instruct":{
        "index": 15,
        "name": "通义千问2-7b",
        "id": "qwen2-7b-instruct",
        "des": "阿里开源模型",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
      },
     "claude-3-5-sonnet-20240620": {
        "index": 16,
        "name": "claude-3.5-sonnet",
        "id": "claude-3-5-sonnet-20240620",
        "des": "最新Claude 3",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_b22cfc32-1ac7-4cb8-9b0d-8e5549a83d6g.jpg"
      },
    "ernie-4.0-turbo-8k": {
        "index": 1,
        "name": "ERNIE-4.0-Turbo",
        "id": "ernie-4.0-turbo-8k",
        "des": "百度文心一言Turbo",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_21596413-461f-4463-8a7b-54330bc218cg.png"
    },
    "qwen-vl-max": {
        "index": 2,
        "name": "qwen-vl-max",
        "id": "qwen-vl-max",
        "des": "阿里通义千问VL",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png"
    },
    "yi-large": {
        "index": 5,
        "name": "yi-large",
        "id": "yi-large",
        "des": "零一万物",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_95d70cea-b934-4795-a5f0-a7de66abf9fg.png"
    },
    "yi-vision": {
        "index": 5,
        "name": "yi-vision",
        "id": "yi-vision",
        "des": "零一万物图像识别",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_95d70cea-b934-4795-a5f0-a7de66abf9fg.png"
    },
    "deepseek-coder": {
        "index": 8,
        "name": "DeepSeek-Coder",
        "id": "deepseek-coder",
        "des": "深度求索编程模型",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cc_5f798d25-defb-4971-9a47-bb30b30d29dg.png"
    },
    "step-1v-8k": {
        "index": 10,
        "name": "step-1v-8k",
        "id": "step-1v-8k",
        "des": "阶跃星辰图像识别",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cc_a9625462-f3f4-4552-8cfd-6451617d4d2g.jpg"
    },
    "generalv3.5": {
        "index": 10,
        "name": "Spark Max",
        "id": "generalv3.5",
        "des": "讯飞星火",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cc_229b3a19-cf64-4cd7-ad41-cdf69a1f5eag.jpg"
    },

    "4.0Ultra":{
        "index": 10,
        "name": "Spark Ultra",
        "id": "4.0Ultra",
        "des": "讯飞星火",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cc_229b3a19-cf64-4cd7-ad41-cdf69a1f5eag.jpg"
    },
"Baichuan3-Turbo":{
    "index": 10,
    "name": "Baichuan3-Turbo",
    "id": "Baichuan3-Turbo",
    "des": "百川大模型",
    "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_27d69105-5ef6-4411-bdc6-606d5b7c6ddg.png"
},
"Baichuan4":{
    "index": 10,
    "name": "Baichuan4",
    "id": "Baichuan4",
    "des": "百川大模型",
    "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_029b_27d69105-5ef6-4411-bdc6-606d5b7c6ddg.png"
},
"XuanYuan-70B-Chat-4bit":{
        "index": 10,
        "name": "XuanYuan-70B",
        "id": "XuanYuan-70B-Chat-4bit",
        "des": "轩辕金融大模型",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cf_dc19169c-5d26-47fa-868f-c81601dbe07g.jpg"
      },
     "ChatLaw": {
        "index": 10,
        "name": "ChatLaw",
        "id": "ChatLaw",
        "des": "北大法律大模型",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cf_74ac9e03-32d9-41cb-a275-29aad3314cfg.jpg"
      },
"gemma2-9b-it":{
        "index": 12,
        "name": "Gemma2-9B",
        "id": "gemma2-9b-it",
        "des": "Google开源模型",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_02c1a455-6ca9-4eab-8eb5-dcf86a592bbg.jpg"
      },
    "SenseChat-5":{
        "index": 10,
        "name": "SenseChat-5",
        "id": "SenseChat-5",
        "des": "商汤商量AI模型",
        "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cm_ed35a1a7-22fb-4468-be81-70e842d10e7g.png"
    },
"SenseChat-Turbo":{
    "index": 10,
    "name": "SenseChat-Turbo",
    "id": "SenseChat-Turbo",
    "des": "商汤商量AI模型",
    "img_url": "https://file.302.ai/gpt/imgs/img_v3_02cm_ed35a1a7-22fb-4468-be81-70e842d10e7g.png"
},
    "gpt-4o-mini":{
        "index": 1,
        "name": "gpt-4o-mini",
        "id": "gpt-4o-mini",
        "des": "最新版GPT4.0-Mini",
        "img_url": "https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg"
      },
'glm-4-airx': {'index': 11, 'name': 'GLM-4-AirX', 'id': 'glm-4-airx', 'des': '智谱最快的AI模型：GLM-4-AirX，清华大学研发', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_e1068da9-0076-4997-8ee2-6e9a9361a9ag.png'}, 'codegeex-4': {'index': 11, 'name': 'CodeGeeX-4', 'id': 'codegeex-4', 'des': '智谱编程模型', 'img_url': 'https://file.302.ai/gpt/imgs/20240801/968cd06c14a4426b8103efe611fd3cd3.jpeg'}, 'step-2-16k-nightly': {'index': 10, 'name': 'Step-2-16k', 'id': 'step-2-16k-nightly', 'des': '阶跃星辰2', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02cc_a9625462-f3f4-4552-8cfd-6451617d4d2g.jpg'}, 'llama3.1-405b': {'index': 7, 'name': 'Llama3.1-405B', 'id': 'llama3.1-405b', 'des': 'Meta最新开源模型', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'}, 'llama3.1-70b': {'index': 7, 'name': 'Llama3.1-70B', 'id': 'llama3.1-70b', 'des': 'Meta最新开源模型', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'}, 'llama3.1-8b': {'index': 7, 'name': 'Llama3.1-8B', 'id': 'llama3.1-8b', 'des': 'Meta最新开源模型', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'}, 'mistral-large-2': {'index': 9, 'name': 'Mistral-Large-2', 'id': 'mistral-large-2', 'des': 'Meta最新开源模型', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_e6132559-de8a-476c-b40c-73df82c4281g.jpg'}, 'google/gemma-2-27b-it': {'index': 13, 'name': 'Gemma2-27B', 'id': 'google/gemma-2-27b-it', 'des': 'Google开源模型', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_02c1a455-6ca9-4eab-8eb5-dcf86a592bbg.jpg'}, 'command-r': {'index': 13, 'name': 'Command R', 'id': 'command-r', 'des': 'Cohere开源模型', 'img_url': 'https://proxyblob.blob.core.windows.net/gpt/cn_gpts/img_v3_02a2_198e79bd-6697-4963-9762-3399d4a2659g.jpg'}
,'qwen2.5-72b-instruct': {'index': 2, 'name': 'Qwen2.5-72B', 'id': 'qwen2.5-72b-instruct', 'des': '阿里开源模型', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png'}, 'qwen2.5-math-72b-instruct': {'index': 2, 'name': 'Qwen2.5-Math-72B', 'id': 'qwen2.5-math-72b-instruct', 'des': '阿里开源模型', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png'}, 'qwen2.5-coder-7b-instruct': {'index': 2, 'name': 'Qwen2.5-Coder-7B', 'id': 'qwen2.5-coder-7b-instruct', 'des': '阿里开源模型', 'des_en': 'Tongyi Qianwen', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png'}, 'qwen-math-plus': {'index': 2, 'name': 'Qwen-Math-Plus', 'id': 'qwen-math-plus', 'des': '阿里通义千问数学模型', 'des_en': 'Tongyi Qianwen Math', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_029b_a545629b-973f-4044-9741-488f0b3ee62g.png'}, 'general': {'index': 10, 'name': 'Spark Lite', 'id': 'general', 'des': '讯飞星火初级版', 'des_en': 'Xunfei Spark', 'img_url': 'https://file.302.ai/gpt/imgs/img_v3_02cc_229b3a19-cf64-4cd7-ad41-cdf69a1f5eag.jpg'}, 'o1-mini': {'index': 1, 'name': 'o1-mini', 'id': 'o1-mini', 'des': '最新的草莓模型', 'des_en': 'Latest Strawberry Model', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}, 'o1-preview': {'index': 1, 'name': 'o1-preview', 'id': 'o1-preview', 'des': '最新的草莓mini模型', 'des_en': 'Latest Strawberry Mini Model', 'img_url': 'https://file.302.ai/gpt/cn_gpts/img_v3_02a0_670de935-133a-43da-916c-b092359694ag.jpg'}

}


class JsonContains(Function):
    database_func = CustomFunction("JSON_CONTAINS", ["name", "value"])

# 创建token
def create_token():
    return "sk-" + random_string(48)


async def is_cn(user_id):
    user_info_dict = await UserInfo.filter(uid=user_id).first().values("region")
    if user_info_dict.get("region") == 0:
        return True
    return False

import os
async def upload(data):
    path = data.get("file")
    prefix = data.get("blob_path")
    async with aiofiles.open(path, "rb") as file:
        file = await file.read()
        file_name = Path(path).name
        print(file_name)
        await upload_blob(file, file_name=file_name, pre=prefix.replace("\\", "/"))
    # loop = asyncio.get_event_loop()
    # 使用线程池执行器在另一个线程中运行阻塞调用
    # await loop.run_in_executor(None, os.remove, path)
    try:
        os.remove(path)
    except:
        pass

async def upload_from_url(url, blob_path, name):
    # task_name = "tasks.upload_file.update_from_url"
    data = {"url": url, "blob_path": blob_path, "name": name}
    # await Tools.celery.send_task(task_name, kwargs=data)

    url = data.get("url")
    prefix = data.get("blob_path")
    name = data.get("name")
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            file = await response.read()
        await upload_blob(file, file_name=name, pre=prefix.replace("\\", "/"))


def get_file_extension(url):
    # 解析url上的文件后缀，大概
    parsed_url = urlparse(url)
    file_path = parsed_url.path
    _, extension = os.path.splitext(file_path)
    if extension == '.image':
        extension = '.png'
    return extension


async def cache_summary_date_by_date(day=7):
    """
    后台展示数据的归档,每天统计最近n天的数据
    """
    async def down_data(by_self=False):
        # 获取当前时间
        now = datetime.now()
        num = 2 if by_self else 1

        # 循环处理7天的数据
        for i in range(day):
            # 计算每一天的日期范围
            current_date = now - timedelta(days=i)
            start_date = current_date.strftime("%Y-%m-%d")
            end_date = (current_date + timedelta(days=1)).strftime("%Y-%m-%d")
            Tools.log.debug(f"start_date:{start_date},end_date:{end_date}")
            sql = f"""
            select str_date,
                   str_month,
                   model as key_name,
                   round(sum(total_cost),6) as value_field,
                   {num} as data_type 
            from (
                select FROM_UNIXTIME(created_on+8*3600,'%Y-%m-%d') as str_date,
                       FROM_UNIXTIME(created_on+8*3600,'%Y-%m') as str_month,
                       model,
                       total_cost
                from gpt.t_log
                where created_on >= UNIX_TIMESTAMP('{start_date}') - 8*3600
                and created_on < UNIX_TIMESTAMP('{end_date}') - 8*3600
                and model > '' 
                and total_cost > 0 
                and token_mapping_id {'' if by_self else 'not'} in (
                    select id from gpt.t_token_mapping 
                    where uid in (select uid from proxy.t_users where email like '%@adswave%')
                )
            ) t
            group by str_date, str_month, key_name
            """

            conn = tortoise.connections.get("gpt_conn")
            records = await conn.execute_query_dict(sql)

            # 批量更新或插入数据
            # 批量查找已存在的记录
            exists_records = await TDataStatistics.filter(
                Q(str_date__in=[r['str_date'] for r in records]) &
                Q(key_name__in=[r['key_name'] for r in records]) & 
                Q(data_type__in=[r['data_type'] for r in records])
            ).values('id', 'str_date', 'key_name', 'data_type')

            # 构建已存在记录的查找字典
            exists_dict = {
                (r['str_date'], r['key_name'], r['data_type']): r['id'] 
                for r in exists_records
            }

            # 分离需要更新和插入的记录
            update_records = []
            create_records = []
            
            for record in records:
                key = (record['str_date'], record['key_name'], record['data_type'])
                if key in exists_dict:
                    record['id'] = exists_dict[key]
                    update_records.append(record)
                else:
                    create_records.append(record)

            # 批量更新
            if update_records:
                await TDataStatistics.bulk_update(
                    [TDataStatistics(**r) for r in update_records],
                    fields=['str_month', 'value_field']
                )

            # 批量创建
            if create_records:
                await TDataStatistics.bulk_create(
                    [TDataStatistics(**r) for r in create_records]
                )

    # 分别统计自己和用户的数据
    await down_data()
    await down_data(by_self=True)







async def cache_summary_data():
    """
    缓存归档数据
    """

    """
    -- 获取时区分组的token_id
    select token_id,time_zone from t_token_info where token_id in (select distinct token_mapping_id from t_log where created_on >1728662400 and created_on <1728835200 )

    """
    log_record = await TLog.annotate(start_time=Min("created_on"), max_time=Max("created_on")).first().values(
        "start_time", 'max_time')

    # 归档数据
    @tortoise.transactions.atomic(connection_name="gpt_conn")
    async def summary_data():
        """查询过去n天的数据，删除数据，再重新写入，达成归档"""
        # 获取前天天的日期
        now = datetime.now()
        if not (now.hour == 0 and now.minute ==0):
            return
        start_time = (now - timedelta(days=2)).strftime("%Y-%m-%d")
        data = await TDataStatistics.filter(str_date__gte=start_time,data_type=0).annotate(min_id=Min("id"),max_id=Max("id")).first().values("min_id", "max_id")
        max_id = data.get("max_id")
        min_id = data.get("min_id")
        records = await TDataStatistics.filter(id__gte=min_id,id__lte=max_id,data_type=0).group_by("str_date","str_month","key_name","data_type","uid").annotate(sum_value=Sum("value_field")).values("str_date","str_month","key_name","data_type","uid","sum_value")
        data_statistics_records = []
        for record in records:
            record['value_field'] = round(record.pop('sum_value'),3)
            data_statistics_records.append(TDataStatistics(**record))
        await TDataStatistics.bulk_create(data_statistics_records)
        await TDataStatistics.filter(id__gte=min_id,id__lte=max_id,data_type=0).delete()


    @tortoise.transactions.atomic(connection_name="gpt_conn")
    async def func(start_time):

        conn = tortoise.connections.get("gpt_conn")
        if not start_time:
            start_time = log_record.get("start_time")
        else:
            start_time = int(start_time.decode())
        max_time = log_record.get("max_time")
        end_time = start_time + 3600 * 24 * 10

        Tools.log.debug(f"start_time:{start_time},max_time:{max_time},end_time:{end_time}")
        if end_time>=max_time:
            end_time = max_time
        tz_tokens = f"""
            select tz,JSON_ARRAYAGG(token_id) as token_ids from t_token_info where token_id in 
            (select distinct token_mapping_id from t_log where created_on >={start_time} and created_on <{end_time} ) group by tz
            """
        records = await conn.execute_query_dict(tz_tokens)
        for record in records:
            # 这里可以并行加快速度
            tz = record.get("tz")
            if not tz:
                continue
            token_ids = json.loads(record.get("token_ids"))
            diff_time = get_diff_time_from_zone(tz)
            ins_sql = f"""
                insert into t_data_statistics(str_date,key_name,value_field,uid)
                select DATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {diff_time}),'%%Y-%%m-%%d') as str_date,share_code as key_name,round(sum(total_cost),3) as value_field
                ,uid from t_log   join t_token_mapping on token_mapping_id=t_token_mapping.id where t_log.created_on>={start_time} and t_log.created_on <{end_time} 
                and  token_mapping_id in %s
                group by share_code,str_date,uid HAVING value_field>0 and share_code >''
                """
            await conn.execute_query(ins_sql, (token_ids,))
        await conn.execute_query("update t_data_statistics set str_month = SUBSTR(str_date,1,7) where str_month ='' ")
        await Tools.redis.set("cache_summary_start_time", end_time)
        if end_time >= max_time:
            return True

    stop = False
    while not stop:
        start_time = await Tools.redis.get("cache_summary_start_time")
        stop = await func(start_time)
    await summary_data()





@cache(ttl=3600)
async def get_model_ids():
    model_list = await TModel.all().distinct().values("tool_id_list")
    ids = set(list(flatten([i.get("tool_id_list") for i in model_list])))
    ids.remove(0)

    return {"ids": ids}


async def get_region_form_db(uid):
    region = await UserInfo.filter(uid=uid).first().values("region")
    return region.get("region",0)

@cache(ttl=60*5)
async def get_model_list(use_in, tool_id,uid=0):
    def ceil_to_two_decimal(number):
        if number < 1:
            return math.ceil(number * 100) / 100
        return math.ceil(number * 10) / 10


    query = TModel.all()
    if use_in == "api":
        query = query.filter(show_in_api=True)
    elif use_in == "mj":
        query = query.annotate(has_id=JsonContains("tool_id_list", str(ToolsId.MJ.value))).filter(has_id=True)
    elif use_in == "kb":
        query = query.annotate(has_id=JsonContains("tool_id_list", str(ToolsId.KB.value))).filter(has_id=True)
    elif use_in == 'all':
        ...
    elif use_in in ("tool", 'tools'):
        query = query.annotate(has_id=JsonContains("tool_id_list", str(tool_id))).filter(has_id=True)
    elif use_in in ('kb_rag'):
        query = query.filter(is_embedding__gt=0,dimension__gt=0)
    elif use_in in ('kb'):
        query = query.filter(is_embedding__gt=0)
    elif use_in in ('kb_llm'):
        query = query.annotate(has_id=JsonContains("tool_id_list", str(ToolsId.KB_EMB.value))).filter(has_id=True)
    else:
        query = query.filter(show_in_robot=True)
    is_cn = False
    if uid:
        region = await get_region_form_db(uid)
        is_cn = region==Region.cn.value
        query = query.annotate(in_region = JsonContains("region_support_list",str(region))).filter(in_region=True)
    list = await query.order_by("ord").annotate(name=F('show_name'), real_model=F("name")).values("real_model", "id",
                                                                                                  "name",
                                                                                                  "tool_id_list",'is_cn_default',
                                                                                                  'remark', 'en_remark',
                                                                                                  'is_default',
                                                                                                  'en_model_type','base_rate',
                                                                                                  'model_type','file_support_type',
                                                                                                  'is_support_plugins',"token_supplier_id_list")
    conn = tortoise.connections.get("gpt_conn")
    _ = await conn.execute_query_dict("select name,en_name,logo_url,en_logo_url,jp_name,jp_logo_url from t_category where id <17")
    category_model = {i.get("name"):i for i in _}
    token_supplier_list =await TTokenSupplier.all().values("id","rate")
    model_rate = {i.get("id"):i.get("rate") for i in token_supplier_list}

    model_prices = await TModelPrice.all()
    prices = {i.model: [i.input_price, i.output_price, i.rate] for i in model_prices}
    # prices['deepseek-chat'] = [0.000165, 0.00033]
    # prices['yi-34b-chat-0205'] = [0.00044, 0.00044]
    # prices['command-r-plus'] = [0.003, 0.015]
    # prices['llama3-70b-8192'] = [0.00065, 0.00275]
    # prices['qwen-vl-max'] = [0.00319, 0.00319]
    # prices['midjourney'] = [0.00065, 0.1]
    # prices['stable-diffusion'] = [0.00065, 0.15]
    cost_by_time_model = ("midjourney","stable-diffusion","o1-plus")
    unit = "PTC/1M"
    unit_cn = "PTC/1M"

    if is_cn:
        is_default_key = "is_cn_default"
    else:
        is_default_key = "is_default"
    # model_name = "name" if not api_key
    model_name = "real_model" if use_in == "api" else 'name'
    if use_in == "api":
        list.sort(key=lambda x: x.get(model_name,"").lower())

    for i in list:
        category_model_item = category_model.get(i.get("model_type"),{})
        i["model"] = i.pop(model_name)
        # i['model_type'] = category_model_item.get("name")
        # i['en_model_type'] = category_model_item.get("en_name")
        i['logo_url'] = category_model_item.get("logo_url")
        i['en_logo_url'] = category_model_item.get("en_logo_url")
        i['jp_logo_url'] = category_model_item.get("jp_logo_url")

        i['is_default'] = i[is_default_key] == 1
        price = prices.get(i.get("real_model"), [])
        if not price:
            continue
        rate = 1000 * prices.get(i.get("real_model"), [0, 0, 1])[2] * i.get('base_rate', 1)
        Tools.log.info(f"{i['real_model']} rate:{rate}")
        # if i['model_type'] not in ("国产模型", "开源模型"):
        #     rate = 0.5 * 1000
        if price[0] ==0 or i['real_model'] in cost_by_time_model:
            unit = "PTC/1 time"
            unit_cn = "PTC/1次"
            rate = model_rate.get(eval(i.get("token_supplier_id_list"))[0],1)*i.get('base_rate',1)
            i['price'] = [ f"{round(price[1] * rate, 3)} {unit_cn}"]
            i['price_zh'] = [ f"{round(price[1] * rate, 3)} {unit_cn}"]
            i['price_en'] = [f"{round(price[1] * rate, 3)} {unit}"]
            i['price_jp'] = [f"{round(price[1] * rate, 3)} {unit}"]
        else:
            i['price'] = [f"Input：{ceil_to_two_decimal(price[0] * rate)} PTC/1M", f"Output：{ceil_to_two_decimal(price[1] * rate)} PTC/1M"]
            i['price_zh'] = [f"输入：{ceil_to_two_decimal(price[0] * rate)} PTC/1M", f"输出：{ceil_to_two_decimal(price[1] * rate)} PTC/1M"]
            i['price_en'] = [f"Input：{ceil_to_two_decimal(price[0] * rate)} PTC/1M", f"Output：{ceil_to_two_decimal(price[1] * rate)} PTC/1M"]
            i['price_jp'] = [f"Input：{ceil_to_two_decimal(price[0] * rate)} PTC/1M", f"Output：{ceil_to_two_decimal(price[1] * rate)} PTC/1M"]
        i.pop("token_supplier_id_list")
    return {"data": list}


@tortoise.transactions.atomic(connection_name="gpt_conn")
async def create_user_gpts_token(uid, **kwargs):
    data: GptToken = kwargs.get("body")
    gpts = await TOpenaiGpts.filter(gizmo_id=data.gpts_code).first()
    if gpts:
        share_code = data.share_code

        mapping = await TTokenMapping.create(user_id=uid, external_token_id=0, internal_token_id=0,
                                             name=data.name, share_code=share_code.lower(), is_robot=data.is_robot,
                                             remark=data.remark,
                                             model_id=0, gpts_code=gpts.gizmo_id,
                                             status=GPTStatus.NORMAL.value, expired_on=0,
                                             limit_cost=data.limit_cost * 1000)
        if not data.external_code:
            data.external_code = ""
        await TTokenInfo.create(token_id=mapping.id, tz=data.tz, limit_daily_cost=data.limit_daily_cost * 1000,
                                external_code=data.external_code)

        return mapping.id


async def search_gpts_by_code(code):
    gpts = await TOpenaiGpts.filter(gizmo_id=code).select_related("openai_author").first()
    if gpts:
        gpts_msg = {
            "id": gpts.id,
            "code": gpts.gizmo_id,
            "name": gpts.display_name,
            "description": gpts.display_description,
            "author": gpts.openai_author.display_name,
            "prompt_starters": json.loads(gpts.prompt_starters) if gpts.prompt_starters else '',
            "logo_url": gpts.profile_picture_url if not gpts.blob_img_url else gpts.blob_img_url
        }
        return gpts_msg

async def get_api_key_limit_func(token_id,uid):
    api_limit = await TApiLimit.filter(token_id=token_id,uid=uid).first()
    if not api_limit:
        return None
    ip_records = await TIpList.filter(limit_id=api_limit.id,deleted_on=0).all()
    blacklist = ''
    whitelist = ''
    for ip_record in ip_records:
        if ip_record.list_type == ListType.BLACKLIST.value:
            if ip_record.ip_type == IpType.RANGE.value:
                blacklist += f"{ip_record.ip_range_start}-{ip_record.ip_range_end}\n"
            elif ip_record.ip_type == IpType.SUBNET.value:
                blacklist += f"{ip_record.ip_subnet}\n"
            else:
                blacklist += f"{ip_record.ip_address}\n"
        else:
            if ip_record.ip_type == IpType.RANGE.value:
                whitelist += f"{ip_record.ip_range_start}-{ip_record.ip_range_end}\n"
            elif ip_record.ip_type == IpType.SUBNET.value:
                whitelist += f"{ip_record.ip_subnet}\n"
            else:
                whitelist += f"{ip_record.ip_address}\n"
    data = {
        "ip_blacklist": blacklist,
        "ip_whitelist": whitelist,
        "limit_balance": api_limit.limit_balance,
        "remark":api_limit.remark,
        "model_id_list": api_limit.model_id_list

    }
    return data


@tortoise.transactions.atomic(connection_name="gpt_conn")
async def limit_api_key_func(body: ApiKeyLimit,**kwargs):
    token_id = body.token_id
    limit_balance = body.limit_balance
    remark = body.remark

    ip_black_list = filter(lambda x:x,(IP(b,ListType.BLACKLIST).check().data for b in body.ip_blacklist))
    ip_white_list = filter(lambda x:x,(IP(b,ListType.WHITELIST).check().data for b in body.ip_whitelist))

    # if body.model_id_list is not None:
    #     # 把数组记录写入库
    #     model_id_list = body.model_id_list

    mapping = await TTokenMapping.filter(id=token_id,user_id=kwargs.get("uid")).first().values("id","user_id")
    if not mapping:
        return suc_data()

    mapping_id = mapping.get("id") if mapping else 0
    uid = mapping.get("user_id") if mapping else 0
    del_all_ip_flag = False
    status = bool(body.ip_blacklist or body.ip_whitelist or body.model_id_list)
    if api_limit := await TApiLimit.filter(token_id=mapping_id).first():
        del_all_ip_flag = True
        await TApiLimit.filter(token_id=mapping_id).update(limit_balance=limit_balance,remark=remark,model_id_list=body.model_id_list or [],status=status)
    else:
        api_limit = await TApiLimit.create(token_id=mapping_id,uid=uid,limit_balance=limit_balance,remark=remark,model_id_list=body.model_id_list or [],status=status)
    records = []
    for i in ip_black_list:
        records.append(TIpList(status=1,limit_id=api_limit.id,**i))
    for i in ip_white_list:
        records.append(TIpList(status=1,limit_id=api_limit.id,**i))
    if del_all_ip_flag:
        await TIpList.filter(limit_id=api_limit.id).update(deleted_on=current_timestamp(),status=0)
    await TIpList.bulk_create(records)
    return suc_data()



@cache(ttl=3600)
async def get_tool_models_list(uid=0):
    tool_list = await TGptTool.filter(deleted_on=0,id__not_in=(9, ToolsId.GPTs.value), id__gte=ToolsId.OTHER.value).values("id",
                                                                                                              "name",
                                                                                                              "en_name","jp_name",
                                                                                                              "extra")
    model_dict = await get_model_list('all', None,uid=uid)
    res = []
    for i in tool_list:
        model_list = [m for m in model_dict.get("data") if i.get("id") in m.get("tool_id_list")]
        res.append(
            {
                "id": i.get("id"),
                "name": i.get("name"),
                "en_name": i.get("en_name"),
                "jp_name": i.get("jp_name"),
                "path": i.get("extra", {}).get("path"),
                "model_list": model_list,
                "has_model": bool(model_list)
            }
        )
    return res

async def create_or_get_user_aibox(user_id):
    mapping = await TTokenMapping.filter(user_id=user_id,deleted_on=0,tool_id=ToolsId.AI_BOX.value).first()
    if mapping:
        data = await TTokenInfo.filter(token_id=mapping.id).first()
        tool = await TGptTool.filter(id=mapping.tool_id).first()
        cn = await is_cn(user_id)
        doamin = tool.cn_domain if cn else tool.domain
        return {
            "domain": doamin,
            "pwd": data.external_code,
            "share_code": mapping.share_code,
            "prefix": tool.prefix,
            'detail': {
                "id": mapping.id,
                "share_code": mapping.share_code,
                "remark": mapping.remark,
                "model_id": mapping.model_id,
                "external_code": data.external_code,
                "limit_daily_cost": data.limit_daily_cost,
                "limit_cost": data.limit_daily_cost,
                "limit_monthly_cost": data.limit_monthly_cost,
            }
        }
    else:
        models = await TModel.all().values("id","tool_id_list","name")
        box_list = await TGptTool.filter(id__gte=3).values("id")
        tool_model = {}
        paint_model_id = 0
        chat_model_id = 0


        for model in models:
            for tool in model.get("tool_id_list"):
                if tool not in tool_model:
                    tool_model[tool] = []
                tool_model[tool].append(model.get("id"))
            if model.get("name") == "midjourney":
                paint_model_id = model.get("id")
            if model.get("name") == "gpt-4o":
                chat_model_id = model.get("id")
        body = GptToken()
        body.is_robot=1
        body.external_code = random_string(4, uppercase=False, lowercase=False)
        body.tool_id=ToolsId.AI_BOX.value
        body.tool_id_conf = [
      {
        "path": "/paint",
        "tools": [
          {
            "extra": {},
            "tool_id": -1,
            "model_id": paint_model_id
          }
        ],
        "enable": True
      },
      {
        "path": "/chat",
        "tools": [
          {
            "extra": {
              "open_tts": 1,
              "use_gpts": 1,
              "enable_plugins": 1
            },
            "tool_id": 1,
            "model_id": chat_model_id
          }
        ],
        "enable": True
      },
      {
        "path": "/api",
        "tools": [
          {
            "extra": {},
            "tool_id": 0,
            "model_id": 0
          }
        ],
        "enable": True
      },
      {
        "path": "/tools",
        "tools": [{
            "extra": {},
            "tool_id": tool.get("id"),
            "model_id": tool_model.get(tool.get("id"))[0] if tool_model.get(tool.get("id")) else 0
          } for tool in box_list]
         ,
        "enable": True
      }
    ]

        body.tool_id_conf = [tool_config(**i) for i in body.tool_id_conf]
    return await create_user_gpt_token(uid=user_id, body=body)






@tortoise.transactions.atomic(connection_name="gpt_conn")
async def create_user_gpt_token(uid, **kwargs):
    """
    创建用户对应的gpt api key token
    """
    data: GptToken = kwargs.get("body")
    is_gpts: bool = kwargs.get("is_gpts")
    status = kwargs.get("status",GPTStatus.NORMAL.value)
    Tools.log.debug(f"data:{data}")
    is_get_from_db = data.tool_id in (7,) and (not data.share_code and not data.name)

    mapping = None
    if is_get_from_db:
        mapping = await TTokenMapping.filter(user_id=uid, tool_id=data.tool_id, deleted_on=0).first()
        if mapping:
            info = await TTokenInfo.filter(token_id=mapping.id).first().values("external_code")
            data.external_code = info.get("external_code")
        share_code = data.share_code if data.share_code else random_string(4, uppercase=False)
        data.share_code = mapping.share_code if mapping else share_code
        data.external_code = pwd = data.external_code if data.external_code else random_string(4, uppercase=False,
                                                                                               lowercase=False)

    if not mapping:
        token = create_token()
        token = await TToken.create(value=token, token_supplier_id=0, **kwargs)
        # 空对象创建竞技模型的情况下，获取已经创建的工具返回就好了
        # 绑定
        data.share_code = share_code = data.share_code if data.share_code else random_string(4, uppercase=False)

        # data.name = name = data.name if data.name else random_string(8,uppercase=False)
        is_exists = await TTokenMapping.filter(share_code=share_code, deleted_on=0).exists()
        if is_exists:
            raise ShareCodeExistsErr()
        if not data.is_robot:
            is_exists = await TTokenMapping.filter(name=(data.name or random_string(6, uppercase=False)), deleted_on=0, user_id=uid).exists()
            if is_exists:
                raise ShareCodeExistsErr()

        if not data.is_robot:
            tools_id = ToolsId.API.value
        else:
            if data.gpts_code > '':
                tools_id = ToolsId.GPTs.value
            else:
                tools_id = ToolsId.Chatbot.value

        code_name = {}
        if is_gpts and data.gpts_code and not data.gpts_code.startswith("g-"):
            code_name = {"name": data.gpts_code}
            sub_query = TModel.filter(Q(name=data.gpts_code) | Q(show_name=data.gpts_code)).values("token_supplier_id")
        elif is_gpts:
            sub_query = TTokenSupplier.filter(deleted_on=0).values("id")
        else:
            sub_query = TModel.filter(id=data.model_id).values("token_supplier_id")
        v = await TToken.filter(token_supplier_id__in=Subquery(sub_query)).values("id")
        if not v:
            v = await TToken.filter(
                token_supplier_id__in=Subquery(TTokenSupplier.filter(deleted_on=0).values("id"))).values("id")
        if data.tool_id:
            tools_id = data.tool_id
        mapping = await TTokenMapping.create(user_id=uid, external_token_id=token.id, internal_token_id=v[0].get("id"),
                                             name=data.name, share_code=share_code.lower(), is_robot=data.is_robot,
                                             enable_plugins=data.enable_plugins,
                                             remark=data.remark, gpts_code=code_name.get("name") or data.gpts_code,
                                             tool_id=tools_id,
                                             model_id=data.model_id if data.model_id else 0,
                                             status=status,
                                             expired_on=data.expired_on if data.expired_on else 0,
                                             limit_cost=data.limit_cost * 1000)
        if not data.external_code:
            data.external_code = ""
        settings = {}
        use_gpts = data.use_gpts
        open_tts = data.open_tts
        if is_gpts:
            if data.gpts_code.startswith('g-'):

                gpts_info = await TOpenaiGpts.filter(gizmo_id=data.gpts_code).first().values("profile_picture_url",
                                                                                             "display_name",
                                                                                             'blob_img_url')
                if not gpts_info:
                    """
                      "extra": {
                "name": "客流猫-收钱文案拆解与模仿专家",
                "code": "g-FVHmAsasn",
                "logo_url": "https://files.oaiusercontent.com/file-Rwb65ekvEN2HITzfxMKzmxdo?se=2124-01-05T14%3A48%3A32Z&sp=r&sv=2021-08-06&sr=b&rscc=max-age%3D1209600%2C%20immutable&rscd=attachment%3B%20filename%3D%25E5%25AE%25A2%25E6%25B5%2581%25E7%258C%25AB-LOGO.jpg&sig=lANuGDijStsbCWnQfdc8cr2WjE7eLQ3zHxfiA2AbbWE%3D",
                "author": "community builder",
                "description": "帮你自动拆解任何收钱文案公式，再帮你100%批量撰写爆款收钱文案？微信搜索【客流猫课堂】",
                "content": "客流猫-收钱文案拆解与模仿专家\n\nBy community builder\n\n帮你自动拆解任何收钱文案公式，再帮你100%批量撰写爆款收钱文案？微信搜索【客流猫课堂】",
                "prompt_starters": []
              }
              """

                    gpts_info = data.extra
                    gpts_info['blob_img_url'] = gpts_info.get("logo_url")
                    gpts_info['display_name'] = gpts_info.get("name")
                settings = {
                    "chatbotLogo": gpts_info.get("blob_img_url") if gpts_info.get("blob_img_url") else gpts_info.get(
                        "profile_picture_url"),
                    "chatbotName": gpts_info.get("display_name")
                }
                use_gpts = 0
                open_tts = 0
            else:

                t = ai_names.get(data.gpts_code, {})
                settings = {
                    "chatbotLogo": t.get("img_url", ''),
                    "chatbotName": t.get("name"),
                    "chatDes": t.get("des"),
                    "prompt": t.get("detail")
                }
                # todo json数据获取setting配置
                use_gpts = 0
                open_tts = 0

        if mapping.tool_id == ToolsId.AI_BOX.value:

            settings = {"tool_conf": [i.model_dump() for i in data.tool_id_conf]}
        extra = data.extra
        if mapping.tool_id == ToolsId.KB.value:
            kb = await TKnowledgeBase.get(id=data.kb_id).values("kb_name")
            extra = {"kb_id": data.kb_id, "kb_name": kb.get("kb_name")}
        await TTokenInfo.create(token_id=mapping.id, tz=data.tz, limit_daily_cost=data.limit_daily_cost * 1000,
                                limit_monthly_cost=data.limit_monthly_cost * 1000,
                                settings=settings, use_gpts=use_gpts, open_tts=open_tts,extra=extra,
                                external_code=data.external_code)
    tool = await TGptTool.filter(id=kwargs.get("tool_id") or mapping.tool_id).first()
    if tool.id == ToolsId.AI_BOX.value:

        objs = []
        for k in data.tool_id_conf:
            for i in k.tools:
                objs.append(TAiAppBox(
                    **{"token_id": mapping.id, "tool_id": i.tool_id, "model_id": i.model_id, "extra": i.extra}))
        if objs:
            await TAiAppBox.bulk_create(objs)
    cn = await is_cn(uid)
    doamin = tool.cn_domain if cn else tool.domain
    return {
        "domain": doamin,
        "pwd": data.external_code,
        "share_code": data.share_code,
        "prefix": tool.prefix,
        "id": mapping.id,
        'detail': {
            "id": mapping.id,
            "share_code": data.share_code,
            "remark": data.remark,
            "model_id": data.model_id,
            "external_code": data.external_code,
            "limit_daily_cost": data.limit_daily_cost,
            "limit_monthly_cost": data.limit_monthly_cost,
            "limit_cost": data.limit_cost,
        }
    }


@tortoise.transactions.atomic(connection_name="gpt_conn")
async def update_user_gpt_token(uid, token_id, **kwargs):
    token = await TTokenMapping.filter(user_id=uid, id=token_id, deleted_on=0).first()

    if not token:
        res = kwargs.get("res")
        res["code"] = StateCode.ShareCodeNotFound.value
        res["msg"] = "token不存在"
        return False
    data: GptToken = kwargs.get("body")
    all_body = kwargs.get("all_body")
    is_gpts: bool = kwargs.get("is_gpts")
    if data.name:
        if not data.is_robot:
            if await TTokenMapping.filter(name=data.name, id__not=token_id, deleted_on=0, user_id=uid).exists():
                raise ShareCodeExistsErr()
        token.name = data.name
    if data.expired_on is not None:
        if token.status == GPTStatus.EXPIRED:
            if data.expired_on == 0 or data.expired_on > current_timestamp():
                token.status = GPTStatus.NORMAL.value
        token.expired_on = data.expired_on
    if data.limit_cost is not None:
        token.limit_cost = data.limit_cost * 1000
    if data.enable_plugins is not None:
        token.enable_plugins = data.enable_plugins
    token.remark = data.remark
    if data.model_id:
        _ = await TModel.filter(id=data.model_id).first().values("token_supplier_id_list")
        if _.get("token_supplier_id_list") and _.get("token_supplier_id_list") > "":
            t = await TToken.filter(token_supplier_id=eval(_.get("token_supplier_id_list"))[0]).first()
        else:
            t = await TToken.filter(
                token_supplier_id=Subquery(TTokenSupplier.filter(deleted_on=0).first().values("id"))).first()
        if not t:
            t = await TToken.filter(
                token_supplier_id=Subquery(TTokenSupplier.filter(deleted_on=0).first().values("id"))).first()

        token.internal_token_id = t.id
        token.model_id = data.model_id
        if _ := await TModel.filter(id=data.model_id, name__in=Subquery(
                TModelPrice.filter(id__gte=46, id__lte=67).values("model"))).first().values("name"):
            model_name: str = _.get("name")
            if model_name.startswith("moonshot"):
                v = await TToken.filter(value='sk-VeXpLX6XJ9k682h1kBo68LQ9b0axkIk6BVBWf68Flt1hCGmD').first().values(
                    "id")
            else:
                v = await TToken.filter(value='sk-fJZRRcZ4UPgk6x2UC9C030Eb11484cA29172A1029d67574d').first().values(
                    "id")
            token.internal_token_id = v.get("id")
    data.share_code = data.share_code.lower()
    if token.name == 'default_box':
        data.share_code = ""
    if data.share_code:
        if await TTokenMapping.filter(share_code=data.share_code, id__not=token_id, deleted_on=0).exists():
            res = kwargs.get("res")
            res["code"] = StateCode.ShareCodeExists.value
            res["msg"] = "分享码已存在"
            return False
        token.share_code = data.share_code
    update_dict = {}
    update_dict["external_code"] = data.external_code
    update_dict["use_gpts"] = data.use_gpts
    update_dict["open_tts"] = data.open_tts
    if is_gpts:
        cn_ai = ''
        # 为了错开setting上传的接口
        t_info = await TTokenInfo.filter(token_id=token_id).first().values("settings")
        settings = {}
        if data.gpts_code and not data.gpts_code.startswith('g-'):
            # setting 也要变更

            _ = await TModel.filter(Q(name=data.gpts_code)).first().values("token_supplier_id_list", 'name')
            if _.get("token_supplier_id_list") and _.get("token_supplier_id_list") > "":
                t = await TToken.filter(token_supplier_id=eval(_.get("token_supplier_id_list"))[0]).first()
                ai = ai_names.get(data.gpts_code, {})

                token.internal_token_id = t.id
                settings = {
                    "chatbotLogo": ai.get("img_url", ''),
                    "chatbotName": ai.get("name"),
                    "chatDes": ai.get("des")
                }
                token.internal_token_id = t.id
                cn_ai = _.get("name")
        else:
            gpts_info = await TOpenaiGpts.filter(gizmo_id=data.gpts_code).first().values("profile_picture_url",
                                                                                         "display_name",
                                                                                         'blob_img_url')
            settings = {
                "chatbotLogo": gpts_info.get("blob_img_url") if gpts_info.get("blob_img_url") else gpts_info.get(
                    "profile_picture_url"),
                "chatbotName": gpts_info.get("display_name")
            }

        v = t_info.get("settings")

        def set_value(key):
            if key in v:
                v[key.split('_')[-1]] = v[key]

        if v:
            v.update(settings)
        set_value("user_chatDes")
        set_value("user_chatbotDesc")
        set_value("user_chatbotLogo")
        set_value("user_chatbotName")
        update_dict['settings'] = v or settings
        Tools.log.debug(f"add key change: {update_dict['settings']}")
        token.gpts_code = cn_ai or data.gpts_code
    if data.limit_daily_cost is not None:
        update_dict["limit_daily_cost"] = data.limit_daily_cost * 1000
        _ = await TTokenInfo.filter(token_id=token_id).first().values("current_date_cost")
        if not _:
            current_date_cost = 0
        else:
            current_date_cost = _.get("current_date_cost", 0)
        if not current_date_cost:
            current_date_cost = 0
        if data.limit_daily_cost == 0 or update_dict["limit_daily_cost"] > current_date_cost:
            if token.status == GPTStatus.LIMIT_CURRENT_DATE.value and await TUser.filter(uid=token.user_id,
                                                                                         is_indebted=False).exists():
                token.status = GPTStatus.NORMAL.value

    if data.limit_monthly_cost is not None:
        update_dict["limit_monthly_cost"] = data.limit_monthly_cost * 1000
        _ = await TTokenInfo.filter(token_id=token_id).first().values("current_month_cost")
        current_month_cost = _.get("current_month_cost", 0) if _ else 0
        if data.limit_monthly_cost == 0 or update_dict["limit_monthly_cost"] > current_month_cost:
            if token.status == GPTStatus.LIMIT_CURRENT_MONTH.value and await TUser.filter(uid=token.user_id, is_indebted=False).exists():
                token.status = GPTStatus.NORMAL.value

    if token.tool_id == ToolsId.AI_BOX.value:
        all_body = kwargs.get("all_body")
        tmp = {}
        for k,v in all_body.items():
            if k.startswith("settings_"):
                tmp[k] = v

        # update_dict['settings'] = {"custom_home_page":data.custom_home_page,"tool_conf": [i.model_dump() for i in data.tool_id_conf],"tool_nav_count":data.tool_nav_count ,'show_balance':data.show_balance,'hide_home_page':data.hide_home_page,'banners':data.banners}
        # update_dict['settings'].update(tmp)
        # _ = await TTokenInfo.filter(token_id=token_id).first().values("settings")
        # _["settings"].update(update_dict.get("settings"))
        # update_dict['settings'] = _['settings']
    if data.kb_id:
        kb = await TKnowledgeBase.get(id=data.kb_id).values("kb_name")
        update_dict['extra'] = {
            "kb_id": data.kb_id,
            "kb_name": kb.get("kb_name")
        }
    await TTokenInfo.update_or_create(token_id=token_id, defaults=update_dict)
    # await TTokenInfo.filter(token_id=token_id).update(limit_daily_cost=data.limit_daily_cost * 1000)
    await token.save()
    await to_action(token_id)

    if token.tool_id == ToolsId.AI_BOX.value:
        for k in data.tool_id_conf:

            for i in k.tools:
                box = await TAiAppBox.filter(token_id=token_id, tool_id=i.tool_id).first()
                if not box:
                    await TAiAppBox.create(**{"token_id": token.id, "tool_id": i.tool_id})
                else:
                    box.model_id = i.model_id
                    box.extra = i.extra
                    await box.save()

    return True


async def get_gpt_detail(uid, token_id):
    token = await TTokenMapping.filter(user_id=uid, id=token_id).select_related("external_token").first().values(
        "external_token__value")

    if not token:
        return False
    data = {'value': token.get("external_token__value")}

    return data


async def enable_or_disable_gpt(uid, token_id):
    token = await TTokenMapping.filter(user_id=uid, id=token_id).first()
    if not token:
        return False
    token.enable = not token.enable
    if token.status in (GPTStatus.DISABLE.value, GPTStatus.NORMAL.value):
        token.status = GPTStatus.DISABLE.value if token.status == GPTStatus.NORMAL and not token.enable else GPTStatus.NORMAL.value
    await token.save()
    return True


async def del_user_gpt_token(uid, token_id):
    """
    删除用户对应的gpt api key token
    """
    now = current_timestamp()
    # 删除token
    await TToken.filter(id=token_id).update(deleted_on=now, modified_on=now)
    # 删除绑定
    await TTokenMapping.filter(user_id=uid, id=token_id).update(deleted_on=now, modified_on=now,
                                                                status=GPTStatus.DELETED.value)
    await TTokenInfo.filter(token_id=token_id).update(extra={})
    # 删除对应的工具集成
    await TRobotMapping.filter(token_id=token_id).update(deleted_on=now)
    await to_action(token_id)
    return True

@tortoise.transactions.atomic("gpt_conn")
async def copy_gpt_token_func(uid, token_id):
    async def get_random_share_code():
        """生成随机分享码"""
        while True:
            share_code = random_string(6, number=True, uppercase=False, lowercase=True)
            if not await TTokenMapping.filter(share_code=share_code).exists():
                return share_code

    async def create_api_limit(token_id: int, new_token_id: int):
        """创建API限制"""
        api_limit = await TApiLimit.filter(token_id=token_id).first().values()
        if not api_limit:
            return
            
        api_limit['token_id'] = new_token_id
        limit_id = api_limit.pop("id")
        new_limit = await TApiLimit.create(**api_limit)

        ip_list = await TIpList.filter(limit_id=limit_id).all().values()
        if ip_list:
            ip_list_obj = []
            for ip in ip_list:
                ip['limit_id'] = new_limit.id
                ip.pop("id")
                ip_list_obj.append(TIpList(**ip))
            await TIpList.bulk_create(ip_list_obj)

    async def create_token_info(token_id: int, new_token_id: int):
        """创建Token信息"""
        info = await TTokenInfo.filter(token_id=token_id).first().values()
        if not info:
            return fail_data(code=-101, msg="token info不存在")
            
        info.pop("id")
        info['token_id'] = new_token_id
        for name in ('current_date_cost','current_month_cost','current_hour_cost','created_on','modified_on'):
            info.pop(name)
        if info.get("external_code"):
            info['external_code'] = random_string(4, uppercase=False, lowercase=False)
        info['modified_on'] = current_timestamp()
        await TTokenInfo.create(**info)

    # 获取原token
    token = await TTokenMapping.filter(user_id=uid, id=token_id).first().values()
    if not token:
        return fail_data(code=-101, msg="token不存在")

    # 创建新token
    token.pop("id")
    share_code = await get_random_share_code()
    token['share_code'] = share_code
    if token.get("tool_id") == ToolsId.API.value:
        token['name'] = share_code

    # 创建外部token
    value = create_token()
    token_obj = await TToken.create(value=value, token_supplier_id=0)
    token['external_token_id'] = token_obj.id
    token['created_on'] = token['modified_on'] = current_timestamp()
    token.pop("current_cost")
    
    new_token = await TTokenMapping.create(**token)

    # 处理API限制
    if token.get("tool_id") == ToolsId.API.value:
        await create_api_limit(token_id, new_token.id)

    # 创建token信息
    await create_token_info(token_id, new_token.id)
    
    return suc_data()

async def get_user_gpt_token_list(uid, token_name, api_key, page, page_size, is_robot=1, **kwargs):
    """
    获取用户对应的gpt api key token 列表
    """
    filter_type = kwargs.get("filter_type", 'token_name')
    text = kwargs.get("text", '')
    is_gpts = kwargs.get("is_gpts", False)
    type = kwargs.get("type", '')

    filter_value = {"user_id": uid, "deleted_on": 0, "is_robot": is_robot}
    if type == 'drawing':
        filter_value['tool_id'] = -1
    elif type == "chatbot":
        filter_value['tool_id__in'] = [ToolsId.Chatbot.value, ToolsId.CustomModel.value]
    elif type == "gpts":
        filter_value['tool_id'] = ToolsId.GPTs.value
        filter_value['model_id'] = 0
        filter_value['gpts_code__gt'] = ''
    elif type == "kb":
        filter_value['tool_id'] = ToolsId.KB.value
    elif type == 'tools':
        filter_value['tool_id__gte'] = ToolsId.OTHER.value
        filter_value['tool_id__not'] = ToolsId.AI_BOX.value
    elif type == "toolbox":
        filter_value['tool_id'] = ToolsId.AI_BOX.value

    else:
        filter_value['tool_id'] = ToolsId.API.value



    if filter_type == 'token_name':

        if not is_robot:
            filter_key = "name__contains"
        else:
            filter_key = "share_code__contains"
        filter_value[filter_key] = text

    elif filter_type == 'remark':
        filter_value["remark__contains"] = text
    elif filter_type == 'external_code':
        filter_value["info__external_code__contains"] = text
    elif filter_type == 'tool_name':
        lang = kwargs.get("lang", 'zh-CN')
        language_fields = {
            "zh-CN": "tool__name__contains",
            "en-US": "tool__en_name__contains",
            "ja-JP": "tool__jp_name__contains",
        }
        lang_field = language_fields.get(lang, "tool__name__contains")
        filter_value[lang_field] = text
    elif filter_type == 'full_name':
        lang = kwargs.get("lang", 'zh-CN')
        language_fields = {
            "zh-CN": "tool__name",
            "en-US": "tool__en_name",
            "ja-JP": "tool__jp_name",
        }
        lang_field = language_fields.get(lang, "tool__name")
        filter_value[lang_field] = text

    if api_key:
        filter_value["external_token__value__contains"] = api_key
    query = TTokenMapping.filter(**filter_value).filter(status__lt=2)
    if token_name:
        query = query.filter(name__contains=token_name)

    # 地域判断
    _ = await UserInfo.filter(uid=uid).first().values("region")
    region = _.get("region")
    models = await TModel.annotate(in_region=JsonContains("region_support_list", str(region))).filter(in_region=True).values("id","name")
    query = query.filter(model_id__in=([i.get("id") for i in models]+[0]))

    records = await query.select_related("external_token").select_related("model").select_related(
        "info").select_related("tool").select_related("custom_models").order_by(
        "-id").limit(page_size).offset(
        (page - 1) * page_size).values("id",
                                       "external_token__value",
                                       "info__limit_daily_cost","info__extra",
                                       "info__settings", "tool__domain","tool__cn_domain", 'tool__logo_url', 'tool_id','tool__en_logo_url',
                                       'tool__en_description', 'tool__description', 'tool__en_name',
                                       'tool__tool_logo_video_url','tool__en_tool_logo_video_url','tool__jp_logo_url','tool__jp_tool_logo_video_url',
                                       'tool__name', "tool__prefix",'tool__jp_name','tool__jp_description',
                                       "info__current_date_cost","info__limit_monthly_cost","info__current_month_cost",
                                       "info__external_code","model__input_token","model__output_token","model__max_token",
                                       "remark", "model__name", "model_id", "model__show_name",
                                       "custom_models__show_name", "custom_models__model", 
                                       "name", "status",
                                       "current_cost",
                                       "limit_cost",
                                       "enable", 'enable_plugins',
                                       "expired_on", 'info__use_gpts', 'info__open_tts',
                                       "created_on", "share_code", "gpts_code",
                                       "deleted_on")
    count = await query.count()
    res = {}
    res_model = {}
    if is_gpts:
        gpts_list = await TOpenaiGpts.filter(gizmo_id__in=[i.get("gpts_code") for i in records]).select_related(
            "openai_author")
        res = {gpts.gizmo_id: {"code": gpts.display_name, "id": {gpts.id}} for gpts in gpts_list}
        model_list = await TModel.all().values("show_name","name")
        res_model = {i.get("name"):i.get("show_name") for i in model_list}
    data_list = []
    ids = [i.get("id") for i in records]
    apps = await TRobotMapping.filter(token_id__in=ids).all()
    kb_ids = []
    for i in records:
        extra = i.get('info__extra', {})
        if not extra:
            continue
        _id = extra.get("kb_id",0)
        kb_ids.append(_id)
    if kb_ids:
        kbs = await TKnowledgeBase.filter(id__in=kb_ids).values("id","kb_name")
        kb_dict = {i.get("id"):i.get("kb_name") for i in kbs}
    else:
        kb_dict = {}
    app_dict = {dict(i).get("token_id"): i for i in apps}
    cn = await is_cn(uid)
    doamin = "tool__cn_domain" if cn else "tool__domain"
    domain2  = "tool__cn_domain" if not cn else "tool__domain"
    for i in records:
        apps = app_dict.get(i.get("id"))
        if i.get('limit_cost') and  i.get('current_cost',0)>=i.get('limit_cost',0):
            ...
        data = {"robot_config": apps.to_dict() if apps else {}}
        if apps:
            data['robot_config']["dingtalk_url"] = Tools.config.app_conf.get('dingtalk_url','')+f"/{apps.dingtalk_webhook}"
        i["model_name"] = i["model__show_name"]
        if is_gpts:
            i["model_name"] = res.get(i["gpts_code"], {}).get("code") or res_model.get(i["gpts_code"], '') or i['info__extra'].get("name","")
            i["gpts_id"] = res.get(i["gpts_code"], {}).get("id")
            i['model__name'] = f"gpt-4-gizmo-{i['gpts_code']}" if i['gpts_code'].startswith("g-") else i["gpts_code"]

        data['is_gpts'] = is_gpts
        data["id"] = i["id"]
        data['kb_id'] = i['info__extra'].get("kb_id",0) if i["tool_id"] == ToolsId.KB.value else 0
        data['kb_name'] = kb_dict.get(data['kb_id'],"")
        data["use_gpts"] = i["info__use_gpts"]
        data["domain"] = i[doamin]
        data["domain2"] = i[domain2]
        data["enable_plugins"] = i["enable_plugins"]
        data["open_tts"] = i["info__open_tts"]
        data["gpts_code"] = i["gpts_code"] if i["gpts_code"] else ''
        data['input_token'] =  i.get("model__input_token", 0)
        data['output_token']= i.get("model__output_token", 0)
        data['max_token']= i.get("model__output_token", 0)
        data['value'] = i["external_token__value"][:6] + "*" * 6 + i["external_token__value"][
                                                                   -6:] if not is_gpts else ''
        data['key'] = i["external_token__value"]
        data['url'] = f'http://{Tools.config.gpt_host}/#/?bot={i["share_code"]}'
        data['default_box'] = i["name"]=='default_box'
        if data["default_box"]:
            i["share_code"] = "default"
        data['share_code'] = i["share_code"].lower()
        data['external_code'] = i["info__external_code"]
        data["name"] = i["name"]
        data["settings"] = i["info__settings"]
        data["limit_daily_cost"] = to_ptc(i["info__limit_daily_cost"], 4) or 0
        data["model_id"] = i["model_id"]
        if i["tool_id"] == ToolsId.CustomModel.value:
            data["model"] = i["custom_models__show_name"] if i["custom_models__show_name"] else ""
            data["real_model"] = i["custom_models__model"] if i["custom_models__model"] else ""
        else:        
            data["model"] = i["model_name"] if i["model_name"] else ""
            data["real_model"] = i["model__name"] if i["model__name"] else ""
        data["current_cost"] = to_ptc(i["current_cost"], 4)
        data["today_cost"] = to_ptc(i["info__current_date_cost"], 4)
        data["limit_cost"] = to_ptc(i["limit_cost"], 4)
        data["limit_monthly_cost"] = to_ptc(i["info__limit_monthly_cost"], 4) or 0
        data["month_cost"] = to_ptc(i["info__current_month_cost"], 4)
        data["status"] = i["status"]
        data["created_on"] = i["created_on"]
        data["expired_on"] = i["expired_on"]
        data["deleted_on"] = i["deleted_on"]
        data["tool_jp_name"] = i["tool__jp_name"]
        data["tool_jp_description"] = i["tool__jp_description"]
        data["enable"] = i["enable"]
        data["remark"] = i["remark"]
        data['tool_id'] = i['tool_id']
        data['tool_en_name'] = i['tool__en_name']
        data['tool_name'] = i['tool__name']
        data['tool_logo_video_url'] = i['tool__tool_logo_video_url']
        data['tool_logo_video_url_en'] = i['tool__en_tool_logo_video_url']
        data['tool_logo_video_url_jp'] = i['tool__jp_tool_logo_video_url']
        data['tool_logo_url'] = i['tool__logo_url']
        data['tool_logo_url_en'] = i['tool__en_logo_url']
        data['tool_logo_url_jp'] = i['tool__jp_logo_url']
        data['tool_en_description'] = i['tool__en_description']
        data['tool_prefix'] = i['tool__prefix']
        data['tool_description'] = i['tool__description']
        data_list.append(data)
    return {"total": count, "data": data_list, "page": page, "page_size": page_size}


async def get_value_from_code(code, pwd, lang='en', pre="", domain="",**kwargs):
    b64_uid = kwargs.get("uid")
    query = dict(share_code=code, deleted_on=0)
    if b64_uid:
        import base64
        uid = int(base64.b64decode(b64_uid))
        query = dict(tool_id=ToolsId.AI_BOX, user_id=uid, name='default_box')
    Tools.log.debug(f"lang:{lang}")
    mapping = await TTokenMapping.filter(**query).select_related("external_token").select_related(
        "model").first().values('id', "external_token__value", "model__name", "model__show_name", 'model__remark',
                                "model__input_token","model__output_token","model__max_token",
                                'model__file_support_type', 'model__en_remark', 'status', "user_id", 'limit_cost'
                                , 'current_cost', 'gpts_code', 'enable_plugins', "tool_id", "tool__name","tool__en_name",'tool__jp_name')

    if not mapping:
        return -99
    _ = await TTokenInfo.filter(token_id=mapping.get("id")).first().values("external_code", 'limit_daily_cost',"extra",
                                                                           'current_date_cost', "settings", "use_gpts",
                                                                           'open_tts', 'limit_monthly_cost', 'current_month_cost')
    if _ and _.get("external_code") and pwd != _.get("external_code"):
        return -99
    if not _:
        _ = {}
    user = await TUser.filter(uid=mapping.get("user_id")).first().values("name")
    uinfo = await UserInfo.filter(uid=mapping.get("user_id")).first()
    if uinfo and uinfo.balance==0:
        await TUser.filter(uid=mapping.get("user_id")).update(is_indebted=True)

    #     return "用户欠费" if is_zh else "User indebted"
    if mapping.get("status") == -1:
        return -101
    if mapping.get("status") == -2:
        return -100

    app_box_detail = {}
    box_extra = {}
    tool_id = mapping['tool_id']
    ai_box_name = ''
    if tool_id == ToolsId.AI_BOX.value:
        # box_list = await TAiAppBox.filter(token_id=mapping['id']).select_related("tool").select_related("model").values(
        #     "model__name", "tool__domain", "tool__prefix", "tool_id"
        # )
        box_list = await TGptTool.all().annotate(tool_id=F("id"), tool__domain=F("domain"),
                                                 tool__prefix=F("prefix")).values(
            "tool__prefix", "tool_id", 'tool__domain'
        )
        app_box_detail = {i.get("tool_id"): {
            "url": f"https://{code}{('-' + i.get('tool__prefix') + '.') if i.get('tool__prefix') else '.'}{i.get('tool__domain')}?confirm=true&pwd={pwd}"
        } if i.get(('tool_id')) != 0 else {"api_key": mapping["external_token__value"], 'url': ''} for i in box_list
                          }

        app_box_detail[ToolsId.Chatbot.value]['url'] = app_box_detail[ToolsId.Chatbot.value]['url'].split('?')[
                                                           0] + f'/#/chat?confirm=true&pwd={pwd}'
        app_box_detail[ToolsId.ZERO_SHOT.value]['url'] = app_box_detail[ToolsId.ZERO_SHOT.value]['url'].split('?')[
                                                             0] + f'/#/?confirm=true&pwd={pwd}'
        app_box_detail[ToolsId.OTHER.value]['url'] = app_box_detail[ToolsId.OTHER.value]['url'].split('?')[
                                                             0] + f'/search_master/index.html?pwd={pwd}'
        t = await TGptTool.filter(domain=domain, prefix=pre, id__not=ToolsId.GPTs.value).order_by("-id").first()

        if t:
            if _.get("settings"):
                Tools.log.debug(f"setting_{t.id}:{_['settings'].get(f'settings_{t.id}',{})}")
                _["settings"].update(_['settings'].get(f"settings_{t.id}",{}))
            ai_box = await TAiAppBox.filter(token_id=mapping['id'], tool_id=t.id).select_related(
                "model").first().values("model__name", 'extra', "tool_id", "model__token_supplier_id_list",
                                        "model__show_name",'tool__en_name','tool__jp_name'
                                        , "model__file_support_type", "tool__name")
            if ai_box:
                mapping['model__file_support_type'] = ai_box.get("model__file_support_type")
                mapping['tool__name'] = ai_box.get("tool__name")
                if ai_box.get("model__token_supplier_id_list"):
                    tmp = await TToken.filter(
                        token_supplier_id__in=json.loads(ai_box.get("model__token_supplier_id_list"))).first()
                    if tmp:
                        await TTokenMapping.filter(id=mapping['id']).update(internal_token_id=tmp.id)
                name = ai_box.get("model__name")
                box_extra = ai_box.get("extra")
                mapping['tool_id'] = ai_box.get("tool_id")
                if ai_box.get("tool_id") >= ToolsId.OTHER.value:
                    if lang == 'cn':
                        ai_box_name = ai_box.get("tool__name")
                    elif lang == 'jp':
                        ai_box_name = ai_box.get("tool__jp_name")
                    else:
                        ai_box_name = ai_box.get("tool__en_name")
                else:
                    ai_box_name = ai_box.get("model__show_name")
            else:
                name = ''
        else:
            name = ''
        mapping['model__name'] = name

    model_name = mapping['model__show_name'] if mapping['model__show_name'] else ''
    if lang =='cn':
        model_name += f'({mapping.get("model__remark")})' if mapping.get("model__remark") else ''
    else:
        model_name += f'({mapping.get("model__en_remark")})' if mapping.get("model__en_remark") else ''
    # if mapping.get("status") == -3:
    #     return "机器人已超出当日限额" if is_zh else "The robot has exceeded the limit of the day"
    # if mapping.get("limit_cost") <= mapping.get("current_cost"):
    #     return "机器人已超出总限额" if is_zh else "The robot has exceeded the limit of the total"
    is_gpts = False
    gpts_msg = {}
    if mapping['gpts_code'] and mapping['gpts_code'].startswith('g-'):
        is_gpts = mapping['gpts_code'] > ''
        if _.get("extra"):
            gpts_msg = _.get("extra")
            mapping["model__name"] = f"gpt-4-gizmo-{gpts_msg.get('code','')}"
        else:
            gpts = await TOpenaiGpts.filter(gizmo_id=mapping['gpts_code']).select_related("openai_author").first()
            model_name = gpts.display_name
            mapping["model__name"] = f"gpt-4-gizmo-{gpts.gizmo_id}"
            gpts_msg = {
                "code": gpts.gizmo_id,
                "name": gpts.display_name,
                "description": gpts.display_description,
                "author": gpts.openai_author.display_name,
                "prompt_starters": json.loads(gpts.prompt_starters) if gpts.prompt_starters else '',
                "logo_url": gpts.blob_img_url if gpts.blob_img_url else gpts.profile_picture_url
            }

        mapping["model__file_support_type"] = 1

    if mapping['gpts_code'] and not mapping['gpts_code'].startswith('g-'):
        is_gpts = mapping['gpts_code'] > ''
        mapping['model__name'] = mapping['gpts_code']
        model = await TModel.filter(name=mapping['gpts_code']).first().values("file_support_type")
        setting = _.get("settings", {})
        model_name = setting.get("chatbotName")
        gpts_msg = {
            "code": mapping['gpts_code'],
            "name": setting.get("chatbotName"),
            "description": setting.get("chatDes"),
            "author": setting.get("chatbotName"),
            "prompt_starters": setting.get("prompt"),
            "logo_url": setting.get("chatbotLogo")
        }
        mapping['model__file_support_type'] = model['file_support_type']

    if not mapping['model__name'] or not model_name:
        model_name = mapping['tool__name']
    if tool_id == ToolsId.AI_BOX.value:
        model_name = ai_box_name
        if not model_name:
            __tmp = await TGptTool.filter(prefix=pre,domain=domain).first().values("name","en_name")
            if __tmp and lang =='cn':
                model_name = __tmp.get("name")
            elif __tmp and not lang =='cn':
                model_name = __tmp.get("en_name")

    user_info = await UserInfo.filter(uid=mapping.get("user_id")).first().values("region")

    support_model_name_list = await TModel.filter(is_support_plugins=1).values("name")
    support_model_name_list = [i.get("name") for i in support_model_name_list]
    # 中国人不给用
    region = user_info.get("region", 0)
    if region == Region.cn.value:
        mapping['enable_plugins'] = 0
    else:
        if is_gpts:
            if mapping["gpts_code"] in support_model_name_list or mapping['gpts_code'].startswith("g-"):
                mapping['enable_plugins'] = 1
            else:
                mapping['enable_plugins'] = 0



    if mapping.get('enable_plugins'):
        if lang =='cn':
            model_name += '(可联网)'
        else:
            model_name += '(Online)'


    if region == Region.cn.value:
        url = "https://302ai.cn"
    else:
        url = "https://302.ai"
    user['name'] = html.escape(user['name'])
    is_show_balance = _.get('settings', {}).get("show_balance", False)
    hide_brand = _.get("settings", {}).get("hideBrand", False)

    Tools.log.debug(f"is_show_balance:{is_show_balance},settings:{_.get('settings')}")
    if lang == 'cn':
        tool_name = mapping['tool__name']
        info = f"""
                <ol class="app-desc-content">
                {'' if hide_brand else f'<li>此{tool_name}由302.AI用户 <strong>{user.get("name")}</strong> 创建，302.AI是一个生成和分享AI的平台，可以一键生成和分享属于自己的AI工具</li>'}

                <li>此{tool_name}默认的模型为 <strong>{model_name}</strong></li>""" + (f"""
                <li>""" + (f"""
                <div>此{tool_name}的单日限额为 <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTC，已使用 <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                <div>此{tool_name}的单月限额为 <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTC，已使用 <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                <div>此{tool_name}的总限额为 <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTC，已使用 <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC</div>""" if
                                        mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>此{tool_name}的聊天记录均保存在本机，不会被上传，生成此{tool_name}的用户无法看到你的聊天记录</li>
                {'' if hide_brand else f'<li>更多信息请访问：<a target="__blank" href="{url}">302.AI</a></li>'}
                {f'<li>用户账户余额为: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>
                """
    elif lang == 'jp':
        tool_name = mapping['tool__jp_name']
        info = f"""
                <ol class="app-desc-content">
                {'' if hide_brand else f'<li>この{tool_name}は302.AIユーザーの <strong>{user.get("name")}</strong> によって作成されました。302.AIはAIを生成し共有するプラットフォームで、ワンクリックで自分だけのAIツールを生成し共有することができます</li>'}

                <li>この{tool_name}デフォルトのモデルは <strong>{model_name}</strong>です</li>""" + (f"""
                <li>""" + (f"""
                <div>この{tool_name}の1日の制限は <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTCで、すでに <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC使用されています</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                <div>この{tool_name}の1月の制限は <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTCで、すでに <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC使用されています</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                <div>この{tool_name}の総制限は <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTCで、すでに <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC使用されています</div>""" if
                                        mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>この{tool_name}のチャット履歴はすべてローカルに保存され、アップロードされることはありません。この{tool_name}を生成したユーザーはあなたのチャット履歴を見ることはできません</li>
                {'' if hide_brand else f'<li>詳細については以下をご覧ください：<a target="__blank" href="{url}">302.AI</a></li>'}

                {f'<li>ユーザーの残高は: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>

                """
    else:
        tool_name = mapping['tool__en_name']

        info = f"""
                <ol class="app-desc-content">
                {'' if hide_brand else f'<li>This {tool_name} was created by 302.AI user <strong>{user.get("name")}</strong>, which is a platform for creating and sharing AI. 302.AI allows for the one-click creation and sharing of your own AI tool.</li>'}

                <li>This {tool_name}'s default model is <strong>{model_name}</strong>.</li>""" + (f"""
                <li>""" + (f"""
                <div>Daily Quota is <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTC, and <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC has been used.</div>""" if _.get('limit_daily_cost', 0) else '') + (f"""
                <div>Monthly Quota is <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTC, and <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC has been used.</div>""" if _.get('limit_monthly_cost', 0) else '') + (f"""
                <div>Total Quota is <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTC, and <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC has been used.</div>""" if
                                        mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>All chat logs of this {tool_name} will be saved on your local computer and will not be uploaded to cloud servers. The user who created this {tool_name} cannot see your chat records.</li>                      {'' if hide_brand else f'<li>For more information, please view: <a target="__blank" href="{url}">302.AI</a></li>'}

                {f'<li>The user balance is: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>
                """

    zero_shot = []
    user_info = await UserInfo.filter(uid=mapping.get("user_id")).first().values("region")



    if mapping.get("tool_id") == ToolsId.ZERO_SHOT.value:


        if lang =='cn':
            info = f"""
                    <ol class="app-desc-content">
                    {'' if hide_brand else f'<li>本工具由302.AI用户 <strong>{user.get("name")}</strong> 创建，302.AI是一个AI生成和分享的平台，可以一键生成和分享属于自己的AI工具</li>'}

                    """ + (f"""
                    <li>""" + (f"""
                    <div>本工具的单日限额为 <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong>PTC，已使用 <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong>PTC</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                    <div>本工具的单月限额为 <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong>PTC，已使用 <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong>PTC</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                    <div>本工具的总限额为 <strong>{to_ptc(mapping['limit_cost'], 3)}</strong>PTC，已使用 <strong>{to_ptc(mapping['current_cost'], 3)}</strong>PTC</div>""" if
                                                mapping['limit_cost'] else '') + """
                    </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                    <li>本工具的聊天记录均保存在本机，不会被上传，模型竞技场每次只回答一个问题，并会清空之前的对话内容，确保对新问题作出的回答不受之前对话内容的影响。</li>
                    {'' if hide_brand else f'<li>了解更多信息，请访问：<a target="__blank" href="{url}">302.AI</a></li>'}

                    {f'<li>用户余额为: {to_ptc(uinfo.balance)}</li>' if is_show_balance else ''} 
                    </ol>
                """
        elif lang == 'jp':
            info = f"""
                    <ol class="app-desc-content">
                    {'' if hide_brand else f'<li>このツールは302.AIユーザーの<strong>{user.get("name")}</strong>によって作成されました。302.AIはAIを生成し共有するプラットフォームで、ワンクリックで自分だけのAIツールを生成し共有することができます</li>'}

                    """ + (f"""
                    <li>""" + (f"""
                    <div>このツールの1日の制限は<strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong>PTCで、すでに<strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong>PTC使用されています</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                    <div>このツールの1月の制限は<strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong>PTCで、すでに<strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong>PTC使用されています</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                    <div>このツールの総制限は<strong>{to_ptc(mapping['limit_cost'], 3)}</strong>PTCで、すでに<strong>{to_ptc(mapping['current_cost'], 3)}</strong>PTC使用されています</div>""" if
                                                mapping['limit_cost'] else '') + """
                    </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                    <li>このツールのチャット履歴はすべてローカルに保存され、アップロードされることはありません。モデル競技場は毎回1つの質問にのみ回答し、以前の対話内容をクリアします。これにより、新しい質問に対する回答が以前の対話内容の影響を受けないことが保証されます。</li>
                    {'' if hide_brand else f'<li>詳細については以下をご覧ください：<a target="__blank" href="{url}">302.AI</a></li>'}

                    {f'<li>ユーザーの残高は: {to_ptc(uinfo.balance)}</li>' if is_show_balance else ''} 
                    </ol>

            """
        else:
            info = f"""
                    <ol class="app-desc-content">
                    {'' if hide_brand else f'<li>This tool is created by 302.AI user <strong>{user.get("name")}</strong>, which is a platform for generating and sharing AI. 302.AI allows for the one-click generation and sharing of your own AI tool.</li>'}

                    """ + (f"""
                    <li>""" + (f"""
                    <div>The daily quota is <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong>PTC, and <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong>PTC has been used.</div>""" if _.get('limit_daily_cost', 0) else '') + (f"""
                    <div>The daily quota is <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong>PTC, and <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong>PTC has been used.</div>""" if _.get('limit_monthly_cost', 0) else '') + (f"""
                    <div>The total quota is <strong>{to_ptc(mapping['limit_cost'], 3)}</strong>PTC, and <strong>{to_ptc(mapping['current_cost'], 3)}</strong>PTC has been used.</div>""" if
                                                mapping['limit_cost'] else '') + """
                    </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                    <li>All chat logs of this {tool_name} will be saved on your local computer and will not be uploaded to cloud servers. The user who created this {tool_name} cannot see your chat records.</li>                      
                    {f'<li>The user balance is: {to_ptc(uinfo.balance)}</li>' if is_show_balance else ''} 
                    </ol>
                """

        zero_shot = await TModel.annotate(in_region=JsonContains("region_support_list", str(user_info.get("region",0))))\
            .filter(in_region=True).filter(show_in_robot=True).order_by("ord").annotate(model=F('name')).values("id",
                                                                                                             "model",
                                                                                                             'remark',
                                                                                                             "file_support_type",
                                                                                                             'en_remark',
                                                                                                             "show_name",
                                                                                                             'is_default',
                                                                                                             'en_model_type',
                                                                                                             'model_type')
        for i in zero_shot:
            res = await get_model_price_fnc(model_name=i['model'])
            i['model_price'] = res.data
            model_logo = ai_names.get(i['model'], {}).get("img_url", "")
            i['model_logo'] = model_logo
    models_file_support_type = {}
    file_support_list = await TModel.filter(file_support_type__gt=0).values("name","file_support_type")
    if file_support_list:
        models_file_support_type = {i['name']: i['file_support_type'] for i in file_support_list}
    data = {"value": mapping["external_token__value"],
            "model": mapping["model__name"] if mapping["model__name"] else "",
            "is_gpts": is_gpts,
            'gpts_msg': gpts_msg,
            'input_token': mapping.get("model__input_token", 0),
            'output_token': mapping.get("model__output_token", 0),
            'max_token': mapping.get("model__output_token", 0),
            "info": info,
            "region":user_info.get("region",0),
            "models_file_support_type":models_file_support_type,
            "file_support_type": mapping['model__file_support_type'] or 0,
            "support_plugin_model_list": support_model_name_list,
            "settings": _.get('settings', {}),
            "use_gpts": _.get("use_gpts", 0),
            "enable_plugins": mapping.get("enable_plugins", 0),
            "open_tts": _.get("open_tts", 0),
            "zero_shot": zero_shot,  # 模型对比列表
            "app_box_detail": app_box_detail
            }
    if box_extra:
        data.update(box_extra)
    return data


async def get_gpt_logs(uid, token_name, st, ed, page, page_size, filter_type, text: str, token_id: str,kb_id=0):
    """
    获取gpt日志
    """
    mapping = {
        -99: 'tools',
        -3:'kb',
        -2: 'kb_robot',
        -1: 'mj',
        0: "api",
        1: "chatbot",
        2: "gpts",
        ToolsId.AI_BOX.value: 'box'
    }
    # 判断kb_id是字符串的数字
    kb_id = str(kb_id)
    token_id = str(token_id)
    kb_id = int(kb_id) if kb_id.isdigit()else 0
    token_id = int(token_id) if token_id.isdigit() else 0
    if kb_id:
        _ = await TKnowledgeBase.filter(id=kb_id).first().values("token_id")
        token_id = _.get("token_id")
    query = TLog.filter(token_mapping__user_id=uid).select_related("token_mapping")
    if token_id:
        query = query.filter(token_mapping_id=token_id)
    if text and filter_type == 'name':
        query = query.filter(
            Q(token_mapping__share_code__contains=text.strip()) | Q(token_mapping__name__contains=text.strip()))
    if text and filter_type == "model":
        query = query.filter(model__contains=text.strip())
    if text and filter_type == "type":
        if text == "kb_robot":
            query = query.filter(token_mapping__tool_id=ToolsId.KB)
        if text == "kb":
            query = query.filter(token_mapping__tool_id=ToolsId.KB_EMB)
        if text == "chatbot":
            query = query.filter(token_mapping__tool_id=ToolsId.Chatbot)
        if text == "gpts":
            query = query.filter(token_mapping__tool_id=ToolsId.GPTs)
        if text == "api":
            query = query.filter(token_mapping__tool_id=ToolsId.API)
        if text == "mj":
            query = query.filter(token_mapping__tool_id=ToolsId.MJ)
        if text == "tools":
            query = query.filter(token_mapping__tool_id__gte=ToolsId.OTHER,token_mapping__tool_id__not=ToolsId.AI_BOX)
        if text == "toolbox":
            query = query.filter(token_mapping__tool_id=ToolsId.AI_BOX)

    if st and ed:
        query = query.filter(created_on__range=(st, ed))
    if token_name:
        query = query.filter(token_mapping__name__contains=token_name)
    count = await query.count()
    total_cost = await query.annotate(cost=RawSQL("sum(total_cost)")).first().values("cost")
    logs = await query.order_by("-id").limit(page_size).offset(
        (page - 1) * page_size).annotate(cost=F("total_cost")) \
        .values("id", "token_mapping__tool_id", "token_mapping__share_code", "token_mapping__name", "model",
                "created_on", 'token_mapping__is_robot', "token_mapping__gpts_code",
                "prompt_token", "completion_token", "cost")
    for i in logs:
        i['cost'] = round(i.get("cost", 0), 6)
        i['name'] = i.get("token_mapping__share_code") if i.get("token_mapping__is_robot") else i.get(
            "token_mapping__name")
        i['type'] = mapping.get(i.get("token_mapping__tool_id"), 'tools')

    return {"total": count, "data": logs,
            "total_cost": round(total_cost.get("cost", 0) if total_cost.get("cost", 0) else 0, 6),
            "page": page, "page_size": page_size}

@tortoise.transactions.atomic("gpt_conn")
async def get_or_create_default_api_key_fuc(uid_b64,**kwargs):
    if not uid_b64:
        raise AuthException()
    import base64
    uid = int(base64.b64decode(uid_b64))
    gpt_tool = await TGptTool.filter(id=ToolsId.AI_BOX).first()
    if not await TUser.filter(uid=uid).exists():
        raise AuthException()
    token = await TTokenMapping.filter(tool_id=ToolsId.AI_BOX,user_id=uid,name='default_box').first().select_related("external_token").\
        values("external_token__value","share_code")
    share_code = token.get("share_code") if token else ''
    if not token:
        token = create_token()
        token = await TToken.create(value=token, token_supplier_id=0)
        # 空对象创建竞技模型的情况下，获取已经创建的工具返回就好了
        data = GptToken(name="default_box", tool_id=ToolsId.AI_BOX,is_robot=1)
        v = await TToken.filter(
            token_supplier_id__in=Subquery(TTokenSupplier.filter(deleted_on=0).values("id"))).limit(1).values("id")
        share_code = random_string(8).lower()
        token = await TTokenMapping.create(user_id=uid, external_token_id=token.id, internal_token_id=v[0].get("id"),
                                             name=data.name, share_code=share_code.lower(), is_robot=data.is_robot,
                                             enable_plugins=data.enable_plugins,
                                             remark=data.remark, gpts_code=data.gpts_code,
                                             tool_id=data.tool_id,
                                             model_id=data.model_id if data.model_id else 0,
                                             status=1,
                                             expired_on=data.expired_on if data.expired_on else 0,
                                             limit_cost=data.limit_cost * 1000)
        conn = tortoise.connections.get("gpt_conn")
        await conn.execute_query("update t_token_mapping set id=-id  where id=%s ",(token.id,))
        token.id = -1 * token.id
        result = await get_tool_models_list(uid)
        tools = []
        for i in result:
            tmp = {"extra":{"enable":True},"tool_id":i.get("id")}
            if i.get("model_list"):
                tmp['model_id'] = i.get("model_list")[0].get("id")
            tools.append(tmp)
        show_balance = True
        tool_nav_count = 3
        hide_home_page = False
        banners = []
        tool_id_conf = [
            {
                "path": "/paint",
                "tools": [
                    {
                        "extra": {},
                        "tool_id": -1,
                        "model_id": 98                    }
                ],
                "enable": True
            },
            {
                "path": "/chat",
                "tools": [
                    {
                        "extra": {
                            "open_tts": 1,
                            "use_gpts": 1,
                            "enable_plugins": 1
                        },
                        "tool_id": 1,
                        "model_id": 114
                    }
                ],
                "enable": True
            },
            {
                "path": "/api",
                "tools": [
                    {
                        "extra": {},
                        "tool_id": 0,
                        "model_id": 0
                    }
                ],
                "enable": True
            },
            {
                "path": "/tools",
                "tools": tools,
                "enable": True
            }
        ]

        objs = []
        for k in tool_id_conf:
            for i in k.get("tools"):
                objs.append(TAiAppBox(
                    **{"token_id": token.id, "tool_id": i.get("tool_id"), "model_id": i.get("model_id"), "extra": i.get("extra")}))
        if objs:
            await TAiAppBox.bulk_create(objs)

        settings = {
            "show_balance": show_balance,
            "tool_nav_count": tool_nav_count,
            "hide_home_page": hide_home_page,
            "tool_conf": tool_id_conf,
            "banners": banners
        }
        await TTokenInfo.create(token_id=token.id, tz=data.tz, limit_daily_cost=data.limit_daily_cost * 1000, limit_monthly_cost=data.limit_monthly_cost * 1000, use_gpts=1,
                                external_code=data.external_code,settings=settings)

    return await get_value_from_code(share_code,pwd="",lang=kwargs.get("lang"),pre=gpt_tool.prefix,domain=gpt_tool.domain)


    # return {"token":token.get("external_token__value")}

async def set_values(token_id, values: dict,**kwargs):
    key = f"lock_{token_id}"
    await Tools.redis.incr(key)
    await Tools.redis.expire(key, 2)

    def add_value(key):
        if values.get(key) != settings.get(key):
            Tools.log.debug(f"add key: {key},{values.get(key)}")
            values[f'user_{key}'] = values.get(key)

    def del_value(key):
        if key in settings:
            del settings[key]

    token_info = await TTokenInfo.filter(token_id=token_id).first().values("settings",'conf_version')
    if not token_info:
        return
    update_dict = {}
    if kwargs.get("check_version"):
        if not token_info.get("conf_version") == kwargs.get("current_version"):return
        v = Version(token_info.get("conf_version",'1.0.0'))
        v.increment_patch()
        update_dict['conf_version'] = v.get_version()
    settings = token_info.get("settings", {})

    add_value("chatDes")
    add_value("chatbotDesc")
    add_value("chatbotLogo")
    add_value("chatbotName")
    if values.get("tool_id_conf"):
        values['tool_conf'] = values.pop("tool_id_conf")
    settings.update(values)
    update_dict["settings"] = settings
    Tools.log.debug(f"add key end: {settings}")
    if 'save_logs' in values:
        if values['save_logs'] == 1:
            settings['log_enable_st'] = current_timestamp()

    await TTokenInfo.filter(token_id=token_id).update(**update_dict)
    # if 'save_logs' in values:
    #         # token = await TTokenMapping.filter(id=token_id).first()
    #     update_dict = dict(save_log=values['save_logs'])
    #     if values['save_logs'] == 1:
    #         update_dict['log_enable_st']=current_timestamp()
    #     await TTokenMapping.filter(id=token_id).update(**update_dict)
    await Tools.redis.delete(key)
    return True


class Version:
    def __init__(self, version_str):
        self.major, self.minor, self.patch = map(int, version_str.split('.'))

    def increment_major(self):
        self.major += 1
        self.minor = 0
        self.patch = 0

    def increment_minor(self):
        self.minor += 1
        self.patch = 0

    def increment_patch(self):
        self.patch += 1
        if self.patch >= 99:
            self.minor += 1
            self.patch = 0
        if self.minor >= 99:
            self.major += 1
            self.minor = 0

    def __str__(self):
        return f"{self.major}.{self.minor}.{self.patch}"

    def get_version(self):
        return f"{self.major}.{self.minor}.{self.patch}"


async def requst_log(token_id,**kwargs):
    key = f"max_log_id_{token_id}"
    db = Tools.mo['gpt302']
    c = db['log']
    page = kwargs.get("page",1)
    page_size = kwargs.get("page_size",10)
    skip_count = (page - 1) * page_size
    # 查询文档
    token = await TTokenMapping.filter(id=token_id).first().values("log_enable_st","save_log")
    info = await TTokenInfo.filter(token_id=token_id).first().values("settings")
    settings = info.get("settings")
    token['save_log'] = settings.get("save_logs")
    token['log_enable_st'] = settings.get("log_enable_st") or token['log_enable_st']
    if not token.get("save_log"):
        return {}
    log_enable_st = token.get("log_enable_st")
    query = {"token_mapping_id": token_id, "timestamp": {"$gte": log_enable_st}}
    if page >1:
        b_max_log_id = await Tools.redis.get(key)
        max_log_id = int(b_max_log_id.decode())
        query['log_id'] = {"$lte":max_log_id}
        Tools.log.debug(f"query:{query},skip_count:{skip_count},page_size:{page_size}")
    cursor = c.find(query).sort('log_id', -1).skip(skip_count).limit(page_size)
    total = await c.count_documents(query)

    # 获取结果
    results = []
    async for document in cursor:
        try:
            dict_document = dict(document)
            dict_document.pop("_id")
            results.append(dict_document)
        except:
            ...
    if page == 1 and results:
        max_log_id = results[0].get("log_id")
        await Tools.redis.setex(key,360,max_log_id)
    return {"records":results,"total":total,"page":page,"page_size":page_size}

async def get_or_set_tool_conf(share_code='',settings=None,**kwargs):
    """获取或者设置工具的settings属性"""
    if settings is None:
        settings = dict()
    token = await TTokenMapping.filter(share_code=share_code).first().values("id")
    if kwargs.get("delete"):
        await TTokenInfo.filter(token_id=token.get("id")).update(settings={})
        return suc_data()

    if not settings:
        token_info = await TTokenInfo.filter(token_id=token.get("id")).first().values('settings',"conf_version")
        return suc_data({"config":token_info.get("settings", {}),"version":token_info.get("conf_version",'1.0.0')})

    is_ok = await set_values(token.get("id"), settings,**kwargs)
    if not is_ok:
        return JSONResponse({"code":412,"msg": "version not match"},status_code=412)
    return suc_data()

async def crontab_to_update_limit_daily_cost():
    """
    每天凌晨更新limit_daily_cost字段
    """
    conn = tortoise.connections.get("gpt_conn")
    # 限额和正常的代理
    records = await conn.execute_query_dict("""
    select tz,token_id,status,limit_daily_cost,current_date_cost from t_token_info left join t_token_mapping on t_token_mapping.id=token_id where limit_daily_cost>0 and status in(1,-3)
    """)
    for record in records:
        tz = record.get("tz")
        if tz.endswith("Unknown"):
            tz = "Asia/Shanghai"
        loc_time = datetime.now(pytz.timezone(tz))
        # 如果时区的时间为0点，重置当日限额流量
        if loc_time.hour == 0:
            await TTokenInfo.filter(token_id=record.get("token_id")).update(current_date_cost=0)
            if record.get("status") == GPTStatus.LIMIT_CURRENT_DATE.value:
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)
        else:
            # 否则判断是否超过当日限额流量，然后标记
            if record.get("current_date_cost") >= record.get("limit_daily_cost"):
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.LIMIT_CURRENT_DATE.value)
            else:
                if record.get("status") == GPTStatus.LIMIT_CURRENT_DATE.value:
                    await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)


async def crontab_to_update_limit_monthly_cost():
    """
    每月第一天更新 limit_monthly_cost 字段
    """
    conn = tortoise.connections.get("gpt_conn")
    # 限额和正常的代理
    records = await conn.execute_query_dict("""
    select tz,token_id,status,limit_monthly_cost,current_month_cost 
    from t_token_info 
    left join t_token_mapping on t_token_mapping.id = token_id 
    where limit_monthly_cost > 0 and status in (1, -6)
    """)
    for record in records:
        loc_time = datetime.now(pytz.timezone(record.get("tz")))
        # 如果时区的时间为月初1号0点，重置当月限额流量
        if loc_time.day == 1 and loc_time.hour == 0:
            await TTokenInfo.filter(token_id=record.get("token_id")).update(current_month_cost=0)
            if record.get("status") == GPTStatus.LIMIT_CURRENT_MONTH.value:
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)
        else:
            # 否则判断是否超过当月限额流量，然后标记
            if record.get("current_month_cost") >= record.get("limit_monthly_cost"):
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.LIMIT_CURRENT_MONTH.value)
            else:
                if record.get("status") == GPTStatus.LIMIT_CURRENT_MONTH.value:
                    await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)


async def change_user_indebted():
    sql =   """
    update proxy.t_users  set is_indebted=0 where is_indebted=1 and uid in (select uid from proxy.t_users_info where balance >1000)
    """
    conn = tortoise.connections.get("default")
    await conn.execute_query(sql)

async def crontab_to_update_limit_hour_cost():
    """
    每天凌晨更新limit_daily_cost字段
    """
    conn = tortoise.connections.get("gpt_conn")
    # 限额和正常的代理
    records = await conn.execute_query_dict("""
    select tz,token_id,status,current_hour_cost,limit_hour_cost from t_token_info left join t_token_mapping 
    on t_token_mapping.id=token_id where limit_hour_cost>0 and status in(1,-5)
    """)
    loc_time = datetime.now()
    for record in records:
        # 如果时区的时间为0点，重置当日限额流量
        if loc_time.minute == 0:
            await TTokenInfo.filter(token_id=record.get("token_id")).update(current_hour_cost=0)
            if record.get("status") == GPTStatus.LIMIT_CURRENT_DATE.value:
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)
        # else:
        #     # 否则判断是否超过当日限额流量，然后标记
        if record.get("current_hour_cost") >= record.get("limit_hour_cost"):
            await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.LIMIT_CURRENT_HOUR.value)
        else:
            if record.get("status") == GPTStatus.LIMIT_CURRENT_HOUR.value:
                await TTokenMapping.filter(id=record.get("token_id")).update(status=GPTStatus.NORMAL.value)
async def crontab_to_check_limit_hour_cost():
    conn = tortoise.connections.get("gpt_conn")
    records = await conn.execute_query_dict("""
       select token_id from t_token_info left join t_token_mapping
        on t_token_mapping.id=token_id where limit_hour_cost>0 and status in (1,-5)
       """)
    ids = [i.get("token_id") for i in records]
    if ids:
        timestamp_st = int(datetime.now().replace(minute=0, second=0, microsecond=0).timestamp())
        # orders = await TIpOrders.filter(payway='gpt_cost', created_on__gt=timestamp_st,
        #                                 token_id__in=ids).annotate(cost=Sum("value")).group_by(
        #     "token_id").values("token_id", "cost")
        orders = await TLog.filter(created_on__gt=timestamp_st,
                                        token_mapping_id__in=ids).annotate(cost=Sum("total_cost"),token_id=F("token_mapping_id")).group_by(
            "token_id").values("token_id", "cost")
        for order in orders:
            await TTokenInfo.filter(token_id=order.get("token_id")).update(current_hour_cost=int(order.get("cost")*1000),
                                                                           modified_on=current_timestamp())
async def crontab_to_check_limit_daily_cost():
    conn = tortoise.connections.get("gpt_conn")
    records = await conn.execute_query_dict("""
    select tz,token_id from t_token_info left join t_token_mapping
     on t_token_mapping.id=token_id where limit_daily_cost>0 and status >=0 and tz >''
    """)
    tzs = [i.get("tz") for i in records]
    tz_tokens = {tz: [] for tz in set(tzs)}
    for i in records:
        tz_tokens[i.get("tz")].append(i.get("token_id"))

    for tz,token_ids in tz_tokens.items():
        if tz.endswith("Unknown"):
            tz = "Asia/Shanghai"
        timestamp_st = datetime.now(pytz.timezone(tz)).replace(hour=0, minute=0, second=0).timestamp()
        # orders = await TIpOrders.filter(payway='gpt_cost', created_on__gt=timestamp_st,
        #                                 token_id__in=token_ids).annotate(cost=Sum("value")).group_by(
        #     "token_id").values("token_id", "cost")
        orders = await TLog.filter(created_on__gt=timestamp_st,
                                        token_mapping_id__in=token_ids).annotate(cost=Sum("total_cost"),token_id=F("token_mapping_id")).group_by(
            "token_id").values("token_id", "cost")
        for order in orders:
            await TTokenInfo.filter(token_id=order.get("token_id")).update(current_date_cost=int(order.get("cost")*1000),
                                                                           modified_on=current_timestamp())


async def crontab_to_check_limit_monthly_cost():
    """
    每月检查并更新当月使用量
    """
    conn = tortoise.connections.get("gpt_conn")
    records = await conn.execute_query_dict("""
    select tz,token_id from t_token_info 
    left join t_token_mapping on t_token_mapping.id = token_id 
    where limit_monthly_cost > 0 and status >= 0 and tz > ''
    """)
    tzs = [i.get("tz") for i in records]
    tz_tokens = {tz: [] for tz in set(tzs)}
    for i in records:
        tz_tokens[i.get("tz")].append(i.get("token_id"))

    for tz, token_ids in tz_tokens.items():
        # 获取月初时间戳
        timestamp_start_of_month = datetime.now(pytz.timezone(tz)).replace(day=1, hour=0, minute=0, second=0).timestamp()
        
        # 查询从月初以来的费用
        orders = await TLog.filter(created_on__gt=timestamp_start_of_month, token_mapping_id__in=token_ids).annotate(
            cost=Sum("total_cost"),
            token_id=F("token_mapping_id")
        ).group_by("token_id").values("token_id", "cost")
        
        for order in orders:
            await TTokenInfo.filter(token_id=order.get("token_id")).update(
                current_month_cost=int(order.get("cost") * 1000),
                modified_on=current_timestamp()
            )


async def check_service_health():
    tools = await TGptTool.filter(deleted_on=0).all()
    msg = ''
    import ssl
    from aiohttp import web
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    for tool in tools:
        if not tool.domain:
            continue
        async with aiohttp.ClientSession() as session:
            url = ".".join((("demo-" + tool.prefix) if tool.prefix else 'demo', tool.domain)).strip('.')
            async with session.get(f"https://{url}", ssl=ssl_context) as resp:
                if resp.status == web.HTTPInternalServerError.status_code:
                    msg += f"工具：{tool.name}，状态异常{resp.status}，请及时处理\nurl: https://{url}\n"

    if msg:
        Tools.feishu_302ai.sendText(msg + "<at user_id=\"all\">所有人</at>")


async def crontab_to_deduction_gpt():
    """
    定时扣除gpt额度
    """

    @tortoise.transactions.atomic("gpt_conn")
    async def for_each_token(token: TTokenMapping):
        """
        处理单个token的扣费
        """
        # 上次统计到这个id，接下去从这个id继续计算
        last_log_id = token.cost_log_id
        the_latest_log = await TLog.filter(id__gt=last_log_id, token_mapping_id=token.id).order_by("-id").first()
        if not the_latest_log:
            return

        # 计算这次扣费的log区间的计费，扣费
        log_cost = await TLog.filter(id__gt=last_log_id, token_mapping_id=token.id, id__lte=the_latest_log.id).annotate(
            cost=Sum(F("total_cost")), count=Count("id")) \
            .values("cost", "count")
        origin_cost = sum([i.get("cost") for i in log_cost]) * 1000 
        if origin_cost > 0 and origin_cost < 1:
            return
        cost = int(origin_cost)
        cache_key = f"cache_number_{token.user_id}"
        cache_number = origin_cost - cost
        await Tools.redis.incrbyfloat(cache_key, cache_number)
       
        # 查用户余额
        user_info = await UserInfo.filter(uid=token.user_id).first()
        if user_info.balance < cost:
            cost = user_info.balance
            await TUser.filter(uid=token.user_id).update(is_indebted=True)
        
        value = await Tools.redis.get(cache_key)
        user_cache_value = value.decode()
        origin_value = float(user_cache_value)
        if origin_value >1:
        
            order_id = await add_ready_ip_order(update_user_info=True, token_id=0,
                                    user_id=token.user_id, type="-", currency=0, currency_type="USD",
                                    receive_currency=0, payway="gpt_cost", pay_order="", is_inner=True,
                                    value=int(origin_value), extra_value=0,
                                    status=2, checksum=True, valid=True)
            
            await Tools.redis.incrbyfloat(cache_key, -int(origin_value))
        user_info.gpt_cost += cost
        user_info.gpt_request_times += sum([i.get("count") for i in log_cost])
        await user_info.save()

        order_id = await add_ready_ip_order(update_user_info=True, token_id=token.id,
                                            user_id=token.user_id, type="-", currency=0, currency_type="USD",
                                            receive_currency=0, payway="gpt_cost", pay_order="", is_inner=True,
                                            value=cost, extra_value=0,
                                            status=1, checksum=True, valid=True)
        Tools.log.info(
            f"gpts扣费统计：{order_id} ,mapping_token_id: {token.id},{last_log_id}<log_id<={the_latest_log.id} rate:{log_cost}")
        
        token.current_cost = token.current_cost + cost
        token.cost_log_id = the_latest_log.id
        token.modified_on = current_timestamp()
        await token.save()

    async def deal_token_list(token_list):
        for token in token_list:
            await for_each_token(token)

    # 过期的apikey
    await TTokenMapping.filter(tool_id=0, expired_on__gt=0, expired_on__lte=current_timestamp()).update(
        status=GPTStatus.EXPIRED.value)
    token_ids = await Tools.redis_14.zpopmin("USAGE_TOKEN_MAPPING_STORTED_IDS", 1000)
    if not token_ids:
        return
    id_list = await TTokenMapping.filter(tool_id=-4).values("id")
    id_list = [i.get("id") for i in id_list]
    token_ids = [int(i[0]) for i in token_ids] + id_list
    tokens = await TTokenMapping.filter(id__in=token_ids)
    num = 20
    pool_list = [[] for i in range(num)]
    for token in tokens:
        index = token.user_id%num
        pool_list[index].append(token)
        # await for_each_token(token)
    jobs = []
    for i in pool_list:
        jobs.append(deal_token_list(i))
    await asyncio.gather(*jobs)

async def get_share_code_summary_func(uid,start_time, end_time, particle_size,**kwargs):
    # 获取用户默认工具箱名称
    deafult_box = await TTokenMapping.filter(user=uid, id__lt=0).first().values()
    if not deafult_box:
        deafult_box = {}
    default_box_name = deafult_box.get('share_code',None)

    to = {
        -99: 'tools',
        -3: 'kb',
        -2: 'kb_robot',
        -1: 'mj_robot',
        0: "api",
        1: "chat_robot",
        2: "gpts_robot",
        ToolsId.AI_BOX.value: 'toolbox'
    }
    tz = kwargs.get('tz',"Asia/Shanghai")
    st = timestamp_to_date(start_time, tz)
    ed = timestamp_to_date(end_time, tz)
    query = TDataStatistics.filter(data_type=0, uid=uid, str_date__gte=st,str_date__lte=ed)
    if particle_size == 'd':
        query = query.annotate(cost=Sum("value_field"),keys=F("str_date"))
    else:
        query = query.annotate(cost=Sum("value_field"), keys=F("str_month"))
    records = await query.group_by("keys","key_name").values("cost","keys","key_name")
    if not records:
        return {"summary": {}, "values": {}, "keys": [], 'type': "", "mapping": {}}
    df = pd.DataFrame(records)
    df_t = df.pivot_table(index=['keys'],columns=['key_name']).fillna(0)
    summary = {}
    total = 0
    for i in df.groupby("key_name").sum()['cost'].reset_index().itertuples():
        cost = round(i.cost,3)
        summary[i.key_name] = cost
        total += cost
    summary['total'] = total
    tokens = await TTokenMapping.filter(share_code__in=list(summary.keys())).values("tool_id","share_code","name")
    mapping = {}
    share_code_2_name = {}
    for i in tokens:
        # 修改默认全能工具箱名称为default
        if i.get("tool_id") == ToolsId.AI_BOX.value and i.get("share_code") == default_box_name:
            mapping["default"] = to.get(i.get("tool_id")) or to.get(-99)
        else: 
            mapping[i.get("share_code")] = to.get(i.get("tool_id")) or to.get(-99)

        if i.get("tool_id") == 0:
            share_code_2_name[i.get("share_code")] = i.get("name")
            mapping[i.get("name")] = mapping.pop(i.get("share_code"))
    df_t.columns = [i[1] for i in df_t.columns]

    df_t['total'] = df_t.iloc[:, :].sum(axis=1)
    df_list_dict = df_t.map(lambda x: round(x, 6)).reset_index().to_dict("list")
    for share_code,name in share_code_2_name.items():
        df_list_dict[name] =df_list_dict.pop(share_code)
        summary[name] =summary.pop(share_code)

    if default_box_name in list(summary.keys()):
        summary = {"default" if k == default_box_name else k: v for k, v in summary.items()}
        df_list_dict = {"default" if k == default_box_name else k: v for k, v in df_list_dict.items()}
    
    return {"summary": summary, "values": df_list_dict, "keys": df_list_dict.pop('keys'), 'type': "","mapping":mapping}


async def get_log_summary(uid, st, ed, particle_size, zone: str = '', token_id=0,kb_id=0,use_in='tool'):

    @cache(ttl=60*10)
    async def get_summary(uid, st, particle_size, zone: str = '', token_id=0,kb_id=0,use_in=''):
        to = {(0, "0"): "api", (1, '0'): "chat_robot", (1, "1"): "gpts_robot"}
        to = {
            -99: 'tools',
            -3:'kb',
            -2: 'kb_robot',
            -1: 'mj_robot',
            0: "api",
            1: "chat_robot",
            2: "gpts_robot",
            ToolsId.AI_BOX.value:'toolbox'
        }
        use_in_dict = {
            "tool": "token_mapping__tool_id",
            "model" : "model"
        }
        group_by_key_name = use_in_dict.get(use_in,use_in_dict.get("tool"))

        time_difference = 0
        kb_id = str(kb_id)
        token_id = str(token_id)
        kb_id = int(kb_id) if kb_id.isdigit() else 0
        token_id = int(token_id) if token_id.isdigit() else 0

        if kb_id and not token_id:
            _ = await TKnowledgeBase.filter(id=kb_id).first().values("token_id")
            token_id = _.get("token_id")
        if zone:
            time_difference = get_diff_time_from_zone(zone)

        base_query = TLog.filter(token_mapping_id__in=Subquery(TTokenMapping.filter(user_id=uid).values("id")),
                                 created_on__gte=st, created_on__lt=ed).select_related(
            "token_mapping")
        if token_id and token_id > 0:
            base_query = base_query.filter(token_mapping_id=token_id)
        summary = await base_query.annotate(times=Count("id")).group_by(group_by_key_name).values(
            group_by_key_name, "times")
        if particle_size == "h":
            base_query = base_query.annotate(
                key=RawSQL(f"DATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {time_difference}),'%m-%d %H:00')"))
        if particle_size == "d":
            base_query = base_query.annotate(
                key=RawSQL(f"DATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {time_difference}),'%Y-%m-%d')"))
        if particle_size == "w":
            base_query = base_query.annotate(key=RawSQL(
                f"CONCAT(DATE_FORMAT(subdate(FROM_UNIXTIME(t_log.created_on + {time_difference}),dATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {time_difference}),'%w')-1),'%m-%d'),' - ',DATE_FORMAT(subdate(FROM_UNIXTIME(t_log.created_on + {time_difference}),dATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {time_difference}),'%w')-7),'%m-%d'))"))
        if particle_size == "m":
            base_query = base_query.annotate(
                key=RawSQL(f"DATE_FORMAT(FROM_UNIXTIME(t_log.created_on + {time_difference}),'%Y-%m')"))
        model_cost = await base_query.annotate(cost=Sum(F("total_cost"))).group_by("key", group_by_key_name).values(
            "key", group_by_key_name, "cost")
        if not model_cost:
            return {"summary": {}, "values": {}, "keys": [], 'type': ''}

        Tools.log.debug(summary)
        Tools.log.debug(model_cost)
        df = pd.DataFrame(model_cost)
        if use_in == 'tool':
            df[group_by_key_name] = df[group_by_key_name].apply(lambda x: -99 if x >= ToolsId.OTHER and x!=ToolsId.AI_BOX  else x).apply(
                lambda x: to.get(x))
        df = df.groupby(['key', group_by_key_name]).sum().reset_index()

        df_pivot = df.pivot(index='key', columns=group_by_key_name, values='cost').fillna(0).sort_values("key")
        df_pivot = df_pivot.loc[:, ~( df_pivot< 0.000001).all(axis=0)]
        df_pivot['total'] = df_pivot.iloc[:, :].sum(axis=1)
        df_list_dict = df_pivot.map(lambda x: round(x, 6)).reset_index().to_dict("list")
        df_summary = pd.DataFrame(summary)
        if use_in == 'tool':
            df_summary[group_by_key_name] = df_summary[group_by_key_name].apply(
                lambda x: -99 if x >= ToolsId.OTHER and x!=ToolsId.AI_BOX else x).apply(lambda x: to.get(x))
        summary = {}
        for i in df_summary.groupby([group_by_key_name]).sum().reset_index().itertuples():
            summary[eval(f'i.{group_by_key_name}')] = i.times
        type = ''
        if token_id:
            type = list(summary.keys())[0] if summary else ''
        summary['total'] = sum(summary.values())
        return {"summary": summary, "values": df_list_dict, "keys": df_list_dict.pop('key'), 'type': type}
    return await get_summary(uid,st,particle_size,zone,token_id,kb_id,use_in=use_in)


def get_content_types(filename):
    # 获取文件的MIME类型和编码（如果有的话）
    content_type, _ = mimetypes.guess_type(filename)

    if filename.endswith("webp"):
        content_type='image/webp'

    if filename.endswith("md"):
        content_type = 'text/markdown'

    if content_type in ('text/plain','text/html'):
        content_type+=';charset=utf-8'

    # 如果无法确定MIME类型，则返回一个默认值，比如 'application/octet-stream'
    if content_type is None:
        content_type = 'application/octet-stream'

    return content_type


connect_str = "DefaultEndpointsProtocol=https;AccountName=proxyblob;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
blob_service_client = BlobServiceClient.from_connection_string(connect_str)

executor = ThreadPoolExecutor(max_workers=30)

async def upload_part(bucket, object_name, upload_id, part_number, part_data):
    """异步上传单个分片"""
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(
        executor,
        bucket.upload_part,
        object_name,
        upload_id,
        part_number,
        part_data
    )
    print(f'已上传分片 {part_number}')
    return oss2.models.PartInfo(part_number, result.etag)

async def oss_upload(bytes_data,bucket,object_name):
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(executor,bucket.put_object,object_name, bytes_data)
    # total_size = len(bytes_data)
    # part_size = oss2.determine_part_size(total_size, preferred_size=1 * 1024 * 1024)  # 可以调整 preferred_size

    # # 初始化分片上传
    # upload_id = bucket.init_multipart_upload(object_name).upload_id
    # parts = []
    # tasks = []

    # part_number = 1
    # offset = 0

    # # 将 bytes_data 按照 part_size 分割并异步上传
    # while offset < total_size:
    #     part_data = bytes_data[offset: offset + part_size]

    #     # 创建异步任务上传分片
    #     task = asyncio.ensure_future(
    #         upload_part(bucket, object_name, upload_id, part_number, part_data)
    #     )
    #     tasks.append(task)

    #     offset += part_size
    #     part_number += 1

    # # 等待所有分片上传完成
    # uploaded_parts = await asyncio.gather(*tasks)
    # parts.extend(uploaded_parts)

    # # 完成分片上传
    # result = bucket.complete_multipart_upload(object_name, upload_id, parts)
    print('分片上传完成。')


async def upload_blob(data: bytes = None, file_name=None, pre='imgs',proxy_blob=False,content_type=None,encoding="utf-8"):
    if not file_name:
        hs5 = hashlib.md5()
        hs5.update(data)
        name = hs5.hexdigest()
        file_name = f"{name}.png"
    container_name = "gpt"
    container_client = blob_service_client.get_container_client(container_name)
    try:
        await container_client.create_container()
    except Exception as e:
        print(e)

    blob_client = blob_service_client.get_blob_client(container=container_name,
                                                      blob=f'{pre}/{file_name}')
    Tools.log.debug(f"file_name:{file_name}")
    content_type = content_type or get_content_types(file_name)
    Tools.log.debug(f"content_type:{content_type}")
    job = await blob_client.upload_blob(data, overwrite=True, content_settings=ContentSettings(content_type=content_type, content_encoding=encoding,content_length=len(data)))

    loop = asyncio.get_running_loop()
    auth = oss2.Auth(Tools.config.oss2.access_key_id, Tools.config.oss2.access_key_secret)
    bucket = oss2.Bucket(auth, Tools.config.oss2.endpoint, Tools.config.oss2.bucket_name,is_cname=False)
    # job2 = oss_upload(bytes_data=data,bucket=bucket,object_name=f"gpt/{pre}/{file_name}")
    job2 = loop.run_in_executor(None,bucket.put_object,f"gpt/{pre}/{file_name}", data, {'Content-Type': content_type})

    # await asyncio.gather(job,job2)
    if proxy_blob:
        blob_url = f"https://proxyblob.blob.core.windows.net/gpt/{pre}/{file_name}"
        blob_url = f"https://file.302.ai/gpt/{pre}/{file_name}"
    else:
        blob_url = f"https://file.302ai.cn/gpt/{pre}/{file_name}"

    return blob_url

def get_news_func(top_n = 5):
    """
    获取文章咨询的top n
    """
    import requests
    from lxml import html
    resp = requests.get("https://news.302.ai/")

    tree = html.fromstring(resp.text)
    paragraph_ul = tree.xpath('//*[@id="wrap"]/div/main/section[2]/div/ul')[0]
    x = paragraph_ul.xpath("//li[@class='item']")
    items = x[:top_n]
    records = []
    for item in items:
        article_href = item.xpath("./div/a[1]/@href[1]")[0]
        img_srf = item.xpath("./div/a[1]/img/@data-original[1]")[0]
        title = item.xpath("./div[2]/h3/a/text()")[0].strip()
        content = item.xpath("./div[2]/div[@class='item-excerpt']/p/text()")[0].strip()
        date_time = item.xpath("./div[2]/div[@class='item-meta']/span/text()")[0]  # 5天前
        read_num = \
            item.xpath("./div[2]/div[@class='item-meta']/div[@class='item-meta-right']/span[@title='Views']/text()")[0]
        collection_num = \
            item.xpath("./div[2]/div[@class='item-meta']/div[@class='item-meta-right']/span[@title='Favorites']/text()")[0]
        praise_num = \
            item.xpath("./div[2]/div[@class='item-meta']/div[@class='item-meta-right']/span[@title='Likes']/text()")[0]
        comment_num = item.xpath("./div[2]/div[@class='item-meta']/div[@class='item-meta-right']/a/text()")[0]
        value = dict(
            article_href=article_href,
            img_srf=img_srf,
            title=title,
            content=content,
            date_time=date_time,
            read_num=read_num,
            collection_num=collection_num,
            praise_num=praise_num,
            comment_num=comment_num
        )
        records.append(value)
    return records
async def update_img():
    value = await Tools.redis.get("last_update_blob_id")
    if not value:
        value = 0
    else:
        value = int(value.decode())
    _ = await TOpenaiGpts.annotate(mxid=Max("id")).first().values("mxid")
    records = await TOpenaiGpts.filter(id__gt=value, profile_picture_url__gt='').limit(5000).order_by("id")

    sem = asyncio.Semaphore(30)  # Limit to 50 concurrent requests

    session = aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=3), connector=aiohttp.TCPConnector(limit=20))

    async def download_and_process_image(r):
        async with sem:  # Acquire semaphore before making request
            url = r.profile_picture_url
            try:
                async with session.get(url) as res:
                    if res.status != 200:
                        return
                    data = await res.read()
                    await asyncio.sleep(random.uniform(0.1, 0.6))  # Sleep between requests
            except Exception as e:
                Tools.log.error(e)
                return

            hs5 = hashlib.md5()
            hs5.update(data)
            name = hs5.hexdigest()

            blob_url = await upload_blob(data, name.lower() + ".png")
            r.blob_img_url = blob_url
            await TOpenaiGpts.filter(id=r.id).update(blob_img_url=blob_url)

    tasks = [download_and_process_image(record) for record in records]
    await asyncio.gather(*tasks)
    await session.close()

    max_id = records[-1].id if records else value
    await Tools.redis.set("last_update_blob_id", max_id)
    # if value != _.get("mxid"):
    #     await update_img()


async def get_gpt_tools_list():
    records = await TGptTool.filter(deleted_on=0, id__gte=ToolsId.OTHER).order_by('id')

    data = [dict(i) for i in records]
    return data


async def get_tool_value_from_code(share_code, pwd, **kwargs):
    mapping = await TTokenMapping.filter(share_code=share_code, deleted_on=0).select_related(
        "external_token").select_related(
        "model").select_related("tool").first().values('id', "external_token__value", "model__name", "model__show_name",
                                                       'model__remark', 'tool__extra',"model__input_token","model__output_token","model__max_token",
                                                       'model__en_remark', 'status', "user_id", 'limit_cost','tool__name','tool__en_name','tool__jp_name',
                                                       'current_cost', 'gpts_code', 'tool_id',
                                                       'enable_plugins')
    if not mapping:
        err_code = -99
        msg = "无效的分享码,not mapping"
        return fail_data(code=err_code, msg=msg)
    _ = await TTokenInfo.filter(token_id=mapping.get("id")).first().values("external_code", "limit_daily_cost","current_date_cost", 
                                                                           "limit_monthly_cost", "current_month_cost", "settings", "use_gpts","open_tts")
    if not _:
        err_code = -99
        msg = "无效的分享码,not info "
        return fail_data(code=err_code, msg=msg)

    if _ and _.get("external_code") and pwd != _.get("external_code"):
        err_code = -99
        msg = "无效的分享码,not match"
        return fail_data(code=err_code, msg=msg)

    user = await TUser.filter(uid=mapping.get("user_id")).first().values("name")
    uinfo =  await UserInfo.filter(uid=mapping.get("user_id")).first()
    if uinfo and uinfo.balance == 0:
        await TUser.filter(uid=mapping.get("user_id")).update(is_indebted=True)

    #     return "用户欠费" if is_zh else "User indebted"
    if mapping.get("status") == GPTStatus.DELETED:
        err_code = -101
        msg = "该分享码已删除"
        return fail_data(code=err_code, msg=msg)
    if mapping.get("status") == GPTStatus.DISABLE:
        err_code = -100
        msg = "该分享码已禁用"
        return fail_data(code=err_code, msg=msg)
    if mapping['tool_id'] == ToolsId.AI_BOX.value:
        domain = kwargs.get("domain")
        pre = kwargs.get("pre")
        t = await TGptTool.filter(domain=domain, prefix=pre, id__not=ToolsId.GPTs.value).order_by('-id').first() or \
            await TGptTool.filter(cn_domain=domain, prefix=pre, id__not=ToolsId.GPTs.value).order_by('-id').first()
        if t:
            if _.get("settings"):
                Tools.log.debug(f"setting_{t.id}:{_['settings'].get(f'settings_{t.id}',{})}")
                _["settings"].update(_['settings'].get(f"settings_{t.id}",{}))
            ai_box = await TAiAppBox.filter(token_id=mapping['id'], tool_id=t.id).select_related(
                "model").first().values("model__name", "model__token_supplier_id_list")
            if ai_box:
                if ai_box.get("model__token_supplier_id_list"):
                    tmp = await TToken.filter(
                        token_supplier_id__in=json.loads(ai_box.get("model__token_supplier_id_list"))).first()
                    if tmp:
                        await TTokenMapping.filter(id=mapping['id']).update(internal_token_id=tmp.id)
                name = ai_box.get("model__name") or ''
            else:
                name = ''
        else:
            name = ''

        mapping['model__name'] = name

    extra_data = {
        "translate_num": str(mapping.get('model__input_token',8000)) or "8000"
    }
    lang = kwargs.get("lang")
    if mapping['tool_id'] == ToolsId.AI_BOX.value:
        model_name = await TGptTool.filter(id=t.id).first().values("name",'en_name','jp_name')
        if lang == 'cn':
            name = model_name.get("name")
        elif lang == 'en':
            name = model_name.get("en_name") or model_name.get("name")
        else:
            name = model_name.get("jp_name") or model_name.get("name")
        model_name = {"name":name}
    else:
        if mapping['model__show_name']:
            model_name ={"name":mapping['model__show_name']}
        else:
            model_name = await TGptTool.filter(id=mapping.get("tool_id")).first().values("name")
            if lang == 'cn':
                name = model_name.get("name")
            elif lang == 'en':
                name = model_name.get("en_name") or model_name.get("name")
            else:
                name = model_name.get("jp_name") or model_name.get("name")
            model_name = {"name": name}
    is_show_balance = _.get("settings",{}).get("show_balance",False)
    hide_brand = _.get("settings",{}).get("hideBrand",False)
    if model_name:
        model_name = model_name.get("name")
    else:
        model_name = " "
    user['name'] = html.escape(user['name'])
    user_info = await UserInfo.filter(uid=mapping.get("user_id")).first().values("region")
    region = user_info.get("region", 0)
    if region == 0:
        url = "https://302ai.cn"
    else:
        url = "https://302.ai"

    if lang == 'cn':
        tool_name = mapping['tool__name']
        info = f"""
                <ol class="app-desc-content">
                {'' if hide_brand else f'<li>此{tool_name}由302.AI用户 <strong>{user.get("name")}</strong> 创建，302.AI是一个生成和分享AI的平台，可以一键生成和分享属于自己的AI工具</li>'}
                <li>此{tool_name}默认的模型为 <strong>{mapping['model__name'] if mapping['model__name'] not in ('', None) else model_name}</strong></li>""" + (f"""
                <li>""" + (f"""
                <div>此{tool_name}的单日限额为 <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTC，已使用 <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                <div>此{tool_name}的单月限额为 <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTC，已使用 <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                <div>此{tool_name}的总限额为 <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTC，已使用 <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC</div>""" if
                                            mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>此{tool_name}的聊天记录均保存在本机，不会被上传，生成此{tool_name}的用户无法看到你的聊天记录</li>
                {'' if hide_brand else f'<li>更多信息请访问：<a target="__blank" href="{url}">302.AI</a></li>'} 
                {f'<li>用户账户余额为: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>
            """
    elif lang == 'jp':
        tool_name = mapping['tool__jp_name']
        info = f"""
                <ol class="app-desc-content">
                {'' if hide_brand else f'<li>この{tool_name}は302.AIユーザーの <strong>{user.get("name")}</strong> によって作成されました。302.AIはAIを生成し共有するプラットフォームで、ワンクリックで自分だけのAIツールを生成し共有することができます</li>'} 
                
                <li>この{tool_name}デフォルトのモデルは <strong>{mapping['model__name'] if mapping['model__name'] not in ('', None) else model_name}</strong>です</li>""" + (f"""
                <li>""" + (f"""
                <div>この{tool_name}の1日の制限は <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTCで、すでに <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC使用されています</div>""" if _.get('limit_daily_cost', 0) else "") + (f"""
                <div>この{tool_name}の1月の制限は <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTCで、すでに <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC使用されています</div>""" if _.get('limit_monthly_cost', 0) else "") + (f"""
                <div>この{tool_name}の総制限は <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTCで、すでに <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC使用されています</div>""" if
                                        mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>この{tool_name}のチャット履歴はすべてローカルに保存され、アップロードされることはありません。この{tool_name}を生成したユーザーはあなたのチャット履歴を見ることはできません</li>
                {'' if hide_brand else f'<li>詳細については以下をご覧ください：<a target="__blank" href="{url}">302.AI</a></li>'} 
                {f'<li>ユーザーの残高は: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>
            """
    else:
        tool_name = mapping['tool__en_name']
        info = f"""
                <ol class="app-desc-content">
                {f'                  <li>This {tool_name} was created by 302.AI user <strong>{user.get("name")}</strong>, which is a platform for creating and sharing AI. 302.AI allows for the one-click creation and sharing of your own AI tool.</li>' if not hide_brand else ''}
                <li>This {tool_name}'s default model is <strong>{mapping['model__name'] if mapping['model__name'] not in ('', None) else model_name}</strong>.</li>""" + (f"""
                <li>""" + (f"""
                <div>Daily Quota is <strong>{to_ptc(_.get('limit_daily_cost', 0), 3)}</strong> PTC, and <strong>{to_ptc(_.get('current_date_cost', 0), 3)}</strong> PTC has been used.</div>""" if _.get('limit_daily_cost', 0) else '') + (f"""
                <div>Monthly Quota is <strong>{to_ptc(_.get('limit_monthly_cost', 0), 3)}</strong> PTC, and <strong>{to_ptc(_.get('current_month_cost', 0), 3)}</strong> PTC has been used.</div>""" if _.get('limit_monthly_cost', 0) else '') + (f"""
                <div>Total Quota is <strong>{to_ptc(mapping['limit_cost'], 3)}</strong> PTC, and <strong>{to_ptc(mapping['current_cost'], 3)}</strong> PTC has been used.</div>""" if
                                            mapping['limit_cost'] else '') + """
                </li>""" if mapping["limit_cost"] or _.get('limit_daily_cost', 0) or _.get('limit_monthly_cost', 0) else '') + f"""
                <li>All chat logs of this {tool_name} will be saved on your local computer and will not be uploaded to cloud servers. The user who created this {tool_name} cannot see your chat records.</li>                  <li>For more information, please view: <a target="__blank" href="{url}">302.AI</a></li>
                {f'<li>The user balance is: {f"{to_ptc(uinfo.balance):.2f}"} PTC</li>' if is_show_balance else ''} 

                </ol>
            """
    data = {
        "api_key": mapping.get("external_token__value"),
        "created_by": user.get("name"),
        "limit_cost": to_ptc(mapping.get("limit_cost"), 3),
        "limit_daily_cost": to_ptc(_.get("limit_daily_cost"), 3),
        "limit_monthly_cost": to_ptc(_.get("limit_monthly__cost"), 3),
        "model_name": mapping.get("model__name") if mapping.get("model__name") else '',
        "extra_data": extra_data,
        'input_token': mapping.get("model__input_token", 0),
        'output_token': mapping.get("model__output_token", 0),
        'max_token': mapping.get("model__output_token", 0),
        "region": region,
        "status": mapping.get('status'),
        "settings": _.get("settings"),
        "info": info,
        "cost": to_ptc(mapping.get("current_cost"), 3),
        "current_date_cost": to_ptc(_.get("current_date_cost"), 3),
        "current_month_cost": to_ptc(_.get("current_month_cost"), 3),
    }

    return suc_data(data=data)


async def create_or_update_tool(**kwargs):
    param_dict = {k: v for k, v in kwargs.items() if v}
    if param_dict.get("id"):
        id = param_dict.pop("id")
        if await TGptTool.filter(id=id).exists():
            await TGptTool.filter(id=id).update(**param_dict)
        else:
            param_dict['id'] = id
            await TGptTool.create(**param_dict)
    else:
        await TGptTool.create(**param_dict)
    return suc_data()

async def create_tool_for_user(uid, tool_id):
    async def get_share_code():
        share_code = random_string(8).lower()
        is_exists = await TTokenMapping.filter(share_code=share_code, deleted_on=0).exists()
        if is_exists:
            return await get_share_code()
        else:
            return share_code

    if token_mapping := await TTokenMapping.filter(user_id=uid, tool_id=tool_id).first():
        _ = await TTokenInfo.filter(token_id=token_mapping.id).first()
        pwd = _.external_code
    else:
        # 创建
        token = create_token()
        token = await TToken.create(value=token, token_supplier_id=0)
        v = await TToken.filter(token_supplier_id__gt=0).order_by('id').values("id")
        pwd = random_string(4).lower()
        token_mapping = await TTokenMapping.create(user_id=uid, external_token_id=token.id,
                                                   internal_token_id=v[0].get("id"),
                                                   name='', share_code=await get_share_code(), is_robot=0,
                                                   remark='', gpts_code='', tool_id=tool_id,
                                                   model_id=0, status=GPTStatus.NORMAL.value, expired_on=0,
                                                   limit_cost=0)
        await TTokenInfo.create(token_id=token_mapping.id, limit_daily_cost=0,
                                settings={}, use_gpts=0, open_tts=0,
                                external_code=pwd)
    tool = await TGptTool.filter(id=token_mapping.tool_id).first()
    cn = await is_cn(uid)
    doamin = tool.cn_domain if cn else tool.domain
    return {
        "domain": doamin,
        "share_code": token_mapping.share_code,
        "pwd": pwd
    }


async def get_chat_log(share_code, sync_pwd):
    share_code = share_code.split("-",1)[0]
    chat_logs = await TSnycLog.filter(share_code=share_code, sync_pwd=sync_pwd, deleted_on=0).order_by(
        "-timestamp").limit(3)
    return suc_data(
        data={"logs": [{"timestamp": _.timestamp, "device": _.device, "log_url": _.log_url} for _ in chat_logs]})


async def upload_chat_record_func(file, share_code, timestamp, device, sync_pwd=""):
    share_code = share_code.split("-",1)[0]
    if not file:
        return fail_data(msg="请上传文件")
    if not share_code:
        return fail_data(msg="请输入分享码")
    if not await TTokenMapping.filter(share_code=share_code, deleted_on=0).exists():
        return fail_data(msg="分享码错误")
    if not sync_pwd:
        while True:
            count = await TSnycLog.filter(share_code=share_code, deleted_on=0).count()
            if count >= 1000:
                sync_pwd = random_string(6, uppercase=False, lowercase=False)
            else:
                sync_pwd = random_string(4, uppercase=False, lowercase=False)
            if not await TSnycLog.filter(share_code=share_code, sync_pwd=sync_pwd, deleted_on=0).exists():
                break
    name = F"{share_code}-{random_string(4, number=False, uppercase=False)}-{timestamp}.json"
    url = await upload_blob(file, file_name=name, pre='chat_record',proxy_blob=True)
    await TSnycLog.create(share_code=share_code, sync_pwd=sync_pwd, timestamp=timestamp, device=device, log_name=name,
                          log_url=url)
    data = {
        "sync_pwd": sync_pwd,
        "file_url": url

    }
    return suc_data(data=data)


async def to_action(token_id=0):
    try:
        async with aiohttp.ClientSession() as session:
            callback_url = Tools.config.app_conf.get('feishu_url',
                                                     'https://test-feishu-webhook.gpt302.com/webhook/update')
            async with session.post(callback_url, data=json.dumps({"id": token_id})) as resp:
                # Tools.log.debug(f"callback_url: {callback_url} json: {await resp.text()}")
                return await resp.json()
    except:
        ...

async def build_share_signature(url=''):
    return await get_signature(url)

async def get_model_price_fnc(model_name):
    if model_name.startswith("gpt-4-gizmo-"):
        model_name ='gpt-4-gizmo-*'
    df = await public()
    if df[df['model'] == model_name].empty:
        return suc_data(data={})
    res = df[df['model'] == model_name].iloc[0].to_dict()
    if not res:
        return suc_data(data={})
    input = res.get("302_input")/1000000
    out_put = res.get("302_output")/1000000
    return suc_data(data={"input": input, "output": out_put})

async def get_tool_list(lang:str):
    if not lang:
        lang = 'cn'
    conn = tortoise.connections.get("gpt_conn")
    records = await conn.execute_query_dict("select *,name as cn_name,description as cn_description,tool_logo_video_url as cn_tool_logo_video_url,logo_url as cn_logo_url from"
                                            " t_gpt302_tool where id>=3 order by ord")
    cates = await conn.execute_query_dict("select * from t_tool_category order by id ")
    cate_dict = {cate.get('id'):cate for cate in cates}
    prefix_dict = {
        "cn":'cn',
        "en":'en',
        "jp":'jp'
    }
    prefix = prefix_dict.get(lang,'en')
    data = []
    cate_list = []

    for i in records:
        if i.get('cate_id'):
            cate_list.append(i.get('cate_id'))
        tmp = {}
        data.append(tmp)
        tmp['tool_id'] = i.get("id")
        tmp['created_time'] = i.get("created_time","")
        tmp['created_on'] = i.get("created_on")
        tmp['open_source_url'] = i.get("open_source_url")
        tmp['open_source'] = i.get("open_source")
        tmp['tool_name'] = i.get(f'{prefix}_name','')
        for key in prefix_dict:
            tmp[f'{key}_tool_name'] = i.get(f'{key}_name', '')
            tmp[f'{key}_tool_show_img_url'] = i.get(f'{key}_tool_logo_url', '')

        tmp['tool_description'] = i.get(f'{prefix}_description','')
        tmp['tool_show_img_url'] = i.get(f'{prefix}_tool_logo_url','')
        tmp['tool_click_img_url'] = i.get(f'{prefix}_logo_url','')
        tmp['tool_click_video_url'] = i.get(f'{prefix}_tool_logo_video_url','')
        tmp['enable'] = i.get(f'deleted_on',0) == 0
        tmp['url'] = i.get(f'prefix','')
        tmp['search_description'] = i.get(f'search_description','')
        tmp['category_name'] = cate_dict.get(i.get('cate_id'),{}).get(f'{prefix}_cate_name','')
        tmp['category_id'] = i.get('cate_id')
    from collections import Counter
    counter = Counter(cate_list)
    summary = []
    for i in cates:
        summary.append({
            "category_name":i.get(f'{prefix}_cate_name',''),
            "category_id": i.get(f'id', 0),
            "count":counter.get(i.get("id"),0),

        })
    return {"data":data,"summary":summary}


async def get_categories_list():
    categories = await TApisCategory.all()
    return suc_data(data=[CategoryResponse(**category.__dict__) for category in categories])


@transactions.atomic("gpt_conn")
async def add_category(category: CategoryRequest):
    category_obj = await TApisCategory.create(**category.model_dump())
    return {"code": 0, "msg": "success", "data": CategoryResponse(**category_obj.__dict__)}


@transactions.atomic("gpt_conn")
async def update_category_at_category_id(category_id: int, category: CategoryRequest):
    category_obj = await TApisCategory.filter(id=category_id).first()
    if not category_obj:
        return fail_data(data={"id": category_id}, code=404, msg="Category not found")
    
    update_data = {key: value for key, value in category.model_dump().items() if value is not None}
    if not update_data:
        return fail_data(data={"id": category_id}, code=400, msg="No fields to update")
    
    await TApisCategory.filter(id=category_id).update(**update_data)
    
    category_obj = await TApisCategory.get(id=category_id)
    return {"code": 0, "msg": "success", "data": CategoryResponse(**category_obj.__dict__)}


@transactions.atomic("gpt_conn")
async def delete_category_at_category_id(category_id: int):
    # 检查是否存在该 category
    category_obj = await TApisCategory.filter(id=category_id).first()
    if not category_obj:
        return fail_data(data={"id": category_id}, code=404, msg="Category not found")
    
    # 检查是否有品牌关联到该 category
    related_brands = await TApisBrand.filter(category_id=category_id).exists()
    if related_brands:
        return fail_data(
            data={"id": category_id},
            code=400,
            msg="Cannot delete category because it has associated brands")
    
    deleted_count = await TApisCategory.filter(id=category_id).delete()
    if not deleted_count:
        return fail_data(data={"id": category_id}, code=500, msg="Failed to delete category")
    
    return suc_data(data={"id": category_id})


async def get_brands_list():
    brands = await TApisBrand.all()
    return suc_data(data=[BrandResponse(**brand.__dict__) for brand in brands])


@transactions.atomic("gpt_conn")
async def add_brand(brand: BrandRequest):
    brand_obj = await TApisBrand.create(**brand.model_dump())
    return {"code": 0, "msg": "success", "data": BrandResponse(**brand_obj.__dict__)}


@transactions.atomic("gpt_conn")
async def update_brand_at_brand_id(brand_id: int, brand: BrandRequest):
    brand_obj = await TApisBrand.filter(id=brand_id).first()
    if not brand_obj:
        return fail_data(data={"id": brand_id}, code=404, msg="Brand not found")
    
    update_data = {key: value for key, value in brand.model_dump().items() if value is not None}
    if not update_data:
        return fail_data(data={"id": brand_id}, code=400, msg="No fields to update")
    
    await TApisBrand.filter(id=brand_id).update(**update_data)    
    brand_obj = await TApisBrand.get(id=brand_id)
    return {"code": 0, "msg": "success", "data": BrandResponse(**brand_obj.__dict__)}


@transactions.atomic("gpt_conn")
async def delete_brand_at_brand_id(brand_id: int):
    deleted_count = await TApisBrand.filter(id=brand_id).delete()
    if not deleted_count:
        return fail_data(data={"id": brand_id}, code=404, msg="Brand not found")

    return suc_data(data={"id": brand_id})


def get_localized_value(json_str: str, lang: str) -> str:
    try:
        data = json.loads(json_str)
        return data.get(lang, data.get("en", ""))
    except (json.JSONDecodeError, TypeError):
        return ""


async def get_apis_list(lang:str):
    if not lang:
        lang = 'zh'
    if lang == 'ja':
        lang = 'jp'
    
    conn = tortoise.connections.get("gpt_conn")
    # 动态查询，提取分类及品牌信息
    records = await conn.execute_query_dict("""
        SELECT 
            c.id AS category_id, 
            c.title AS category_title, 
            c.description AS category_description, 
            c.tags AS category_tags,
            c.link AS category_link,
            b.id AS brand_id, 
            b.title AS brand_title, 
            b.description AS brand_description, 
            b.search_description AS brand_search_description,
            b.link AS brand_link,
            b.img AS brand_img,
            b.tags AS brand_tags
        FROM t_apis_categories c
        LEFT JOIN t_apis_brands b ON c.id = b.category_id
        ORDER BY c.id, b.id;
    """)

    # 记录已经存在的分类，避免重复插入
    categories_dict = {}

    for record in records:
        category_id = record['category_id']

        category_title_lang = get_localized_value(record.get("category_title", "{}"), lang)
        category_description_lang = get_localized_value(record.get("category_description", "{}"), lang)
        category_tags_lang = get_localized_value(record.get("category_tags", "{}"), lang)
        category_link_lang = get_localized_value(record.get("category_link", "{}"), lang)
        # 处理品牌信息
        brand_data = {
            "id": record['brand_id'],
            "title": json.loads(record['brand_title']).get(lang, '') if record['brand_title'] else '',
            "description": json.loads(record['brand_description']).get(lang, '') if record['brand_description'] else '',
            "searchDescription": json.loads(record['brand_search_description']).get(lang, '') if record['brand_search_description'] else '',
            "tags": json.loads(record['brand_tags']).get(lang, '').split(',') if record['brand_tags'] else [],
            "link": json.loads(record['brand_link']).get(lang, '') if record['brand_link'] else '',
            "img": json.loads(record['brand_img']).get(lang, '') if record['brand_img'] else '',
        }

        # 如果该类别还未添加到字典中，则添加
        if category_id not in categories_dict:
            categories_dict[category_id] = {
                "id": category_id,
                "title": category_title_lang,
                "description": category_description_lang,
                "link": category_link_lang,
                "tags": category_tags_lang,
                "brands": []
            }

        # 将品牌添加到对应的类别中
        categories_dict[category_id]['brands'].append(brand_data)

    # 为每个分类添加品牌数量
    for category in categories_dict.values():
        category['nums'] = str(len(category['brands']))

    return suc_data(data={'categories': list(categories_dict.values())})


async def get_models_apis_by_brand_id(lang: str, brand_id: int):
    if not lang:
        lang = 'zh'

    if lang == 'ja':
        lang = 'jp'

    # 查询指定品牌信息以获取 show_model 和 api_info 字段
    conn = tortoise.connections.get("gpt_conn")
    brand = await conn.execute_query_dict(f"""
    SELECT
        c.title as category_title,
		b.title as brand_title,
        b.show_model,
        b.api_info
    FROM t_apis_categories c
    LEFT JOIN t_apis_brands b ON c.id = b.category_id
    WHERE b.id = {brand_id}
    """)

    if not brand:
        return fail_data(data={"models": [], "apis": []}, code=404, msg="Brand not found")
    
    category_title = brand[0].get("category_title")

    # brand_title兼容
    brand_title = brand[0].get("brand_title")
    if 'OpenAI' in brand_title:
        brand_title = {"en": "OpenAI Model", "jp": "OpenAIモデル", "zh": "OpenAI模型"}
    elif 'Anthropic' in brand_title:
        brand_title = {"en": "Anthropic Model", "jp": "Anthropicモデル", "zh": "Anthropic模型"}
    elif 'Gemini' in brand_title:
        brand_title = {"en": "Google Model", "jp": "Googleモデル", "zh": "Google模型"}
    elif 'DALL.E' in brand_title:
        brand_title = {"en": "DALL·E", "jp": "DALL·E", "zh": "DALL·E"}
    elif 'Microsoft Azure' in brand_title:
        brand_title = {"en": "Azure", "jp": "Azure", "zh": "Azure"}
    elif 'Vectorizer.AI' in brand_title:
        brand_title = {"en": "Vectorizer", "jp": "Vectorizer", "zh": "Vectorizer"}
    elif 'Black Forest Labs' in brand_title:
        brand_title = {"en": "Flux", "jp": "Flux", "zh": "Flux"}
    elif 'Luma AI' in brand_title:
        brand_title = {"en": "Luma", "jp": "Luma", "zh": "Luma"}
    else:
        brand_title = json.loads(brand_title)

    category_title = json.loads(category_title)
    is_llm = True if category_title.get('en') == 'LLM' else False

    show_model = brand[0].get("show_model", 0)
    apis = []
    api_info_str = brand[0].get("api_info", "{}")
    try:
        # 将 api_info json字符串转换为列表字典List[dict]
        api_info = json.loads(api_info_str)
    except json.JSONDecodeError:
        api_info = []  # 如果转换失败，返回一个空列表

    for idx, api in enumerate(api_info):
        apis.append({
            "id": idx,
            "title": api.get("title", {}).get(lang, ""),
            "method": api.get("method", ""),
            "url": api.get("url", ""),
            "link": api.get("link", {}).get(lang, ""),
            "stability": api.get("stability", "green"),
        })
    # 如果 show_model 是 0，直接返回 api_info 中的 apis 数据
    if show_model == 0:
        
        return suc_data(data={"apis": apis})

    # 如果 show_model 是 1，查询服务表并返回 models 和 apis
    records = await conn.execute_query_dict(f"""
    SELECT 
        c.id,
        s.id AS service_id,
        s.sort_order,
        s.service_names,
        s.description,
        s.links,
        s.input_token,
        s.output_size,
        s.is_multimodal,
        s.old_input_price,
        s.old_output_price,
        s.input_price,
        s.output_price,
        s.price_prefix,
        s.suffix,
        b.id AS brand_id,
        b.img AS brand_img,
        b.category_id
    FROM t_apis_categories c
    LEFT JOIN t_apis_brands b ON c.id = b.category_id
    LEFT JOIN t_services s ON JSON_UNQUOTE(s.titles->>"$.en") = "{category_title['en']}"
        AND JSON_UNQUOTE(s.tags->>"$.en") = "{brand_title['en']}"
    WHERE b.id = {brand_id}
    ORDER BY sort_order ASC
    """)

    models = []
    # 构建 models 数据
    for record in records:
        # 构建模型信息
        model_data = {
            "id": record.get("service_id", ""),
            "sort_order": record.get("sort_order"),
            "title": get_localized_value(record.get("service_names", "{}"), lang),
            "description": get_localized_value(record.get("description", "{}"), lang),
            "img": get_localized_value(record.get("brand_img", "{}"), lang),
            "link": get_localized_value(record.get("links", "{}"), lang),
            "windowSize": record.get("input_token", 0),
            "outputSize": record.get("output_size", 0),
            "supportMultiModal": record.get("is_multimodal", False),
            "oldPrice": {
                "input": record.get("old_input_price", 0) if record.get("old_input_price", 0) != 0 else None,
                "output": record.get("old_output_price", 0) if record.get("old_output_price", 0) != 0 else None
            } if record.get("old_input_price", 0) != 0 or record.get("old_output_price", 0) != 0 else None,
            "price": {
                "input": record.get("input_price", 0),
                "output": record.get("output_price", 0)
            },
            "pricePrefix": record.get("price_prefix", ""),
            "priceSuffix": get_localized_value(record.get("suffix", "{}"), lang),
        }
        models.append(model_data)

    return suc_data(data={"models": models, "apis": apis, "isLLM": is_llm})


@transactions.atomic("gpt_conn")
async def add_service_at_sort_order(request):
    # 将其他记录的 `sort_order` 向后移动，以便为新的记录腾出位置
    await TService.filter(sort_order__gte=request.sort_order).update(sort_order=F('sort_order') + 1)
    # 插入新记录
    service = await TService.create(**request.dict())

    return suc_data(data={"id": service.id})

@transactions.atomic("gpt_conn")
async def update_service_at_service_id(request, service_id):
    service = await TService.get_or_none(id=service_id)
    if not service:
        return fail_data(data={"id": service_id}, msg="Service not found")
    
    # 更新服务信息
    for key, value in request.dict().items():
        setattr(service, key, value)
    await service.save()

    return suc_data(data={"id": service_id})

@transactions.atomic("gpt_conn")
async def delete_service_at_service_id(service_id: int):
    service = await TService.get_or_none(id=service_id)
    if not service:
        return fail_data(data={"id": service_id}, msg="Service not found")
    
    sort_order = service.sort_order
    # 删除该服务记录
    await service.delete()

    # 将其他记录的 `sort_order` 向前移动
    await TService.filter(sort_order__gte=sort_order).update(sort_order=F('sort_order') - 1)

    return suc_data(data={"id": service_id})


@transactions.atomic("gpt_conn")
async def get_models_param_list():
    models = await TModelParam.all()
    data = [
        {
            "id": model.model_name,
            "support_system_role": model.support_system_role,
            "support_stop": model.support_stop,
            "support_stream": model.support_stream,
            "max_output_tokens": model.max_output_tokens,
            "max_context_tokens": model.max_context_tokens,
            "min_temperature": model.min_temperature,
            "max_temperature": model.max_temperature,
            "min_top_p": model.min_top_p,
            "max_top_p": model.max_top_p,
            "min_frequency_penalty": model.min_frequency_penalty,
            "max_frequency_penalty": model.max_frequency_penalty,
            "min_presence_penalty": model.min_presence_penalty,
            "max_presence_penalty": model.max_presence_penalty,
            "support_attachment_type": model.support_attachment_type,
        }
        for model in models
    ]
    return suc_data(data=data)

async def get_models_param_at_id(model_name):
    model = await TModelParam.filter(model_name=model_name).first()
    if not model:
        return fail_data(data={"model_name": model_name}, code=404, msg="Model not found")

    return {"code": 0, "msg": "success", "data": ModelParamBase(**model.__dict__)}


@transactions.atomic("gpt_conn")
async def add_model_param(model_data: ModelParamBase):
    model = await TModelParam.create(**model_data.model_dump())
    return {"code": 0, "msg": "success", "data": ModelParamBase(**model.__dict__)}


@transactions.atomic("gpt_conn")
async def update_model_param_at_model_id(model_name: str, model_data: ModelParamBase):
    model = await TModelParam.get_or_none(model_name=model_name)
    if not model:
        return fail_data(data={"model_name": model_name}, code=404, msg="Model not found")
    
    update_data = model_data.model_dump(exclude_unset=True)

    await model.update_from_dict(update_data)
    await model.save()
    return {"code": 0, "msg": "success", "data": ModelParamBase(**model.__dict__)}


@transactions.atomic("gpt_conn")
async def delete_model_param_at_model_id(model_name: str):
    deleted_count = await TModelParam.filter(model_name=model_name).delete()
    if deleted_count == 0:
        return fail_data(data={"model_name": model_name}, code=404, msg="Model not found")
    return suc_data(data={"model_id": model_name})


# 应用集成
async def integration_func(action="add", body: dict = {}, token_id=0, app_name=''):
    # 增加

    async def add_integration_func(body: dict):
        # if app_name == APPName.FEISHU:
        data = {}
        rb = await TRobotMapping.filter(token_id=token_id, deleted_on=0).first()
        if rb:
            for i in body.keys():
                if i not in TRobotMapping().to_dict():
                    body.pop(i)
            if not rb.dingtalk_webhook:
                body["dingtalk_webhook"] = random_string(10, uppercase=False)
            rb.dingtalk_webhook = body.get("dingtalk_webhook", random_string(10, uppercase=False))
            await TRobotMapping.filter(token_id=token_id, deleted_on=0).update(**body)
            await to_action(token_id)
        else:
            rb = await TRobotMapping.create(token_id=token_id, app_name=app_name,
                                       feishu_app_id=body.get("feishu_app_id", ""),
                                       feishu_app_secret=body.get("feishu_app_secret", ""),
                                       feishu_verification_token=body.get("feishu_verification_token", ""),
                                       feishu_encrypt_key=body.get("feishu_encrypt_key", ""),
                                       feishu_bot_name=body.get("feishu_bot_name", ""),
                                       dingtalk_webhook=random_string(10, uppercase=False),
                                       dingtalk_client_id=body.get("dingtalk_client_id", ""),
                                       dingtalk_client_secret=body.get("dingtalk_client_secret", '')
                                       )
            await to_action(token_id)
        data = {
            "dingtalk_url":Tools.config.app_conf.get('dingtalk_url','')+f"/{rb.dingtalk_webhook}"
        }

        return suc_data(data = data)

    # 获取
    async def get_integration_func(token_id):
        # if app_name == APPName.FEISHU:
        data = await TRobotMapping.filter(token_id=token_id, deleted_on=0).first()
        data = data.to_dict()
        data['dingtalk_url'] =  Tools.config.app_conf.get('dingtalk_url','')+f"/{data.get('dingtalk_webhook')}"
        return suc_data(data=data)

    # 修改
    async def update_integration_func(token_id, body):
        # if app_name == APPName.FEISHU:
        for i in body.keys():
            if i not in TRobotMapping().to_dict():
                body.pop(i)
        rb = await TRobotMapping.filter(token_id=token_id).first()
        await TRobotMapping.filter(token_id=token_id).update(**body)
        await to_action(token_id)
        data = {}
        data['dingtalk_url'] =  Tools.config.app_conf.get('dingtalk_url','')+f"/{rb.dingtalk_webhook}"

        return suc_data(data = data)

    # 删除
    async def delete_integration_func(token_id):
        if app_name == APPName.FEISHU:
            await TRobotMapping.filter(token_id=token_id).update(deleted_on=1)
            return suc_data()

    token_id = body.get("token_id") or token_id
    # feishu_app_id = body.get("feishu_app_id", "")
    # feishu_app_secret = body.get("feishu_app_secret", "")
    # feishu_verification_token = body.get("feishu_verification_token", "")

    if not token_id:
        return fail_data(msg="token_id不能为空")
    _ = body.copy()
    for i in body.keys():
        if i not in TRobotMapping().to_dict():
            _.pop(i)
    if await TRobotMapping.filter( deleted_on=0,**_).exists():
        code = StateCode.AppFeiShuExist.value
        for i in _:
            if i.startswith("dingtalk"):
                code = StateCode.AppDingTalkExist.value
                break
            if i.startswith("feishu"):
                code = StateCode.AppFeiShuExist.value
                break
        return fail_data(code=code, msg="配置已存在")
    if action == "add":
        return await add_integration_func(body)
    if action == "get":
        return await get_integration_func(token_id)
    if action == "update":
        return await update_integration_func(token_id, body)
    if action == "delete":
        return await delete_integration_func(token_id)


@cache(CacheSaveWhere.redis,ttl=60*5)
async def public():
    models = await TModel.all().annotate(model_name=F('name')).values("id","show_in_robot","show_in_api","input_token"
                                                                      ,"model_name","show_name","tool_id_list","model_type",
                                                                      "en_model_type","ord","token_supplier_id_list","remark","en_remark",'jp_remark')
    prices = await TModelPrice.all().values("model","input_price","output_price")
    supplier = await TTokenSupplier.all().values("id","rate")
    df_models = pd.DataFrame(models)
    df_prices = pd.DataFrame(prices)
    df_supplier = pd.DataFrame(supplier)
    df = df_models.merge(df_prices,left_on='model_name',right_on='model')
    df['token_supplier_id'] = df['token_supplier_id_list'].map(lambda x: json.loads(x)[0])
    df = df.merge(df_supplier, left_on='token_supplier_id', right_on='id')
    df = df.explode("tool_id_list")
    def ceil_to_two_decimal(number,rate=1):
        if int(rate) == 1:
            return number
        if number < 1:
            return math.ceil(number * 100) / 100
        return number
    df['302_input'] = df.apply(
        lambda x: x['input_price']*x['rate'] if x['input_price'] == 0 else ceil_to_two_decimal(x['input_price']*x['rate']*100*10,x['rate']), axis=1)
    df['302_output'] =  df.apply(
        lambda x: x['output_price']*x['rate'] if x['input_price'] == 0 else ceil_to_two_decimal(x['output_price']*x['rate']*100*10,x['rate']), axis=1)
    df['origin_input'] = df.apply(
        lambda x: x['input_price'] if x['input_price'] == 0 else ceil_to_two_decimal(x['input_price']*100*10,x['rate']), axis=1)
    df['origin_output'] = df.apply(
        lambda x: x['output_price'] if x['input_price'] == 0 else ceil_to_two_decimal(x['output_price']*100*10,x['rate']), axis=1)
    return df




async def get_robot_mapping():
    def build_tree(df, parent_id=0):
        nodes = []
        children = df[df['p_id'] == parent_id]

        for _, child in children.iterrows():
            items = build_tree(df, child['id'])
            tags = {'zh':[],'en':[],'jp':[]}
            tmp = []
            for item in items:
                _id = cate_models_dict.get(item.get('id'))
                if _id:
                    _ = []
                    tags['zh'].append(item.get('title'))
                    tags['en'].append(item.get('en_title'))
                    tags['jp'].append(item.get('jp_title'))
                    list_id = []
                    for i in _id:
                        model = model_dict.get(i)
                        if not model:
                            continue
                        list_id.append((model.get("ord"), i))

                    list_id.sort()
                    list_id = [i[1] for i in list_id]
                    for i in list_id:
                        record = model_dict.get(i)

                        _.append({
                            "model": {"zh":record.get("show_name"),"en":record.get("show_name"),'jp':record.get('show_name')},

                            "price_prefix":"",
                            "suffix": {"en": "call" if round(record.get("origin_input"), 2) == 0 else "", "zh": "次" if round(record.get("origin_input"), 2) == 0 else "",
                                       "jp": "次" if round(record.get("origin_input"), 2) == 0 else ""},
                            "link": "",
                            "input_token":str(record.get("input_token","-")),

                            "pricing": {
                                "input": round(record.get('302_input'), 2),
                                "output": round(record.get("302_output"), 2)
                            },
                            "old_pricing": {
                                "input": round(record.get("origin_input"), 2),
                                "output": round(record.get("origin_output"), 2)
                            },
                            "des": {
                                "zh": record.get("remark"),
                                "en": record.get("en_remark"),
                                "jp": record.get("jp_remark")
                            }

                        })
                    tmp.append(_)

            if tmp:
                items = tmp

            node = {
                'id': child['id'],
                'title': child['name'],
                'en_title': child['en_name'],
                'jp_title': child['jp_name'],
                # 'tags': [i.get("title") for i in items],
                'items': items
            }
            if tags:
                node['tags'] = tags
            nodes.append(node)

        return nodes

    models = await public()
    model_dict = {i.get("id_x"):i for i in models.to_dict("records")}
    conn = tortoise.connections.get("gpt_conn")
    cates = await conn.execute_query_dict("select * from t_category order by ord")
    cate_models = await conn.execute_query_dict("select * from t_category_model where show_in_robot")
    cate_models_dict = pd.DataFrame(cate_models).groupby("category_id")['model_id'].agg(list).to_dict()
    df_cate =pd.DataFrame(cates)
    print(df_cate)
    x = build_tree(df_cate)

    return x

async def get_api_robot():
    models = await public()
    models = models[models['show_in_api']==1]
    model_dict = {i.get("id_x"): i for i in models.to_dict("records")}
    conn = tortoise.connections.get("gpt_conn")
    cate_models = await conn.execute_query_dict("""

select name,en_name,jp_name,ord,JSON_ARRAYAGG(model_id) model_list,JSON_ARRAYAGG(zh_link) as zh_links, JSON_ARRAYAGG(en_link) as en_links,JSON_ARRAYAGG(jp_link) as jp_links,JSON_ARRAYAGG(model_ord) as model_ord from t_category_model left join t_category on category_id=t_category.id  left join (select id,ord as model_ord from t_models) t_models on t_models.id=model_id where name not in ('Midjourney','SD3','其他模型') and show_in_api and  t_category.id >0 group by name,en_name,ord,jp_name order by ord
""")


    tags = []
    en_tags = []
    jp_tags = []
    items = []
    # cate_models_dict.sort_values(key=lambda x:cate_order.index(x.get("name")))
    for v in cate_models:
        tags.append(v.get("name"))
        jp_tags.append(v.get("jp_name"))
        en_tags.append(v.get("en_name"))
        _ = []
        items.append(_)
        zh_links = json.loads(v.get("zh_links"))
        en_links = json.loads(v.get("en_links"))
        jp_links = json.loads(v.get("jp_links"))
        model_list = json.loads(v.get("model_list"))
        model_ord = json.loads(v.get("model_ord"))
        c = [(k,i) for i, k in zip(model_list, model_ord)]
        c.sort()
        model_list = [i[1] for i in c]
        for index,i in enumerate(model_list):
            if not model_dict.get(i):
                continue
            _.append(
                    {
                    "model": {"zh":model_dict.get(i).get("show_name"),"en":model_dict.get(i).get("show_name"),'jp':model_dict.get(i).get("show_name")},
                    "price_prefix": "",
                    "input_token": str(model_dict.get(i).get("input_token",0)) if model_dict.get(i).get("input_token",0) !=0 else "/",
                    "suffix": {"en": "", "zh": "",'jp':""},
                    "link": {"zh":zh_links[index] or '', "en":en_links[index] or '','jp':jp_links[index] or ''},
                    "pricing": {
                        "input": round(model_dict.get(i).get('302_input'), 2),
                        "output": round(model_dict.get(i).get("302_output"), 2)
                    },
                    "old_pricing": {
                        "input": round(model_dict.get(i).get("origin_input"), 2),
                        "output": round(model_dict.get(i).get("origin_output"), 2)
                    },
                    "des": {
                        "zh": model_dict.get(i).get("remark"),
                        "en": model_dict.get(i).get("en_remark"),
                        'jp':model_dict.get(i).get('jp_remark'),
                    }

                }
            )
    result = [
        {
                "id": 0,
            "title": "语言大模型",
            "en_title": "LLM",
            "jp_title": "大規模言語モデル",
            "tags": {"zh":tags,"en":en_tags,'jp':jp_tags},
            "items": items
        }
    ]
    return result

async def get_tools():
    filter = {}
    filter['id__gte'] = ToolsId.OTHER.value

    tools = await TGptTool.filter(**filter).filter(deleted_on=0).order_by("ord").values("id", 'name', 'en_name','jp_name')
    conn = tortoise.connections.get("gpt_conn")
    cate_models = await conn.execute_query_dict("select name as c_name,en_name as en_c_name,jp_name as jp_c_name,model_id from t_category_model left join t_category on category_id=t_category.id where show_in_tool")
    cate_order = await conn.execute_query_dict("select name from t_category where id<17 order by ord")
    cate_order_key = [i.get("name") for i in cate_order]  # 排序key
    df_tmp = pd.DataFrame(cate_models)

    df = await public()
    # df.merge(df_tools, left_on='tool_id_list', right_on='id')
    df = df.merge(df_tmp,left_on='id_x',right_on='model_id')
    df['c_name'] = pd.Categorical(df['c_name'], categories=cate_order_key, ordered=True)
    df = df.sort_values(['c_name', 'ord'])
    result = []


    api_tool_dict = {
        "AI 图片翻译": {
            "title": "AI 图片翻译",
            "en_title": "AI Image Translation",
            "jp_title": "AI画像翻訳",
            "tags": {"zh": ["功能"], "en": ["Feature"], "jp": ["機能"]},
            "items": [
                [
                    {"des": {'zh': '图片翻译', 'en': 'Image Translation', 'jp': '画像翻訳'},
                     "model": {
                         "zh": "Image Translation",
                         "en": "Image Translation",
                         "jp": "Image Translation"
                     },
                     "price_prefix": "",
                     "suffix": {'en': 'call', 'zh': '次', 'jp': '回'},
                     "link": "",
                     "pricing": {'input': 0, 'output': 0.02}
                     }
                ]
            ]
        },
        "AI内容检测":{
        "title": "AI内容检测",
        "en_title": "AI Content Detection",
        "jp_title": "AIコンテンツ検出",
        "tags": {"zh": ["功能"], "en": ["Feature"], "jp": ["機能"]},
        "items": [
            [
                {
                    "model": {
                        "zh": "Text-classify",
                        "en": "Text-classify",
                        "jp": "Text-classify"
                    },
                    "price_prefix": "",
                    "suffix": {
                        "en": "call",
                        "zh": "次",
                        "jp": "回"
                    },
                    "link": "",
                    "pricing": {
                        "input": 0,
                        "output": 0.01
                    },
                    "des": {
                        "zh": "检测AI文字",
                        "en": "Detect AI Text",
                        "jp": "AIテキストの検出"
                    }
                },
                {
                    "model": {
                        "zh": "Image-classify",
                        "en": "Image-classify",
                        "jp": "Image-classify"
                    },
                    "price_prefix": "",
                    "suffix": {
                        "en": "call",
                        "zh": "次",
                        "jp": "回"
                    },
                    "link": "",
                    "pricing": {
                        "input": 0,
                        "output": 0.01
                    },
                    "des": {
                        "zh": "检测AI图像",
                        "en": "Detect AI Images",
                        "jp": "AI画像の検出"
                    }
                }
            ]
        ]
    },'AI老照片修复': {'title': 'AI老照片修复', 'en_title': 'AI Old-Photo Restoration', 'jp_title': 'AI古写真修復', 'items': [[{'model': {'zh': 'Enlarge', 'en': 'Enlarge', 'jp': 'Enlarge'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.005}, 'des': {'zh': '无损放大', 'en': 'Photo Upscale', 'jp': '画質劣化なし拡大'}}, {'model': {'zh': 'Enhance', 'en': 'Enhance', 'jp': 'Enhance'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.01}, 'des': {'zh': '人物增强', 'en': 'Portrait Photo Enlargement', 'jp': '人物強調'}}, {'model': {'zh': 'Colorize', 'en': 'Colorize', 'jp': 'Colorize'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.01}, 'des': {'zh': '黑白上色', 'en': 'Fast or Professional \nPhoto Colorization', 'jp': '白黒写真の色付け'}}]], 'tags': {'zh': ['功能'], 'en': ['Feature'], 'jp': ['機能']}}, 'AI电商场景图生成': {'title': 'AI电商场景图生成', 'en_title': 'AI E-commerce Scene Generation', 'jp_title': 'AIネット通販シーン生成', 'items': [[{'model': {'zh': 'Scenario Generation（Text）', 'en': 'Scenario Generation（Text）', 'jp': 'Scenario Generation（Text）'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '场景生成（文字）', 'en': 'Customized Scenario Generation', 'jp': 'シーン生成（テキスト）'}}, {'model': {'zh': 'Scenario Generation（Image）', 'en': 'Scenario Generation（Image）', 'jp': 'Scenario Generation（Image）'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '场景生成（图片）', 'en': 'Customized Scenario Generation', 'jp': 'シーン生成（画像）'}}]], 'tags': {'zh': ['功能'], 'en': ['Feature'], 'jp': ['機能']}},
                     'AI视频生成器': {'title': 'AI视频生成器', 'en_title': 'AI Video Generator', 'jp_title': 'AI動画生成器', 'items':
                         [
                         [
                             {'model': {'zh': 'Luma', 'en': 'Luma', 'jp': 'Luma'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.4}, 'des': {'zh': '文字生成视频', 'en': 'Text to Video', 'jp': 'テキストから動画'}}],
                             [
                             {'model': {'zh': 'Runway Gen-3', 'en': 'Runway Gen-3', 'jp': 'Runway Gen-3'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.5}, 'des': {'zh': '文字生成视频', 'en': 'Text to Video', 'jp': 'テキストから動画'}}],[
                {
                    "model": {
                        "zh": "Video",
                        "en": "Video",
                        "jp": "Video"
                    },
                    "price_prefix": "",
                    "suffix": {
                        "en": "call",
                        "zh": "次",
                        "jp": "回"
                    },
                    "link": "",
                    "pricing": {
                        "input": 0,
                        "output": 0.4
                    },
                    "des": {
                        "zh": "视频生成",
                        "en": "Video Generation",
                        "jp": "動画生成"
                    }
                }
            ]], 'tags': {'zh': ['Luma AI', 'Runway','Minimax'], 'en': ['Luma AI', 'Runway','Minimax'], 'jp': ['Luma AI', 'Runway','Minimax']}}, 'AI图片工具箱':
        {'title': 'AI图片工具箱', 'en_title': 'AI Image Toolbox', 'jp_title': 'AI画像ツールボックス',
         'items':
             [
             [
                 {
                    "model": {
                        "zh": "QRCode",
                        "en": "QRCode",
                        "jp": "QRCode"
                    },
                    "price_prefix": "",
                    "suffix": {
                        "en": "call",
                        "zh": "次",
                        "jp": "回"
                    },
                    "link": "",
                    "pricing": {
                        "input": 0,
                        "output": 0.01
                    },
                    "des": {
                        "zh": "艺术二维码生成",
                        "en": "Artistic QR Code Generation",
                        "jp": "アートQRコード生成"
                    }

                },
                 {
                     'model': {'zh': 'Remove-background', 'en': 'Remove-background', 'jp': 'Remove-background'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '去除背景', 'en': 'Remove-background', 'jp': '背景削除'}}, {'model': {'zh': 'Erase', 'en': 'Erase', 'jp': 'Erase'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '物体消除', 'en': 'Erase', 'jp': 'オブジェクト消去'}}, {'model': {'zh': 'Relight', 'en': 'Relight', 'jp': 'Relight'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '背景替换', 'en': 'Background Replacement', 'jp': '背景置換'}}, {'model': {'zh': 'Vectorize', 'en': 'Vectorize', 'jp': 'Vectorize'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.3}, 'des': {'zh': '图片矢量化', 'en': 'Vectorize', 'jp': '画像ベクトル化'}}, {'model': {'zh': 'Upscale', 'en': 'Upscale', 'jp': 'Upscale'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.01}, 'des': {'zh': '图片放大', 'en': 'Upscale', 'jp': '画像拡大'}}, {'model': {'zh': 'Super-Upscale', 'en': 'Super-Upscale', 'jp': 'Super-Upscale'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '超级图片放大', 'en': 'Super-Upscale', 'jp': '超高画質拡大'}}, {'model': {'zh': 'Colorize', 'en': 'Colorize', 'jp': 'Colorize'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.01}, 'des': {'zh': '黑白上色', 'en': 'Colorize', 'jp': '白黒写真の色付け'}}, {'model': {'zh': 'Face-swap', 'en': 'Face-swap', 'jp': 'Face-swap'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': 'AI换脸', 'en': 'Face-swap', 'jp': 'AI顔交換'}}, {'model': {'zh': 'Inpaint', 'en': 'Inpaint', 'jp': 'Inpaint'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '图片修改', 'en': 'Image Editing', 'jp': '画像編集'}}, {'model': {'zh': 'Structure', 'en': 'Structure', 'jp': 'Structure'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '以图生图', 'en': 'Draft to Image', 'jp': '画像から画像生成'}}, {'model': {'zh': 'Sketch', 'en': 'Sketch', 'jp': 'Sketch'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '草稿生图', 'en': 'Sketch to Image', 'jp': 'スケッチから画像生成'}}, {'model': {'zh': 'Crop Image', 'en': 'Crop Image', 'jp': 'Crop Image'}, 'price_prefix': '', 'suffix': {'en': 'time', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.0}, 'des': {'zh': '裁剪图片', 'en': 'Precisely Crop the Image', 'jp': '画像トリミング'}}, {'model': {'zh': 'Flux-Dev', 'en': 'Flux-Dev', 'jp': 'Flux-Dev'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '图片生成  Flux-Dev', 'en': 'Image Generation  Flux-Dev', 'jp': '画像生成  Flux-Dev'}}, {'model': {'zh': 'Flux-Pro', 'en': 'Flux-Pro', 'jp': 'Flux-Pro'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.1}, 'des': {'zh': '图片生成  Flux-Pro', 'en': 'Image Generation  Flux-Pro', 'jp': '画像生成  Flux-Pro'}}, {'model': {'zh': 'Flux-Schnell', 'en': 'Flux-Schnell', 'jp': 'Flux-Schnell'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.005}, 'des': {'zh': '图片生成  Flux-Schnell', 'en': 'Image Generation  Flux-Schnell', 'jp': '画像生成  Flux-Schnell'}}, {'model': {'zh': 'Flux-Realism', 'en': 'Flux-Realism', 'jp': 'Flux-Realism'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '图片生成  Flux-Realism', 'en': 'Image Generation  Flux-Realism', 'jp': '画像生成  Flux-Realism'}}, {'model': {'zh': 'SD3', 'en': 'SD3', 'jp': 'SD3'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.05}, 'des': {'zh': '图片生成  SD3', 'en': 'Image Generation  SD3', 'jp': '画像生成  SD3'}}, {'model': {'zh': 'SDXL-Lightning', 'en': 'SDXL-Lightning', 'jp': 'SDXL-Lightning'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.005}, 'des': {'zh': '图片生成  SDXL-Lightning', 'en': 'Image Generation  SDXL-Lightning', 'jp': '画像生成  SDXL-Lightning'}}, {'model': {'zh': 'Aura-Flow', 'en': 'Aura-Flow', 'jp': 'Aura-Flow'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.01}, 'des': {'zh': '图片生成  Aura-Flow', 'en': 'Image Generation  Aura-Flow', 'jp': '画像生成  Aura-Flow'}}, {'model': {'zh': 'Kolors', 'en': 'Kolors', 'jp': 'Kolors'}, 'price_prefix': '', 'suffix': {'en': 'call', 'zh': '次', 'jp': '回'}, 'link': '', 'pricing': {'input': 0, 'output': 0.005}, 'des': {'zh': '图片生成  可灵', 'en': 'Image Generation  Kolors', 'jp': '画像生成  Kolors'}},{'model': {'zh': 'Image Text Translation',
      'en': 'Image Text Translation',
      'jp': 'Image Text Translation'
    },
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'
    },
     'link': {'zh': None, 'en': None, 'jp': None
    },
     'pricing': {'input': 0, 'output': 0.02
    },
     'des': {'zh': '图片文字翻译',
      'en': 'Image Text Translation',
      'jp': '画像テキスト翻訳 '
    }
},
{'model': {'zh': 'Image Text Erasure',
      'en': 'Image Text Erasure',
      'jp': 'Image Text Erasure'
    },
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'
    },
     'link': {'zh': None, 'en': None, 'jp': None
    },
     'pricing': {'input': 0, 'output': 0.02
    },
     'des': {'zh': '图片文字擦除', 'en': 'Image Text Erasure', 'jp': '画像テキスト消去'
    }
}
             ],], 'tags': {'zh': ['功能'], 'en': ['Feature'], 'jp': ['機能']}}
        ,"AI 音乐制作":{'title': 'AI 音乐制作',
  'en_title': 'AI Music Production',
  'jp_title': 'AI音楽制作',
  'tags': {'zh': ['功能'], 'en': ['Feature'], 'jp': ['機能']},
  'items': [[{'model': {'zh': 'Generated Lyrics',
      'en': 'Generated Lyrics',
      'jp': 'Generated Lyrics'},
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'},
     'link': '',
     'pricing': {'input': 0, 'output': 0.01},
     'des': {'zh': 'AI生成歌词',
      'en': 'Generated Lyrics by AI',
      'jp': 'AIが生成した歌詞'}},
    {'model': {'zh': 'Pure Music (v3.0)',
      'en': 'Pure Music (v3.0)',
      'jp': 'Pure Music (v3.0)'},
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'},
     'link': '',
     'pricing': {'input': 0, 'output': 0.05},
     'des': {'zh': '纯音乐', 'en': 'Pure Music', 'jp': '純音楽'}},
    {'model': {'zh': 'Custom Mode (v3.0)',
      'en': 'Custom Mode (v3.0)',
      'jp': 'Custom Mode (v3.0)'},
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'},
     'link': '',
     'pricing': {'input': 0, 'output': 0.05},
     'des': {'zh': '自定义模式', 'en': 'Custom Mode', 'jp': 'カスタムモード '}},
    {'model': {'zh': 'Pure Music (v3.5)',
      'en': 'Pure Music (v3.5)',
      'jp': 'Pure Music (v3.5)'},
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'},
     'link': '',
     'pricing': {'input': 0, 'output': 0.05},
     'des': {'zh': '纯音乐', 'en': 'Pure Music', 'jp': '純音楽'}},
    {'model': {'zh': 'Custom Mode (v3.5)',
      'en': 'Custom Mode (v3.5)',
      'jp': 'Custom Mode (v3.5)'},
     'price_prefix': '',
     'suffix': {'en': 'call', 'zh': '次', 'jp': '回'},
     'link': '',
     'pricing': {'input': 0, 'output': 0.05},
     'des': {'zh': '自定义模式', 'en': 'Custom Mode', 'jp': 'カスタムモード '}}]]}
    }


    for tool in tools:
        df_tmp = df[df['tool_id_list'] == tool.get("id")]
        if df_tmp.empty:
            _ = api_tool_dict.get(tool.get("name"))
            if _:
                result.append(_)
            continue
        # df_tmp = df_tmp[df_tmp['model_type']>'']
        # df_tmp = df_tmp.sort_values(['model_type', 'ord'])
        tags = []
        tags_en = []
        tags_jp = []
        items = []
        for i in df_tmp.to_dict("records"):
            model_type = i.get('c_name')
            en_model_type = i.get('en_c_name')
            jp_model_type = i.get('jp_c_name')
            if model_type not in tags:
                tags.append(model_type)
                tags_en.append(en_model_type)
                tags_jp.append(jp_model_type)
                items.append([])
            items[-1].append(
                {
                    "model": {"zh":i.get("show_name"),"en":i.get("show_name"),'jp':i.get('show_name')},
                    "input_token":str(i.get("input_token","-")),
                    "pricing": {
                        "input": round(i.get('302_input'), 2),
                        "output": round(i.get("302_output"), 2)
                    },
                    "old_pricing": {
                        "input": round(i.get("origin_input"), 2),
                        "output": round(i.get("origin_output"), 2)
                    },
                    "des": {
                        "zh": i.get("remark"),
                        "en": i.get("en_remark"),
                        'jp':i.get('jp_remark')
                    }

                }
            )

        value = {
            "title": tool.get("name"),
            "en_title": tool.get("en_name"),
            "jp_title": tool.get("jp_name"),
            "tags": {"zh": tags, "en": tags_en,'jp':tags_jp},
            "items": items
        }
        result.append(value)
    return result

async def get_items(service_type: str):
    '''
    service_type: 服务类型——“机器人超市/工具超市/API超市”

    通过t_services表查询指定服务类型的数据并返回为item格式。
    '''
    
    # 连接services/models/model_price/token_supplier表，用于优先获取price表的价格。
    conn = tortoise.connections.get("gpt_conn")
    services = await conn.execute_query_dict(f"""
        SELECT DISTINCT 
            t_services.id,
            t_services.service_names,
            t_services.suffix,
            t_services.price_prefix,
            t_services.input_token,
            t_services.links,
            t_services.description,
            t_services.input_price,
            t_services.output_price,
            t_services.old_input_price,
            t_services.old_output_price,
            t_services.model_id,
            t_services.sort_order,
            models.show_name,
            models.name as model_name,
            price.token_count,
            price.input_price as origin_input_price,
            price.output_price as origin_output_price,
            price.rate 
        FROM t_services 
        LEFT JOIN t_model_price price ON price.id=t_services.model_id 
        LEFT JOIN t_models models ON models.name=price.model 
        WHERE service_type='{service_type}' 
        ORDER BY t_services.sort_order
    """)
    def format_price(number):
        rounded_value = round(number, 3)
        if rounded_value.is_integer():
            return int(rounded_value)  # 如果是整数，返回整数类型
        return rounded_value  # 否则返回保留三位小数的值
    
    items = []
    for service in services:
        if service.get('model_name'):
            service_names = json.loads(service.get('service_names', '{}'))
            model_name = service.get('model_name')
            
            # 更新service_names
            if (service_names.get('zh') != model_name or 
                service_names.get('en') != model_name or 
                service_names.get('jp') != model_name):
                    
                await conn.execute_query("""
                    UPDATE t_services 
                    SET service_names = %s
                    WHERE id = %s
                """, [
                    json.dumps({
                        'zh': model_name,
                        'en': model_name,
                        'jp': model_name
                    }),
                    service['id']
                ])
        # 由于使用execute_query_dict方法查询得到的是一个字典列表(List[Dict])，并且字典内的json，如 links 为字符串，需要先转换格式才能使用。
        service['service_names'] = json.loads(service.get('service_names', {}))
        service['suffix'] = json.loads(service.get('suffix', {}))
        service['links'] = json.loads(service.get('links', {}))
        service['description'] = json.loads(service.get('description', {}))
        # 初始化价格
        pricing_input = service['input_price']
        pricing_output = service['output_price']
        old_pricing_input = service['old_input_price']
        old_pricing_output = service['old_output_price']

        # 如果绑定了model_id并且在models表和model_price表中查得到价格
        # 则将origin_input_price/origin_output_price 乘以1000 代替old_pricing中的input/output，乘以 rate*1000 代替pricing中的input/output
        # 当token_count为0时 显示价格*1
        if (service['origin_input_price'] or service['origin_output_price']) and service['rate']:
            multiplier = service.get('token_count') if service.get('token_count', 0) != 0 else 1
            
            new_old_pricing_input = format_price(service['origin_input_price'] * multiplier)
            new_pricing_input = format_price(service['origin_input_price'] * service['rate'] * multiplier)
            new_old_pricing_output = format_price(service['origin_output_price'] * multiplier)
            new_pricing_output = format_price(service['origin_output_price'] * service['rate'] * multiplier)
            
            # 如果价格不同则更新
            if (pricing_input != new_pricing_input or
                pricing_output != new_pricing_output or
                old_pricing_input != new_old_pricing_input or
                old_pricing_output != new_old_pricing_output):
                await conn.execute_query(f"""
                    UPDATE t_services 
                    SET 
                        input_price={new_pricing_input}, 
                        output_price={new_pricing_output}, 
                        old_input_price={new_old_pricing_input}, 
                        old_output_price={new_old_pricing_output} 
                    WHERE id={service['id']}
                """)
            
            pricing_input, pricing_output = new_pricing_input, new_pricing_output
            old_pricing_input, old_pricing_output = new_old_pricing_input, new_old_pricing_output

        # 填充item
        item_data = {
            "model": {
                "zh": service['service_names'].get("zh", ""),
                "en": service['service_names'].get("en", ""),
                "jp": service['service_names'].get("jp", "")
            },
            "price_prefix": service['price_prefix'],
            **({"input_token": str(service['input_token'])} if service['input_token'] is not None else {}),
            "suffix": {
                "zh": service['suffix'].get("zh", ""),
                "en": service['suffix'].get("en", ""),
                "jp": service['suffix'].get("jp", "")
            },
            "link": {
                "zh": service['links'].get("zh", ""),
                "en": service['links'].get("en", ""),
                "jp": service['links'].get("jp", "")
            },
            "pricing": {
                "input": pricing_input,
                "output": pricing_output
            },
            "old_pricing": {
                "input": old_pricing_input,
                "output": old_pricing_output
            },
            "des": {
                "zh": service['description'].get("zh", ""),
                "en": service['description'].get("en", ""),
                "jp": service['description'].get("jp", "")
            }
        }

        # 设置price_output为指定特殊值时，添加other_pricing字段
        if pricing_output == -1:
            item_data["other_pricing"] = {
                "zh": "参考语言大模型价格",
                "en": "Refer to LLM",
                "jp": "参考大規模言語モデル価格表",
            }
        elif pricing_output == -2:
            item_data["other_pricing"] = {
                "zh": "与图生视频价格一致",
                "en": "Same price as image-generated video",
                "jp": "画像生成ビデオと同じ価格",
            }
        elif pricing_output == -3:
            item_data["other_pricing"] = {
                "zh": "参考相关模型价格",
                "en": "Refer to related model prices",
                "jp": "関連するモデル価格を参照してください"
            }


        items.append(item_data)

    return items

async def build_result(items, service_type):
    """
    items: 使用get_items方法返回的所有服务项
    service_type: 服务类型——“机器人超市/工具超市/API超市”
    
    将获取到的items组装成json。
    """
    result = []
    conn = tortoise.connections.get("gpt_conn")
    titles = await conn.execute_query_dict(f"SELECT titles, MIN(sort_order) AS sort_order FROM t_services WHERE service_type='{service_type}' GROUP BY titles ORDER BY sort_order")
    start_idx = 0
    # 以所有title进行循环，组装json格式数据
    for title in titles:
        title = json.loads(title['titles'])
        # 获取title对应tags数据
        tags_by_title = await conn.execute_query_dict(f"SELECT DISTINCT tags FROM t_services WHERE JSON_UNQUOTE(JSON_EXTRACT(titles, '$.en')) = '{title['en']}' GROUP BY tags ORDER BY MIN(sort_order)")
        # 一个tag对应一个列表存放item数量
        services_num = await conn.execute_query_dict(f"SELECT JSON_UNQUOTE(JSON_EXTRACT(tags, '$.en')) AS en, COUNT(*) AS count FROM t_services WHERE JSON_UNQUOTE(JSON_EXTRACT(titles, '$.en')) = '{title['en']}' GROUP BY en ORDER BY MIN(sort_order)")
        nums = [tag_nums['count'] for tag_nums in services_num]
        group_items = []
        # 分组划分item
        for num in nums:
            subset = items[start_idx:start_idx + num]
            group_items.append(subset)
            start_idx += num

        # 获取tags
        format_tags = {"zh": [], "en": [], "jp": []}
        for tag in tags_by_title:
            tags = json.loads(tag['tags'])
            format_tags["zh"].append(tags["zh"])
            format_tags["en"].append(tags["en"])
            format_tags["jp"].append(tags["jp"])

        # 组装每个title下的数据
        service_data = {
            "title": title.get("zh", ""),
            "en_title": title.get("en", ""),
            "jp_title": title.get("jp", ""),
            "tags": {
                "zh": format_tags.get("zh", []),
                "en": format_tags.get("en", []),
                "jp": format_tags.get("jp", [])
            },
            "items": group_items
        }
        result.append(service_data)

    return result

@cache(ttl=60*10)
async def get_models_from_name(name):
    if name == 'api':
        service_type = 'API超市'
        items = await get_items(service_type)
        result = await build_result(items, service_type)
        return result
    elif name == '机器人超市' or '工具超市':
        items = await get_items(name)
        result = await build_result(items, name)
        return result
    else:
        return
        


async def get_text_summary(url,Token,model="",**kwargs):
    import textract
    import markdown
    from lxml import etree
    Authorization = Token
    url = url.replace('file.302ai.cn','proxyblob.blob.core.windows.net')

    def convert_markdown_to_html(file):
        with open(file, 'r', encoding='utf-8') as f:
            file = f.read()
            Tools.log.debug(f"file_content:{file}")
            html = markdown.markdown(file)
            Tools.log.debug(f"html:{html}")
            return html

    async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        async with session.get(url) as resp:
            Tools.log.debug(f"url:{url}")
            data = await resp.read()
    url_parts = urlparse(url)
    # 解析url
    name = url_parts.netloc.replace(".", "") + url_parts.path.replace("/", "")
    name = name.replace(" ", "")

    name = os.path.join("tmp", name)
    extension = ''
    if name:
        async with aiofiles.open(name, 'wb') as f:
            await f.write(data)

        filename = name.split("/")[-1]
        extension = filename.split(".")[-1]
    text = ""
    async with aiohttp.ClientSession() as session:
        domain = f"{Tools.config.gpt_api}" if "302" not in Tools.config.gpt_api else f"{Tools.config.gpt_api}/jina/reader"
        headers = {}
        if "302" in Tools.config.gpt_api:
            headers = {
                "Authorization": Authorization
            }
        """
        2024-07-16 03:41:38.615 | MainThread | DEBUG | gpt : textract_view:558 -  test-dash.gpt302.com/jina/reader/https://www.npmjs.com/package/url-parse
        2024-07-16 03:41:38.615 | MainThread | DEBUG | gpt : textract_view:559 -  {'Authorization': 'Bearer sk-XYpOX05lzs7CNDhkJBvFvxt2apuViRDKZi05hawzF3ZPhcnL'}
        """



        Tools.log.debug(f"domain:{domain},url:{url}")

        async with session.get(f"{domain}/{url}", headers=headers) as resp:
            if resp.status == 200:
                text = await resp.text()
                text = text.split("Markdown Content:")[-1]
                text = text.strip()
        if kwargs.get('doc2x',True) and url.endswith("pdf") and not text:
            payload = aiohttp.FormData()
            payload.add_field('file', data, filename=name.split("/", 1)[-1],
                              content_type='application/octet-stream')
            payload.add_field("ocr", 'true')
            headers = {
                "Authorization": Authorization if Authorization.startswith("Bearer") else f"Bearer {Authorization}"
            }
            async with session.post(f"{Tools.config.gpt_api}/doc2x_v2/api/v2/parse/pdf", headers=headers,
                                    data=payload) as resp:
                b = b""
                text = ""
                async for chunk in resp.content.iter_chunks():
                    # 处理数据
                    Tools.log.debug(chunk)
                    if not chunk[1]:
                        b += chunk[0]
                        continue
                    b = chunk[0]
                    processor = StreamProcessor()
                    processor.process_stream(b.decode())
                    # 获取处理后的事件
                    events = processor.get_events()
                    events = events[0] if events else {}
                    if events.get("status") == 'success':
                        texts = events.get("data").get("pages")
                        text = '\n'.join([text.get('md') for text in texts])
                if not text:
                    b += chunk[0]

                    processor = StreamProcessor()
                    processor.process_stream(b.decode())
                    # 获取处理后的事件
                    events = processor.get_events()
                    events = events[0] if events else {}
                    if events.get("status") == 'success':
                        texts = events.get("data").get("pages")
                        text = '\n'.join([text.get('md') for text in texts])

    Tools.log.debug(f"extension:{extension}")
    Tools.log.debug(f"text:{text}")
    result = text.split("Markdown Content:")[-1]
    Tools.log.debug(f"result:{result},name:{name}")
    if not result and name:
        loop = asyncio.get_event_loop()
        if extension == 'md':
            result = await loop.run_in_executor(None, convert_markdown_to_html, name)
        elif extension in ('py', 'go', 'java', 'scala', 'c', 'h', 'xsl', 'php', 'cpp', 'js', 'html', 'css', 'cs'
                           , 'json', 'xml', 'yaml', 'yml', 'sh', 'bat', 'sql', 'txt', 'log', 'conf', 'ini', 'csv',
                           'tsv', 'ts', 'js'):
            encoding = kwargs.get('encoding','utf-8') or 'utf-8'
            result = data.decode(encoding)
        elif extension == 'rtf':
            result = rtf_to_text(data.decode())
        elif extension == "xlsx":
            import pandas as pd
            df = pd.read_excel(name)
            result = df.to_csv(index=False)
        else:
            try:
                result = await loop.run_in_executor(None, textract.process, name)
                result = result.decode('utf-8')
            except:
                try:
                    result = data.decode('utf-8')
                except:
                    ...
    try:
        os.remove(name)
    except:
        ...
    if model:
        _ = await TModel.filter(name=model).first().values("input_token")
        input_tokens = _.get("input_token", 0)
    else:
        _ = await TTokenMapping.filter(external_token_id__in=Subquery(
            TToken.filter(value=Authorization.split(" ")[-1]).values("id"))).select_related("model").first().values(
            "model__input_token")
        input_tokens = _.get("model__input_token")
    if input_tokens and input_tokens > 0:
        result = str(result)[0:int(input_tokens * 0.3)]

    Tools.log.debug(f"result:{result}")
    Tools.log.debug(f"result_len:{len(result)}")
    return result


async def sync_parameters():
    # 存储被修改的模型
    modified_models = []

    # 查询所有 t_model_param 数据
    model_params = await TModelParam.all()

    for param in model_params:
        model_name = param.model_name
        max_context_tokens = param.max_context_tokens
        max_output_tokens = param.max_output_tokens
        support_attachment_type = param.support_attachment_type

        # 计算 is_multimodal 值
        is_multimodal = 1 if len(support_attachment_type) > 1 or "text" not in support_attachment_type else 0

        services = await TService.filter(service_names__contains={"en": f"{model_name}"}).all()

        for service in services:
            # 检查是否有修改
            updated = False
            if service.input_token != max_context_tokens:
                service.input_token = max_context_tokens
                updated = True
            if service.output_size != max_output_tokens:
                service.output_size = max_output_tokens
                updated = True
            if service.is_multimodal != is_multimodal:
                service.is_multimodal = is_multimodal
                updated = True

            # 保存修改并记录模型
            if updated:
                await service.save()
                modified_models.append(model_name)
    
    result = {
        "modified_models": list(set(modified_models)),
        "total": len(modified_models)
    }

    # 去重并返回修改过的模型
    return result

async def update_tool_models(tool_id: str, model_id_list: list):
    # 获取所有相关的模型记录
    models = await TModel.filter(id__in=model_id_list).all()

    result = []
    for model in models:
        if isinstance(model.tool_id_list, str):  # 如果是字符串，反序列化
            tool_list = json.loads(model.tool_id_list)
        elif isinstance(model.tool_id_list, list):  # 如果已经是列表，直接使用
            tool_list = model.tool_id_list
        else:  # 如果既不是字符串也不是列表，处理默认值
            tool_list = []
        
        # 添加 tool_id，如果不在列表中
        if tool_id not in tool_list:
            tool_list.append(tool_id)
        
        # 更新字段
        model.tool_id_list = json.dumps(tool_list)
        await model.save()
        result.append({model.id: model.name})
        
    return result

async def get_robot_models():
    models = await TModel.filter(show_in_robot=True).order_by("-id").values(
    "id",
    "name",
    "show_name",
    "model_type",
    "en_model_type",
    "remark",
    "en_remark",
    "jp_remark"
    )
    return models

async def generate_unique_share_code():
    while True:
        share_code = random_string(6, uppercase=False)
        # 检查share_code是否已存在
        exists = await TTokenMapping.filter(share_code=share_code).exists()
        if not exists:
            return share_code
    

def model_to_dict(model) -> dict:
    """Convert custom model to dict with api_key from token mapping"""
    model_dict = dict(model)
    if model.token_mapping and model.token_mapping.external_token:
        model_dict['api_key'] = model.token_mapping.external_token.value
    return model_dict

@tortoise.transactions.atomic("gpt_conn")
async def create_custom_model(uid: int, model: CustomModelCreate):
    timestamp = current_timestamp()
    # 首先检查表中是否有自定义模型的供应商，如果没有则创建
    token_supplier = await TTokenSupplier.filter(name="自定义模型").first()
    if not token_supplier:
        supplier_pk = "sk-custommodel"

        token_supplier = await TTokenSupplier.create(
            name="自定义模型",
            target_url="https://custom-model.302ai.com",
            primary_key=supplier_pk,
            rate=1,
            time_weighted=1
            )
        # 同时在t_token表创建对应的数据
        supplier_t_token = await TToken.create(
            value=supplier_pk, 
            token_supplier_id=token_supplier.id
        )
        supplier_t_token_id = supplier_t_token.id
    else:
        supplier_t_token = await TToken.filter(token_supplier_id=token_supplier.id).first()
        supplier_t_token_id = supplier_t_token.id

    # 创建新的自定义模型时，检查api_key，如果是重复的，检查需要创建的自定义模型的uid是不是和已有数据重复，如果不重复，抛出错误
    custom_models = await TCustomModels.filter(api_key=model.api_key).exists()
    if custom_models:
        custom_model = await TCustomModels.filter(api_key=model.api_key).first()
        token_mapping_id = custom_model.token_mapping_id
        if custom_model.uid != uid:
            # 该api_key已被其他用户使用
            return fail_data(msg="API_KEY already exists and is not owned by the current user")
        # 如果uid/api_key/model_name都相同，则返回已有的自定义模型
        elif custom_model.uid == uid and custom_model.api_key == model.api_key and custom_model.model == model.model:
            return suc_data(msg="Custom model already exists.")

    # 新增t_token/t_token_mapping记录   
    token = create_token()
    t_token = await TToken.create(value=token, token_supplier_id=token_supplier.id)
    t_token_mapping = await TTokenMapping.create(
        created_on=timestamp,
        updated_on=timestamp,
        deleted_on=0,
        status=1,
        enable=1,
        expired_on=0,
        limit_cost=0,
        current_cost=0,
        user_id=uid,
        name='',
        remark='',
        cost_log_id=0,
        external_token_id=t_token.id,
        internal_token_id=supplier_t_token_id,
        share_code=await generate_unique_share_code(),
        is_robot=1,
        model_id=0,
        gpts_code='',
        tool_id=-4,
        enable_plugin=1,
        save_log=0,
        webhook='',
        log_enalbe_st=timestamp,
        )
    token_mapping_id = t_token_mapping.id

    token_mapping = await TTokenMapping.get(id=token_mapping_id)
    # 创建t_token_info记录
    await TTokenInfo.create(
        token=token_mapping,
        tz="Asia/Shanghai",
        limit_daily_cost=0,
        current_date_cost=0,
        limit_monthly_cost=0,
        current_month_cost=0,
        limit_hour_cost=0,
        current_hour_cost=0,
        external_code=random_string(4, uppercase=False, lowercase=False),
        settings={},
        use_gpts=0,
        open_tts=0,
        extra={},
        conf_version="1.0.0"
    )

    custom_model = await TCustomModels.create(
        uid=uid,
        token_mapping_id=token_mapping_id,
        created_on=timestamp,
        updated_on=timestamp,
        **model.model_dump()
    )
    custom_model = await TCustomModels.filter(
        id=custom_model.id
    ).prefetch_related(
        'token_mapping__external_token'
    ).first()
    return suc_data(data=model_to_dict(custom_model))

async def get_custom_models(uid: int):
    models = await TCustomModels.filter(
        uid=uid, 
        deleted_on=0
    ).prefetch_related(
        'token_mapping__external_token'
    ).all()
    
    result = []
    for model in models:
        model_dict = model_to_dict(model)
        result.append(model_dict)
    
    return suc_data(data=result)

async def get_custom_model(uid: int, model_id: int):
    model = await TCustomModels.filter(
        id=model_id, uid=uid, deleted_on=0
    ).prefetch_related(
        'token_mapping__external_token'
    ).first()
    if not model:
        return fail_data(msg="Model not found")
    model_dict = model_to_dict(model)
    return suc_data(data=model_dict)

@tortoise.transactions.atomic("gpt_conn")
async def update_custom_model(uid: int, model_id: int, model: CustomModelUpdate):
    timestamp = current_timestamp()
    
    db_model = await TCustomModels.filter(
        id=model_id, 
        uid=uid, 
        deleted_on=0
    ).prefetch_related(
        'token_mapping',
        'token_mapping__external_token'
    ).first()
    
    if not db_model:
        return fail_data(msg="Model not found")

    update_data = {k: v for k, v in model.model_dump(exclude_unset=True).items() if v is not None}
    
    # 更新 custom_model
    update_data["updated_on"] = timestamp
    await db_model.update_from_dict(update_data).save()
    
    updated_model = await TCustomModels.filter(
        id=model_id
    ).prefetch_related(
        'token_mapping__external_token'
    ).first()
    updated_model_dict = model_to_dict(updated_model)
    return suc_data(data=updated_model_dict)

@tortoise.transactions.atomic("gpt_conn")
async def delete_custom_model(uid: int, model_id: int):
    # 预加载关联数据
    model = await TCustomModels.filter(
        id=model_id, 
        uid=uid, 
        deleted_on=0
    ).prefetch_related(
        'token_mapping',
        'token_mapping__external_token'
    ).first()
    
    if not model:
        return fail_data(msg="Model not found")

    timestamp = current_timestamp()
    
    # 删除 custom_model
    await model.update_from_dict({
        "deleted_on": timestamp,
        "updated_on": timestamp
    }).save()
    
    # 删除关联的 token_mapping
    if model.token_mapping:
        await model.token_mapping.update_from_dict({
            "deleted_on": timestamp,
            "updated_on": timestamp
        }).save()
        
        # 删除关联的 token
        if model.token_mapping.external_token:
            await model.token_mapping.external_token.update_from_dict({
            "deleted_on": timestamp,
            "updated_on": timestamp
        }).save()
    model_dict = model_to_dict(model)
    return suc_data(data=model_dict)