# -*- coding: utf-8 -*-
# @Time    : 2023/8/24 14:23
# <AUTHOR> hzx1994
# @File    : response.py
# @Software: PyCharm
# @description:
from typing import Union

from pydantic import BaseModel
from enum import Enum


class StateCode(int, Enum):
    SUCCESS = 0  # 成功
    FAIL = -1  # 通用失败
    CallFrequently = 101
    AuthFail = 5001  # 登录异常

    SignInError = 6002


    ApiKeyLimit = 601 # key limit 10
    HttpCodeError = 602 # http code error
    RestPasswordError = 603 # 用户重置密码异常
    PW_NOT_MATCH = 604 # password not match
    UserExistError = 605 # user is exist
    LINK_EXPIRED = 603
    TEST_USER = 610 # 测试用户需要注册登录
    PHONE_IS_BAND = 606 #  手机号已被绑定

    USER_EXIST = 2000  # 测试用户需要注册登录
    USER_NOT_EXIST = 2001  # 邮箱或者密码错误
    Email_Not_Exist = 2002  # 邮箱不存在
    USER_NEED_ACTIVE = 2100  # 用户需要激活

    ConnectionError = 703 # connection error
    ConnectionTimeOut = 704 # connection error


    GenerationError = 801
    CaptchaError = 900 # 验证码错误
    CaptchaLimitError = 901 # 验证码已超过调用次数

    Pay_Qr_Time_Out = 1200  # 二维码超时

    ShareCodeExists = 9000
    XxNameExists = 9000
    ShareCodeNotFound = 9001
    LinkError = 9100

    AppFeiShuExist = 10001
    AppDingTalkExist = 10002









class BaseData(BaseModel):
    code: int = StateCode.SUCCESS.value
    msg: str = "success"
    data: Union[dict,list] = {}


def suc_data(data=None, code=StateCode.SUCCESS.value, msg="success"):
    if data is None:
        data = {}
    return BaseData(code=code,msg=msg,data=data)

def fail_data(data=None, code=StateCode.FAIL.value, msg="fail"):
    if data is None:
        data = {}
    return BaseData(code=code,msg=msg,data=data)


