# -*- coding: utf-8 -*-
# @Time    : 2023/9/5 10:48
# <AUTHOR> hzx1994
# @File    : Log.py
# @Software: PyCharm
# @description:
import logging
from types import FrameType
from typing import cast
from loguru import logger
import sys

class InterceptHandler(logging.Handler):
    def emit(self, record: logging.LogRecord) -> None:  # pragma: no cover
        # Get corresponding Loguru level if it exists
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = str(record.levelno)

        # Find caller from where originated the logged message
        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:  # noqa: WPS609
            frame = cast(FrameType, frame.f_back)
            depth += 1

        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage(),
        )


def get_log_conf(LOGGING_LEVEL,log_file_path,err_log_file_path):
    loguru_config = {
        "handlers": [
            {
                "sink": sys.stdout,
                "level": LOGGING_LEVEL,
                "format": "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | {thread.name} | <level>{level}</level> | "
                       "<cyan>{module}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
            },
            {
                "sink": log_file_path,
                "level": LOGGING_LEVEL,
                "rotation": "10 MB",
                "retention": "3 day",
                "enqueue": True,
                "encoding": 'utf-8',
                "format":"{time:YYYY-MM-DD HH:mm:ss.SSS} | {thread.name} | {level} | {module} : {function}:{line} -  {message}"
            },
            {
                "sink": err_log_file_path,
                "serialize": True,
                "level": 'ERROR',
                "retention": "3 day",
                "rotation": "10 MB",
                "enqueue": True,
                "encoding": 'utf-8',
                "format":"{time:YYYY-MM-DD HH:mm:ss.SSS} | {thread.name} | {level} | {module} : {function}:{line} -  {message}"
            },
        ],
    }
    return loguru_config


def init_logger(level,log_path,err_path):
    LOGGER_NAMES = ("uvicorn","uvicorn.access","tortoise")
    log = logging.getLogger("tortoise")
    log.propagate = False
    log.level = level

    for logger_name in LOGGER_NAMES:
        logging_logger = logging.getLogger(logger_name)
        logging_logger.handlers = [InterceptHandler()]
    logger.configure(**get_log_conf(level,log_path,err_path))
