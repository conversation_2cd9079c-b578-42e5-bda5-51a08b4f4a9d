<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>302.AI - {{账单}}</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
  <style>
    .font-regular {
      font-family: "Poppins", sans-serif;
      font-weight: 400;
      font-style: normal;
    }

    .font-medium {
      font-family: "Poppins", sans-serif;
      font-weight: 500;
      font-style: normal;
    }

    .font-semibold {
      font-family: "Poppins", sans-serif;
      font-weight: 600;
      font-style: normal;
    }

    .font-bold {
      font-family: "Poppins", sans-serif;
      font-weight: 700;
      font-style: normal;
    }
  </style>
  <style>
    body {
      font-family: "Poppins", sans-serif;
      margin: 0;
      font-weight: 400;
      font-style: normal;
      padding: 0px;
      --theme-color: #8E47F0;
      --black-100: #052049;
      --black-200: #041F48;
      --text-base: 14px;
      --text-lg: 16px;
      --text-xl: 18px;
      --text-2xl: 26px;
      --text-3xl: 36px;
      font-size: var(--text-base);
      color: var(--black-100);
    }

    .invoice-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
    }

    .logo-section {
      display: flex;
      flex-direction: column;
    }

    .logo {
      margin-right: 10px;
    }

    .logo img {
      width: 156px;
    }

    .company-address {
      font-size: var(--text-base);
    }

    .invoice-title {
      font-size: var(--text-3xl);
      color: #3F3FAA;
      text-align: right;
      letter-spacing: 2px;
    }

    .invoice-details {
      text-align: right;
      color: var(--theme-color);
      font-size: var(--text-lg);
    }

    .invoice-details span {
      font-weight: normal;
      font-size: var(--text-base);
      color: #333;
    }

    .bill-info {
      margin-bottom: 30px;
    }

    .bill-info h3 {
      color: var(--theme-color);
      margin-bottom: 2px;
      font-size: var(--text-lg);
    }

    .bill-section {
      margin-bottom: 20px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 50px 0 100px 0;
      font-size: var(--text-xl);
    }

    th {
      text-align: left;
      padding: 10px 0;
      border-bottom: 1px solid #ddd;
      color: var(--theme-color);
    }

    td {
      padding: 15px 0;
      border-bottom: 1px solid #ddd;
    }

    .amount {
      color: var(--theme-color);
    }

    .fees {
      font-size: 14px;
      /* font-style: italic; */
    }

    .total-amount {
      font-size: var(--text-2xl);
    }

    .text-right {
      text-align: right;
    }

    .bank-info {
      position: relative;
      margin-top: 50px;
      margin-bottom: 50px;
    }

    .bank-info p {
      margin: 5px 0;
      font-size: var(--text-base);
      font-weight: 500;
      max-width: 50%;
    }

    .bank-info span {
      margin-left: 3px;
      font-weight: 400;
    }

    .bank-download {
      position: absolute;
      right: 10%;
      bottom: 10%;
      border: 1px solid var(--theme-color);
      border-radius: 5px;
      padding: 10px 20px;
      color: var(--theme-color);
      cursor: pointer;
    }

    .support {
      text-align: center;
      color: #666;
      margin-top: 50px;
      border-top: 1px solid #eee;
      padding-top: 20px;
    }

    @media (max-width: 768px) {
      body {
        --text-base: 12px;
        --text-lg: 14px;
        --text-xl: 16px;
        --text-2xl: 24px;
        --text-3xl: 32px;
      }

      .logo img {
        width: 130px;
      }

      table {
        margin: 20px 0 50px 0;
      }

      .bank-info p {
        max-width: 100%;
      }

      .bank-download {
        right: 3%;
        bottom: 0;
        transform: translateY(30px);
      }
    }
  </style>
</head>

<body>
  <div class="invoice-container">
    <div class="header">
      <div class="logo-section">
        <div class="logo">
          <img src="data:image/png;base64,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" alt="">
        </div>
        <div>
          <div class="company-address">Univerads Technology Limited, HONG KONG</div>
        </div>
      </div>
      <div>
        <div class="invoice-title font-regular">{{账单}}</div>
        <div class="invoice-details font-bold">
          <div>{{账单编号}}: <span>{{order_id}}</span></div>
          <div>{{账单日期}}: <span>{{order_created_on}}</span></div>
        </div>
      </div>
    </div>

    <div class="bill-info">
      <div class="bill-section">
        <h3>{{开票给}}:</h3>
        <div>{{pay_email}}</div>
      </div>

      <div class="bill-section">
        <h3>{{支付给}}:</h3>
        <div>Univerads Technology Limited</div>
        <div>FLAT A112, 1/F, LEE KA INDUSTRIAL BUILDING, 8 NG FONG STREET, SAN PO KONG, KL</div>
      </div>
    </div>

    <table>
      <thead>
        <tr>
          <th style="width: 60%;">{{描述}}</th>
          <th style="width: 20%;">{{金额}}</th>
          <th style="width: 20%;"><!-- 占位符 --></th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{order_note}}</td>
          <td>${{order_price}}</td>
          <td><!-- 占位符 --></td>
        </tr>
        <tr>
          <td><!-- 占位符 --></td>
          <td class="font-bold amount">{{总计}}</td>
          <td class="total-amount font-bold text-right">${{order_total}}</td>
        </tr>
        <tr>
          <td><!-- 占位符 --></td>
          <td><!-- 占位符 --></td>
          <td class="fees text-right">{{手续费}}: ${{order_service_fee}}</td>
        </tr>
      </tbody>
    </table>

    <div class="bank-info">
      <p>Account no: <span>*********</span></p>
      <p>Bank Name: <span>DBS Bank (Hong Kong) Limited</span></p>
      <p>Bank Address: <span>G/F, The Center,99 Queen's Road Central, Central, Hong Kong</span></p>
      <p>Swift: <span>DHBKHKHHXXX</span></p>

      <div id="downloadBtn" class="bank-download" onclick="downloadPDF()">
        DOWNLOAD
      </div>
    </div>

    <div class="support">
      <EMAIL>
    </div>
  </div>

  <script>
    function downloadPDF() {
      const downloadBtn = document.getElementById('downloadBtn');
      downloadBtn.style.display = 'none';  // 隐藏按钮

      const element = document.querySelector('.invoice-container');
      const opt = {
        margin: 10,
        filename: '302ai-bill.pdf',
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      };

      html2pdf().set(opt).from(element).save().then(() => {
        downloadBtn.style.display = 'block';  // 生成完成后显示按钮
      });
    }
  </script>
</body>

</html>