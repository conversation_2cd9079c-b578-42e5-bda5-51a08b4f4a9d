name: Publish Docker image

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

on:
  push:
    branches: ["main", "dev", "proxy", "proxy-dev"]
    # Publish semver tags as releases.
    tags: ["v*.*.*"]
  pull_request:
    branches: ["main", "dev", "proxy", "proxy-dev"]

env:
  # Use docker.io for Docker Hub if empty
  REGISTRY: docker.io
  # github.repository as <account>/<repo>
  IMAGE_NAME: ${{ github.repository }}

  

jobs:
  push_to_registry:
    name: Push Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repo
        uses: actions/checkout@v3
      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Set image name based on branch
        id: set-image
        run: |
          if [[ "${{ github.ref_name }}" == "proxy" || "${{ github.ref_name }}" == "proxy-dev" ]]; then
            echo "IMAGE_NAME=proxy302/proxy302" >> $GITHUB_ENV
          else
            echo "IMAGE_NAME=proxy302/ai_backend" >> $GITHUB_ENV
          fi

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            # type=raw,value=latest
            type=ref,event=tag
            type=ref,event=branch
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Build and push Docker image for main branch
        uses: docker/build-push-action@v4
        with:
          context: .
          file: Dockerfile
          platforms: linux/amd64
          push: true
          tags: |
            ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # 6. 设置kubectl工具（需要kubectl已安装在GitHub Runner上）
      - name: Set up kubectl
        if: github.ref_name == 'dev'
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBECONFIG_DEPLOY }}" > $HOME/.kube/config

      # 7. 更新Kubernetes中的Deployment
      - name: Deploy to Kubernetes
        if: github.ref_name == 'dev'
        run: |
          kubectl rollout restart deployment/ai-backend

      # 6. 设置kubectl工具（需要kubectl已安装在GitHub Runner上）
      - name: Set up kubectl
        if: github.ref_name == 'main'
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBECONFIG_PROD }}" > $HOME/.kube/config

      # 7. 更新Kubernetes中的Deployment
      - name: Deploy to Kubernetes
        if: github.ref_name == 'main'
        run: |
          kubectl rollout restart deployment/ai-backend



    # 6. 设置kubectl工具（需要kubectl已安装在GitHub Runner上）
      - name: Set up kubectl
        if: github.ref_name == 'proxy-dev'
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBECONFIG_DEPLOY }}" > $HOME/.kube/config

      # 7. 更新Kubernetes中的Deployment
      - name: Deploy to Kubernetes
        if: github.ref_name == 'proxy-dev'
        run: |
          kubectl rollout restart deployment/application-proxy-public

      # 6. 设置kubectl工具（需要kubectl已安装在GitHub Runner上）
      - name: Set up kubectl
        if: github.ref_name == 'proxy'
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBECONFIG_PROD }}" > $HOME/.kube/config

      # 7. 更新Kubernetes中的Deployment
      - name: Deploy to Kubernetes
        if: github.ref_name == 'proxy'
        run: |
          kubectl rollout restart deployment/application-proxy-public
