

def create_secret(params:dict,key:str):
    import hashlib
    
    # 获取参数
    user_id = str(params.get('user_id', ''))
    money = "{:.2f}".format(float(params.get('money', 0)))
    withdrawal_id = str(params.get('withdrawal_id', ''))
    
    # 密钥
    
    # 生成签名字符串
    print(user_id,money,withdrawal_id,key)
    sign_str = user_id + money + withdrawal_id + key
    
    # 计算SHA256哈希
    sha256 = hashlib.sha256()
    sha256.update(sign_str.encode('utf-8'))
    calculated_secret = sha256.hexdigest()
    return calculated_secret

def check_secret(secret, params,secret_key):
    """
    验证授权码是否正确
    
    Args:
        secret: 授权码
        params: 包含用户ID、金额、提现ID的参数字典
    
    Returns:
        bool: 授权码验证是否通过
    """
    calculated_secret = create_secret(params=params,key=secret_key)
    return secret == calculated_secret

def demo(is_gpt=False):
    import requests
    if is_gpt:
        url = "https://dash-api.302.ai/api/charge/invite"
    else:
        url = "https://dash-api.proxy302.com/api/charge/invite"
    data = {
        "user_id": "18388",
        "money": "10.00",
        "withdrawal_id": 'demo',
        "status": 1
    }
    if is_gpt:
        secret_key = "r2uXvHHTGUrgcldES5aSDeY2ckNV0Y9R"
    else:
        secret_key = "rCaZSNOzyvIYN0kqVYRV8xFCm1uG-YYb"
    secret = create_secret(params=data,key=secret_key)
    data["secret"] = secret
    print(data)
    response = requests.get(url, params=data)
    return response.text
    

if __name__ == "__main__":
    data = demo(False)
    print(data)

