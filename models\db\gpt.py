# -*- coding: utf-8 -*-
# @Time    : 2024/1/25 16:17
# <AUTHOR> hzx1994
# @File    : gpt.py
# @Software: PyCharm
# @description:
import datetime
import time
from typing import List

from tortoise import Model, fields


class Base(Model):
    class Meta:
        abstract = True

    created_on = fields.IntField(null=True, description="创建时间", default=time.time)
    modified_on = fields.IntField(null=True, description="修改时间", default=time.time)
    deleted_on: int = fields.IntField(null=True, description="删除时间", default=0)

    def to_dict(self):
        return self.__dict__


class TModel(Model):
    class Meta:
        table='t_models'
    name:str =fields.CharField(max_length=512, null=True, description="名称", default="")
    show_name:str =fields.CharField(max_length=512, null=True, description="名称", default="")
    show_in_robot:bool = fields.BooleanField(default=True,description="是否在机器人中显示")
    show_in_api:bool = fields.BooleanField(default=True,description="是否在api中显示")
    remark:str=fields.Char<PERSON>ield(max_length=512, null=True, description="备注，补充说明", default="")
    en_remark:str=fields.CharField(max_length=512, null=True, description="英文备注，补充说明", default="")
    jp_remark:str=fields.CharField(max_length=512, null=True, description="ri文备注，补充说明", default="")
    is_default:bool = fields.BooleanField(default=False,description="是否默认")
    is_cn_default:bool = fields.BooleanField(default=False,description="是否默认")
    ord:int=fields.IntField(default=0,description="排序")
    token_supplier_id:int=fields.IntField(null=False, description="供应商id", default=0)
    token_supplier_id_list:str=fields.CharField(max_length=512,null=False,source_field='token_supplier_list', description="供应商id", default=0)
    tool_id_list:List[int]=fields.JSONField(null=True, description="工具id列表", default=[])
    model_type:str = fields.CharField(max_length=512, null=True, description="模型类型", default="")
    en_model_type:str =  fields.CharField(max_length=512, null=True, description="英文模型类型", default="")
    file_support_type:int = fields.IntField(null=False, description="文件支持类型", default=0)
    is_support_plugins:int = fields.IntField(null=False, description="插件支持", default=0)
    region_support_list:list = fields.JSONField(null=True, description="区域支持列表", default=[0,1])
    input_token:int = fields.IntField(null=False, description="", default=0)
    output_token:int = fields.IntField(null=False, description="", default=0)
    max_token:int = fields.IntField(null=False, description="", default=0)
    base_rate:float=fields.FloatField(null=False, description="", default=1)
    is_embedding:int = fields.IntField(null=False, description="是否支持embedding", default=0)
    dimension:int = fields.IntField(null=False, description="", default=0)


class TAiAppBox(Model):
    class Meta:
        table="t_ai_app_box"

    id = fields.IntField(pk=True, description="主键", auto=True)
    model = fields.ForeignKeyField("gpt.TModel", related_name="models", description="模型id", null=True,db_constraint=False)
    tool = fields.ForeignKeyField("gpt.TGptTool", related_name="tools", description="工具id", null=True,db_constraint=False)
    token = fields.ForeignKeyField("gpt.TToken", related_name="tokens", description="令牌id", null=True,db_constraint=False)
    extra:dict = fields.JSONField(null=True, description="额外信息", default={})



class TTokenSupplier(Base):
    class Meta:
        table="t_token_supplier"

    id = fields.IntField(pk=True, description="主键", auto=True)
    name = fields.CharField(max_length=512, null=True, description="名称", default="")
    target_url = fields.CharField(max_length=512, null=True, description="目标地址", default="")
    primary_key = fields.CharField(max_length=512, unique=True, null=False, default="")
    rate:float = fields.FloatField(default=1,description="汇率")


class TTokenMapping(Base):
    class Meta:
        table="t_token_mapping"

    id = fields.IntField(pk=True, description="主键", auto=True)
    external_token = fields.ForeignKeyField("gpt.TToken", related_name="external_tokens", description="外部代币ID", null=True,db_constraint=False)
    internal_token = fields.ForeignKeyField("gpt.TToken", related_name="internal_tokens", description="内部代币ID", null=True,db_constraint=False)
    status = fields.IntField(null=True, description="状态", default=0)
    tool = fields.ForeignKeyField("gpt.TGptTool",null=True, description="工具id", default=0)
    enable_plugins = fields.IntField(null=True, description="插件开关", default=0)
    enable = fields.IntField(null=True, description="开启状态", default=1)
    save_log = fields.IntField(null=True, description="开启日志记录状态", default=0)
    log_enable_st = fields.IntField(null=True, description="开启的时间", default=time.time)
    expired_on = fields.IntField(null=True, description="过期时间", default=0)
    limit_cost = fields.IntField(null=True, description="限额", default=0)
    current_cost = fields.IntField(null=True, description="已用额度", default=0)
    user = fields.ForeignKeyField("models.TUser", related_name="mappings", description="用户id",source_field="uid", null=True)
    # uid = fields.IntField(null=True, description="proxy库的用户id", default=0)
    name = fields.CharField(max_length=255, null=True, description="令牌名", default="")
    share_code = fields.CharField(max_length=255, null=True, description="分享code", default="")
    remark = fields.CharField(max_length=255, null=True, description="备注", default="")
    cost_log_id = fields.IntField(null=True, description="最近一次扣费的日志id", default=0)
    is_robot = fields.IntField(null=True, description="是否api类型的mapping", default=1)
    model:int=fields.ForeignKeyField("gpt.TModel", related_name="tokens", description="模型", null=True,db_constraint=False)
    gpts_code:str=fields.CharField(max_length=255, null=True, description="code", default="")
    custom_models = fields.ReverseRelation["TCustomModels"]


class TRefundLog(Base):
    class Meta:
        table = "t_refund_log"
    id = fields.IntField(pk=True, description="id")
    uid = fields.IntField(description="用户id",default=0)
    refund_amount = fields.FloatField(description="退款金额",default=0)
    log_id = fields.IntField(description="日志id",default=0)
    refund_source = fields.CharField(max_length=255, description="退款来源",default="")
    refund_source_log_id = fields.IntField(description="退款来源日志id",default=0)
    status = fields.IntField(description="状态",default=0)

class TOpenaiGpts(Base):
    class Meta:
        table="t_openai_gpts"
    id:int=fields.IntField(pk=True, description="主键", auto=True)
    gizmo_id:str=fields.CharField(max_length=64,description="gizmo_id")
    profile_picture_url:str=fields.CharField(max_length=256,description="头像")
    display_name:str=fields.CharField(max_length=256,description="名称")
    display_description:str=fields.CharField(max_length=256,description="描述")
    prompt_starters:str=fields.CharField(max_length=512,description="描述")
    openai_author:int = fields.ForeignKeyField('gpt.TOpenaiAuthor',related_name='openai_author_id',description='作者id')
    blob_img_url:str=fields.CharField(max_length=256,description="blob_img_url",default="")

class TOpenaiAuthor(Base):
    class Meta:
        table='t_openai_author'

    id:int=fields.IntField(pk=True, description="主键", auto=True)
    display_name:str=fields.CharField(max_length=512,description="用户名")

class TTokenInfo(Base):
    class Meta:
        table="t_token_info"

    id = fields.IntField(pk=True, description="主键", auto=True)
    token = fields.ForeignKeyField("gpt.TTokenMapping", null=True, description="内部token id", related_name="info",db_constraint=False)
    tz = fields.CharField(max_length=255, null=True, description="时区", default="Asia/Shanghai")
    limit_daily_cost = fields.IntField(null=True, description="每日额度", default=0)
    current_date_cost = fields.IntField(null=True, description="当日额度", default=0)
    limit_monthly_cost = fields.IntField(null=True, description="每月额度", default=0)
    current_month_cost = fields.IntField(null=True, description="当月额度", default=0)
    limit_hour_cost = fields.IntField(null=True, description="每小时额度", default=0)
    current_hour_cost = fields.IntField(null=True, description="当前小时额度", default=0)
    external_code = fields.CharField(max_length=255, null=True, description="分享码", default="")
    settings:dict=fields.JSONField(null=False, description="额外设置", default="{}")
    use_gpts:int=fields.IntField(default=0,description="使用gpts机器人商城")
    open_tts:int=fields.IntField(default=0,description="tts开关")
    extra:dict = fields.JSONField(null=False, description="额外设置", default="{}")
    conf_version:str=fields.CharField(max_length=255, null=True, description="配置版本", default="1.0.0")

"""
CREATE TABLE `t_ip_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '唯一标识符，自增',
  `list_type` enum('BLACKLIST','WHITELIST') CHARACTER SET utf8 NOT NULL DEFAULT 'BLACKLIST' COMMENT '区分黑名单和白名单',
  `ip_type` enum('SINGLE','SUBNET','RANGE') CHARACTER SET utf8 NOT NULL DEFAULT 'RANGE' COMMENT 'IP类型，单个IP、子网或IP范围',
  `ip_address` varchar(45) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '单个IP地址',
  `ip_subnet` varchar(45) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '子网地址',
  `ip_range_start` varchar(45) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'IP范围起始地址',
  `ip_range_end` varchar(45) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'IP范围结束地址',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，表示该条目是否有效',
  `created_on` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间，默认当前时间',
  `modified_on` int(11) NOT NULL DEFAULT '0',
  `deleted_on` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=latin1 COMMENT='IP黑白名单表，包含单个IP、子网和IP范围';
"""

class TIpList(Base):
    class Meta:
        table="t_ip_list"
    id: int=fields.IntField(pk=True, description="唯一标识符，自增")
    list_type:str=fields.CharField(max_length=255, description="黑名单还是白名单", default="BLACKLIST")
    ip_type:str=fields.CharField(max_length=255, description="IP类型，单个IP、子网或IP范围", default="RANGE")
    ip_address:str=fields.CharField(max_length=255, description="单个IP地址", default="")
    ip_subnet:str=fields.CharField(max_length=255, description="子网地址", default="")
    ip_range_start:str=fields.CharField(max_length=255, description="IP范围起始地址", default="")
    ip_range_end:str=fields.CharField(max_length=255, description="IP范围结束地址", default="")
    status:int=fields.IntField(null=True, description="状态，表示该条目是否有效", default=1)
    limit = fields.ForeignKeyField('gpt.TApiLimit', related_name='ip_lists', description="限制表对应的 t_api_limit.id",)


class TApiLimit(Base):
    class Meta:
        table="t_api_limit"

    id: int = fields.IntField(pk=True, description="唯一标识符，自增")
    token_id:int=fields.IntField(null=True, description="对应的mapping id")
    limit_balance:float=fields.FloatField(null=True, description="限额")
    model_id_list:List[int]=fields.JSONField(null=True, description="限制能使用的模型id")
    remark:str=fields.CharField(max_length=255, null=True, description="备注")
    uid:int=fields.IntField(null=True, description="用户id")
    status:int=fields.IntField(null=True, description="状态",default=1)



class TToken(Base):
    class Meta:
        table="t_token"

    id = fields.IntField(pk=True, description="主键", auto=True)
    value = fields.CharField(max_length=512, null=True, description="代币值", default="")
    token_supplier = fields.ForeignKeyField("gpt.TTokenSupplier", related_name="tokens", description="模型", null=True,db_constraint=False)



class TDataStatistics(Model):
    str_date:str=fields.CharField(max_length=255, description="日期 天，可以空字符")
    str_month:str=fields.CharField(max_length=255, description="日期，月份 可以空字符")
    uid:int=fields.IntField(null=True, description="用户id，可以给0",default=0)
    key_name:str=fields.CharField(max_length=255, description="动态键名")
    value_field:float=fields.FloatField(null=True, description="值")
    data_type:int=fields.IntField(null=True, description="0 指定用户id的sharecode数据")
    class Meta:
        table="t_data_statistics"


class TModelPrice(Base):
    class Meta:
        table="t_model_price"

    id = fields.IntField(pk=True, description="主键", auto=True)
    model = fields.CharField(max_length=64, null=True, description="模型", default="")
    token_count = fields.IntField(null=True, description="代币数量，表示服务的使用量", default=0)
    input_price = fields.FloatField(null=True, description="输入价格", default=0)
    output_price = fields.FloatField(null=True, description="输出价格", default=0)
    resolution = fields.CharField(max_length=64, null=True, description="对于图像相关的服务，表示图像分辨率", default="")
    description = fields.CharField(max_length=256, null=True, description="", default="")
    rate = fields.FloatField(null=True, description="比率", default=1)



class TLog(Base):
    class Meta:
        table="t_log"

    id = fields.IntField(pk=True, description="主键", auto=True)
    token_mapping = fields.ForeignKeyField("gpt.TTokenMapping", null=True, description="token映射", related_name="logs1",db_constraint=False)
    external_token = fields.ForeignKeyField("gpt.TToken", null=True, description="外部token id", related_name="logs2",db_constraint=False)
    internal_token = fields.ForeignKeyField("gpt.TToken", null=True, description="内部token id", related_name="logs3",db_constraint=False)
    process_time = fields.IntField(null=True, description="处理时间", default=0)
    prompt_token = fields.IntField(null=True, description="提示token", default=0)
    completion_token = fields.IntField(null=True, description="完成token", default=0)
    model = fields.CharField(max_length=255,null=True, description="模型", default="")
    input_cost = fields.FloatField(null=True, description="输入成本", default=0)
    output_cost = fields.FloatField(null=True, description="输出成本", default=0)
    total_cost = fields.FloatField(null=True, description="总成本", default=0)




class TSnycLog(Base):
    class Meta:
        table="t_gpt_snyc_log"

    id = fields.IntField(pk=True, description="主键", auto=True)
    share_code:str=fields.CharField(max_length=255, description="分享码")
    sync_pwd:str=fields.CharField(max_length=255, description="同步密码")
    timestamp:int=fields.IntField(description="客户端对应的时间戳 毫秒级")
    log_name:str=fields.CharField(max_length=255, description="随机生成的名 {share_code}-{随机字符串}-{时间戳}")
    device:str=fields.CharField(max_length=255, description="同步的设备名")
    log_url:str=fields.CharField(max_length=255, description="上传的log文件地址")


class TGptTool(Base):
    class Meta:
        table = "t_gpt302_tool"

    id = fields.IntField(pk=True)
    en_name = fields.CharField(null=True, description="英文名称", default="",max_length=512)
    jp_name = fields.CharField(null=True, description="英文名称", default="",max_length=512)
    name = fields.CharField(null=True, description="名称", default="",max_length=512)
    en_description = fields.CharField(null=True, description="英文描述", default="",max_length=512)
    jp_description = fields.CharField(null=True, description="英文描述", default="",max_length=512)
    description = fields.CharField(null=True, description="描述", default="",max_length=512)
    search_description = fields.TextField(null=True, description="描述", default="")
    domain = fields.CharField(null=True, description="领域", default="tools302.com",max_length=512)
    cn_domain = fields.CharField(null=True, description="领域", default="302ai.cn",max_length=512)
    logo_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    zh_tool_logo_url = fields.CharField(null=True, description="logo", default="",max_length=512,source_field='cn_tool_logo_url')
    en_logo_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    jp_logo_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    en_tool_logo_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    jp_tool_logo_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    tool_logo_video_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    en_tool_logo_video_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    jp_tool_logo_video_url = fields.CharField(null=True, description="logo", default="",max_length=512)
    prefix = fields.CharField(null=True, description="项目前缀", default="",max_length=512)
    cate_id:int=fields.IntField(null=True, description="分类id", default=0)
    extra:dict=fields.JSONField(null=True, description="额外数据", default={})
    ord:int=fields.IntField(null=True, description="排序", default=0)
    created_time:str=fields.CharField(null=True, description="工具的创建时间", default="",max_length=225)
    open_source_url:str=fields.CharField(null=True, description="开源地址", default="",max_length=512)
    open_source:bool = fields.BooleanField(null=True, description="是否开源", default=False)


class TToolCategory(Model):
    class Meta:
        table = "t_tool_category"

    id = fields.IntField(pk=True)
    cn_cate_name = fields.CharField(max_length=255)
    en_cate_name = fields.CharField(max_length=255)
    jp_cate_name = fields.CharField(max_length=255)


class TRobotMapping(Base):
    class Meta:
        table = "t_robot_mapping"
    id = fields.IntField(pk=True, description="主键", auto=True)
    # token_id = fields.IntField(null=True, description="t_mappint_token.id", default=0)
    token = fields.ForeignKeyField("gpt.TTokenMapping", null=True, description="TTokenMapping id", related_name="robots",
                                            db_constraint=False,default=0)
    feishu_app_id = fields.CharField(max_length=256, null=True, description="", default="")
    feishu_app_secret = fields.CharField(max_length=256, null=True, description="", default="")
    feishu_verification_token = fields.CharField(max_length=256, null=True, description="", default="")
    feishu_encrypt_key = fields.CharField(max_length=128, null=True, description="", default="")
    feishu_bot_name = fields.CharField(max_length=64, null=True, description="", default="")
    dingtalk_client_id = fields.CharField(max_length=256, null=True, description="", default="")
    dingtalk_client_secret = fields.CharField(max_length=256, null=True, description="", default="")
    mode = fields.IntField(null=True, description="1 http 2 stream", default=1)
    dingtalk_aes_key = fields.CharField(max_length=256, null=True, description="", default="")
    dingtalk_token = fields.CharField(max_length=256, null=True, description="", default="")
    dingtalk_webhook = fields.CharField(max_length=32, null=True, description="", default="")




class TKBFiles(Model):
    class Meta:
        table = "t_kb_files"

    id = fields.IntField(pk=True, description="主键", auto=True)
    file_name = fields.TextField(null=True, description="文件名", default="")
    blob_url = fields.TextField( null=True, description="文件名", default="")
    kb_id = fields.IntField(null=True, description="知识库id", default=0)


class TKnowledgeFile(Model):
    class Meta:
        table = "t_kb_knowledge_file"

    id = fields.IntField(pk=True, description="主键", auto=True)
    file_name = fields.TextField(null=True, description="文件名", default="")
    file_ext = fields.CharField(max_length=10, null=True, description="文件扩展名", default="")
    kb_name = fields.CharField(max_length=50, null=True, description="所属知识库名称", default="")
    document_loader_name = fields.CharField(max_length=50, null=True, description="文档加载器名称", default="")
    text_splitter_name = fields.CharField(max_length=50, null=True, description="文本分割器名称", default="")
    file_version = fields.IntField(null=True, description="文件版本", default=0)
    file_mtime = fields.FloatField(null=True, description="文件修改时间", default=0)
    file_size = fields.IntField(null=True, description="文件大小", default=0)
    custom_docs = fields.BooleanField(null=True, description="是否自定义docs", default=False)
    docs_count = fields.IntField(null=True, description="切分文档数量", default=0)
    create_time = fields.DatetimeField(null=True, description="创建时间", default=datetime.datetime.now)
    uid = fields.IntField(null=True, description="用户id", default=0)
    blob_url = fields.TextField( null=True, description="对象存储地址", default="")
    uuid = fields.CharField(max_length=255, null=True, description="流程是先上传文件，所以需要一个uuid来绑定创建知识库", default="")
    kb_base_id = fields.IntField(null=True, description="知识库id", default=0)
    in_db:int = fields.IntField(default=0,description="在向量库")
    task_id:str = fields.CharField(default=0,description="任务id",max_length=255)


class TKnowledgeBase(Model):
    class Meta:
        table = "t_kb_knowledge_base"
    id = fields.IntField(pk=True, description="主键", auto=True)
    kb_name = fields.CharField(max_length=50, null=True, description="知识库名称", default="")
    kb_info = fields.CharField(max_length=200, null=True, description="知识库简介(用于Agent)", default="")
    vs_type = fields.CharField(max_length=50, null=True, description="向量库类型", default="")
    embed_model = fields.CharField(max_length=50, null=True, description="嵌入模型名称", default="")
    type = fields.CharField(max_length=50, null=True, description="知识库类型", default="chatchat")
    file_count = fields.IntField(null=True, description="文件数量", default=0)
    create_time = fields.DatetimeField(null=True, description="创建时间", default=datetime.datetime.now)
    uid = fields.IntField(null=True, description="用户id", default=0)
    token_id = fields.IntField(null=True, description="用于向量化的token_id", default=0)
    llm_model:str = fields.CharField(max_length=250, null=True, description="llm模型名称", default="")


class TService(Model):
    class Meta:
        table = "t_services"
    id = fields.IntField(pk=True)
    sort_order = fields.IntField()
    service_type = fields.CharField(max_length=255)
    titles = fields.JSONField(null=True)
    tags = fields.JSONField(null=True)
    service_names = fields.JSONField(null=True)
    price_prefix = fields.CharField(null=True, max_length=255)
    input_token = fields.IntField(null=True)
    suffix = fields.JSONField(null=True)
    links = fields.JSONField()
    input_price = fields.FloatField()
    output_price = fields.FloatField()
    old_input_price = fields.FloatField()
    old_output_price = fields.FloatField()
    description = fields.JSONField()
    model_id = fields.IntField(null=True)
    output_size = fields.IntField(null=True)
    is_multimodal = fields.BooleanField(null=True)


class TApisCategory(Model):
    class Meta:
        table = "t_apis_categories"
    id = fields.IntField(pk=True)
    title = fields.JSONField()
    tags = fields.JSONField(null=True)
    description = fields.JSONField(null=True)
    link = fields.JSONField(null=True)


class TApisBrand(Model):
    class Meta:
        table = "t_apis_brands"
    id = fields.IntField(pk=True)
    category = fields.ForeignKeyField("gpt.TApisCategory", related_name="brands", on_delete=fields.CASCADE)
    title = fields.JSONField()
    description = fields.JSONField(null=True)
    search_description = fields.JSONField(null=True)
    tags = fields.JSONField(null=True)
    link = fields.JSONField(null=True)
    img = fields.JSONField(null=True)
    show_model = fields.BooleanField(null=True, default=False)
    api_info = fields.JSONField(null=True)


class TModelParam(Model):
    class Meta:
        table = "t_model_param"
    id = fields.IntField(pk=True)
    model_name = fields.CharField(max_length=50, unique=True)
    support_system_role = fields.BooleanField(default=False)
    support_stop = fields.BooleanField(default=False)
    support_stream = fields.BooleanField(default=False)
    max_context_tokens = fields.IntField(default=0)
    max_output_tokens = fields.IntField(default=8192)
    min_temperature = fields.FloatField(default=0.0)
    max_temperature = fields.FloatField(default=1.0)
    min_top_p = fields.FloatField(default=0.0)
    max_top_p = fields.FloatField(default=1.0)
    min_frequency_penalty = fields.FloatField(default=0.0)
    max_frequency_penalty = fields.FloatField(default=2.0)
    min_presence_penalty = fields.FloatField(default=0.0)
    max_presence_penalty = fields.FloatField(default=2.0)
    support_attachment_type = fields.JSONField(default=["text"])


#nano知识库文件对应milvus的索引表
class TKBFileToMilvus(Model):
    class Meta:
        table = "t_kb_file_to_milvus"

    id = fields.IntField(pk=True, description="主键", auto=True)
    kb_id = fields.IntField(null=True, description="知识库id", default=0)
    kb_name =  fields.CharField(max_length=50, null=True, description="知识库名称", default="")
    file_id = fields.IntField(null=True, description="文件id", default=0)
    file_name = fields.TextField(null=True, description="文件名", default="")
    collection_name = fields.CharField(max_length=50, null=True, description="Milvus集合名称", default="")
    partition_name =  fields.CharField(max_length=50, null=True, description="Milvus分区名称", default="")

class TCustomModels(Model):
    id = fields.IntField(pk=True)
    uid = fields.IntField(default=0)
    token_mapping = fields.ForeignKeyField(
        'gpt.TTokenMapping', 
        related_name='custom_models',
        source_field='token_mapping_id'
    )
    show_name = fields.CharField(max_length=255)
    api_key = fields.CharField(max_length=255)
    base_url = fields.CharField(max_length=255, default='')
    model = fields.CharField(max_length=255, default='')
    description = fields.TextField(null=True)
    max_tokens = fields.IntField(default=1)
    temperature = fields.FloatField(default=0)
    top_p = fields.FloatField(default=0)
    frequency_penalty = fields.FloatField(default=0)
    presence_penalty = fields.FloatField(default=0)
    allow_upload = fields.BooleanField(default=False)
    allow_functions = fields.BooleanField(default=False) 
    allow_deep_thinking = fields.BooleanField(default=False)
    created_on = fields.IntField(default=0)
    updated_on = fields.IntField(default=0)
    deleted_on = fields.IntField(default=0)

    class Meta:
        table = "t_custom_models"
